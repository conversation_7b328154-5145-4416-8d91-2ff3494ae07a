import { LitElement, html, css } from '/node_modules/lit/index.js';
import { databaseService } from './database.js';

class AboutPage extends LitElement {
  static styles = css`
    :host {
      display: block;
    }

    .about-container {
      max-width: var(--container-xl);
      margin: 0 auto;
      padding: var(--spacing-6) var(--spacing-4);
    }

    .hero-section {
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      color: white;
      padding: var(--spacing-16) 0;
      margin: 0 calc(-1 * var(--spacing-4)) var(--spacing-16);
      text-align: center;
      position: relative;
      overflow: hidden;
    }

    .hero-section::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(45deg, rgba(37, 99, 235, 0.1), rgba(16, 185, 129, 0.1));
      opacity: 0.2;
      z-index: 0;
    }

    .hero-content {
      position: relative;
      z-index: 1;
      max-width: var(--container-lg);
      margin: 0 auto;
      padding: 0 var(--spacing-4);
    }

    .hero-title {
      font-size: var(--font-size-4xl);
      font-weight: 700;
      margin-bottom: var(--spacing-6);
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .hero-subtitle {
      font-size: var(--font-size-xl);
      margin-bottom: var(--spacing-8);
      opacity: 0.9;
      line-height: 1.6;
    }

    .content-section {
      margin-bottom: var(--spacing-16);
    }

    .section-title {
      font-size: var(--font-size-2xl);
      font-weight: 700;
      color: var(--gray-800);
      margin-bottom: var(--spacing-6);
      text-align: center;
    }

    .content-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: var(--spacing-8);
      align-items: center;
    }

    .content-text {
      font-size: var(--font-size-base);
      line-height: 1.7;
      color: var(--gray-600);
    }

    .content-text h3 {
      color: var(--gray-800);
      font-size: var(--font-size-lg);
      font-weight: 600;
      margin: var(--spacing-6) 0 var(--spacing-3) 0;
    }

    .content-text ul {
      margin: var(--spacing-4) 0;
      padding-left: var(--spacing-6);
    }

    .content-text li {
      margin-bottom: var(--spacing-2);
    }

    .content-image {
      width: 100%;
      height: 300px;
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      border-radius: var(--radius-xl);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 4rem;
      position: relative;
      overflow: hidden;
    }

    .content-image::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
      transform: translateX(-100%);
      animation: shimmer 3s infinite;
    }

    @keyframes shimmer {
      0% { transform: translateX(-100%); }
      100% { transform: translateX(100%); }
    }

    .features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: var(--spacing-6);
      margin-top: var(--spacing-8);
    }

    .feature-card {
      background: white;
      border-radius: var(--radius-xl);
      padding: var(--spacing-6);
      text-align: center;
      box-shadow: var(--shadow-md);
      transition: all var(--transition-normal);
    }

    .feature-card:hover {
      transform: translateY(-5px);
      box-shadow: var(--shadow-xl);
    }

    .feature-icon {
      width: 80px;
      height: 80px;
      margin: 0 auto var(--spacing-4);
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: var(--font-size-2xl);
    }

    .feature-title {
      font-size: var(--font-size-lg);
      font-weight: 600;
      color: var(--gray-800);
      margin-bottom: var(--spacing-3);
    }

    .feature-description {
      color: var(--gray-600);
      line-height: 1.6;
    }

    .contact-section {
      background: white;
      border-radius: var(--radius-2xl);
      padding: var(--spacing-8);
      box-shadow: var(--shadow-lg);
      margin-top: var(--spacing-16);
    }

    .contact-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: var(--spacing-8);
    }

    .contact-info h3 {
      font-size: var(--font-size-xl);
      font-weight: 700;
      color: var(--gray-800);
      margin-bottom: var(--spacing-4);
    }

    .contact-item {
      display: flex;
      align-items: flex-start;
      gap: var(--spacing-3);
      margin-bottom: var(--spacing-4);
    }

    .contact-icon {
      width: 24px;
      height: 24px;
      background: var(--primary-color);
      border-radius: var(--radius-md);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: var(--font-size-sm);
      flex-shrink: 0;
      margin-top: 2px;
    }

    .contact-details {
      color: var(--gray-600);
      line-height: 1.6;
    }

    .contact-details strong {
      color: var(--gray-800);
      display: block;
      margin-bottom: var(--spacing-1);
    }

    .map-placeholder {
      width: 100%;
      height: 300px;
      background: linear-gradient(135deg, var(--gray-200), var(--gray-300));
      border-radius: var(--radius-lg);
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--gray-500);
      font-size: var(--font-size-lg);
      position: relative;
      overflow: hidden;
    }

    .map-placeholder::before {
      content: '🗺️';
      font-size: 3rem;
      margin-bottom: var(--spacing-2);
    }

    .stats-section {
      background: linear-gradient(135deg, var(--gray-800), var(--gray-700));
      color: white;
      padding: var(--spacing-12) 0;
      margin: var(--spacing-16) calc(-1 * var(--spacing-4));
      border-radius: var(--radius-2xl);
    }

    .stats-container {
      max-width: var(--container-lg);
      margin: 0 auto;
      padding: 0 var(--spacing-4);
      text-align: center;
    }

    .stats-title {
      font-size: var(--font-size-2xl);
      font-weight: 700;
      margin-bottom: var(--spacing-8);
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: var(--spacing-6);
    }

    .stat-item {
      text-align: center;
    }

    .stat-number {
      font-size: var(--font-size-3xl);
      font-weight: 700;
      margin-bottom: var(--spacing-2);
      background: linear-gradient(135deg, var(--primary-light), var(--secondary-color));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .stat-label {
      font-size: var(--font-size-base);
      opacity: 0.9;
    }

    .loading-state {
      text-align: center;
      padding: var(--spacing-12);
    }

    .spinner {
      width: 40px;
      height: 40px;
      border: 4px solid var(--gray-200);
      border-top: 4px solid var(--primary-color);
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto var(--spacing-4);
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    @media (max-width: 768px) {
      .hero-title {
        font-size: var(--font-size-3xl);
      }

      .hero-subtitle {
        font-size: var(--font-size-lg);
      }

      .content-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-6);
      }

      .contact-grid {
        grid-template-columns: 1fr;
      }

      .stats-grid {
        grid-template-columns: repeat(2, 1fr);
      }

      .features-grid {
        grid-template-columns: 1fr;
      }
    }

    @media (max-width: 480px) {
      .stats-grid {
        grid-template-columns: 1fr;
      }
    }
  `;

  static properties = {
    content: { type: Object },
    loading: { type: Boolean }
  };

  constructor() {
    super();
    this.content = null;
    this.loading = true;
    
    this.loadContent();
  }

  async loadContent() {
    try {
      this.loading = true;
      this.content = await databaseService.getContent('about');
    } catch (error) {
      console.error('Ошибка загрузки контента:', error);
    } finally {
      this.loading = false;
    }
  }

  render() {
    if (this.loading) {
      return html`
        <div class="about-container">
          <div class="loading-state">
            <div class="spinner"></div>
            <p>Загрузка информации о студии...</p>
          </div>
        </div>
      `;
    }

    return html`
      <div class="hero-section">
        <div class="hero-content">
          <h1 class="hero-title">О нашей студии</h1>
          <p class="hero-subtitle">
            FitStudio - это современное пространство для занятий фитнесом, 
            где каждый найдет подходящую программу тренировок
          </p>
        </div>
      </div>

      <div class="about-container">
        <section class="content-section">
          <h2 class="section-title">Наша история</h2>
          <div class="content-grid">
            <div class="content-text">
              <p>
                FitStudio была основана в 2018 году с целью создания современного 
                и комфортного пространства для занятий фитнесом. Мы верим, что 
                здоровый образ жизни должен быть доступен каждому.
              </p>
              <p>
                За годы работы мы помогли тысячам людей достичь своих целей в фитнесе, 
                улучшить здоровье и обрести уверенность в себе. Наша команда 
                профессиональных тренеров постоянно совершенствует свои навыки 
                и изучает новые методики тренировок.
              </p>
            </div>
            <div class="content-image">
              🏢
            </div>
          </div>
        </section>

        <section class="content-section">
          <h2 class="section-title">Наши преимущества</h2>
          <div class="features-grid">
            <div class="feature-card">
              <div class="feature-icon">👨‍🏫</div>
              <h3 class="feature-title">Профессиональные тренеры</h3>
              <p class="feature-description">
                Сертифицированные инструкторы с многолетним опытом 
                и постоянным повышением квалификации
              </p>
            </div>
            <div class="feature-card">
              <div class="feature-icon">🏋️‍♀️</div>
              <h3 class="feature-title">Современное оборудование</h3>
              <p class="feature-description">
                Новейшее фитнес-оборудование от ведущих мировых 
                производителей для эффективных тренировок
              </p>
            </div>
            <div class="feature-card">
              <div class="feature-icon">📅</div>
              <h3 class="feature-title">Удобное расписание</h3>
              <p class="feature-description">
                Гибкое расписание занятий с утра до вечера, 
                которое подойдет для любого образа жизни
              </p>
            </div>
            <div class="feature-card">
              <div class="feature-icon">🎯</div>
              <h3 class="feature-title">Индивидуальный подход</h3>
              <p class="feature-description">
                Персональные программы тренировок с учетом 
                ваших особенностей, целей и уровня подготовки
              </p>
            </div>
            <div class="feature-card">
              <div class="feature-icon">🏆</div>
              <h3 class="feature-title">Результативность</h3>
              <p class="feature-description">
                Проверенные методики и постоянный контроль 
                прогресса для достижения ваших целей
              </p>
            </div>
            <div class="feature-card">
              <div class="feature-icon">💚</div>
              <h3 class="feature-title">Дружелюбная атмосфера</h3>
              <p class="feature-description">
                Комфортная и поддерживающая среда, где каждый 
                чувствует себя как дома
              </p>
            </div>
          </div>
        </section>

        <div class="stats-section">
          <div class="stats-container">
            <h2 class="stats-title">FitStudio в цифрах</h2>
            <div class="stats-grid">
              <div class="stat-item">
                <div class="stat-number">2000+</div>
                <div class="stat-label">Довольных клиентов</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">15</div>
                <div class="stat-label">Видов тренировок</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">8</div>
                <div class="stat-label">Профессиональных тренеров</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">500м²</div>
                <div class="stat-label">Площадь студии</div>
              </div>
            </div>
          </div>
        </div>

        <section class="content-section">
          <h2 class="section-title">Наши программы</h2>
          <div class="content-grid">
            <div class="content-image">
              🧘‍♀️
            </div>
            <div class="content-text">
              <h3>Йога и пилатес</h3>
              <p>
                Развитие гибкости, силы и баланса через древние практики йоги 
                и современные методики пилатеса. Подходит для всех уровней подготовки.
              </p>
              
              <h3>Функциональный тренинг</h3>
              <p>
                Интенсивные тренировки с использованием собственного веса и 
                функциональных движений для развития силы, выносливости и координации.
              </p>
              
              <h3>Танцевальные программы</h3>
              <p>
                Энергичные занятия под музыку, которые помогают сжигать калории, 
                улучшать координацию и получать удовольствие от движения.
              </p>
            </div>
          </div>
        </section>

        <div class="contact-section">
          <h2 class="section-title">Контакты и расположение</h2>
          <div class="contact-grid">
            <div class="contact-info">
              <h3>Как нас найти</h3>
              
              <div class="contact-item">
                <div class="contact-icon">📍</div>
                <div class="contact-details">
                  <strong>Адрес</strong>
                  г. Москва, ул. Спортивная, д. 15<br>
                  (5 минут от метро "Спортивная")
                </div>
              </div>
              
              <div class="contact-item">
                <div class="contact-icon">📞</div>
                <div class="contact-details">
                  <strong>Телефон</strong>
                  +7 (495) 123-45-67<br>
                  Ежедневно с 07:00 до 23:00
                </div>
              </div>
              
              <div class="contact-item">
                <div class="contact-icon">✉️</div>
                <div class="contact-details">
                  <strong>Email</strong>
                  <EMAIL><br>
                  Ответим в течение 24 часов
                </div>
              </div>
              
              <div class="contact-item">
                <div class="contact-icon">🕒</div>
                <div class="contact-details">
                  <strong>Режим работы</strong>
                  Понедельник - Пятница: 07:00 - 23:00<br>
                  Суббота - Воскресенье: 09:00 - 21:00
                </div>
              </div>
            </div>
            
            <div class="map-placeholder">
              Интерактивная карта
            </div>
          </div>
        </div>
      </div>
    `;
  }
}

customElements.define('about-page', AboutPage);
