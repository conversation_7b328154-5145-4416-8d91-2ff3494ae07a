{"version": 3, "file": "bundle.js", "sources": ["../database.js", "../app-header.js", "../home-page.js", "../schedule-page.js", "../trainers-page.js", "../about-page.js", "../auth.js", "../login-page.js", "../register-page.js", "../admin-page.js", "../not-found-page.js", "../app-router.js", "../app-footer.js", "../app.js"], "sourcesContent": ["// Сервис для работы с базой данных (эмуляция CouchDB API)\n// PouchDB загружается через script тег в index.html\n\nclass DatabaseService {\n  constructor() {\n    // Локальные базы данных PouchDB\n    this.users = new PouchDB('users');\n    this.trainers = new PouchDB('trainers');\n    this.workouts = new PouchDB('workouts');\n    this.schedule = new PouchDB('schedule');\n    this.bookings = new PouchDB('bookings');\n    this.content = new PouchDB('content');\n    this.slider = new PouchDB('slider');\n    \n    // Эмуляция удаленного CouchDB сервера\n    this.remoteUrl = 'http://localhost:5984';\n    this.isOnline = false;\n    \n    this.initializeData();\n  }\n\n  async initializeData() {\n    try {\n      // Проверяем, есть ли уже данные\n      const usersInfo = await this.users.info();\n      if (usersInfo.doc_count === 0) {\n        await this.seedInitialData();\n      }\n    } catch (error) {\n      console.log('Инициализация данных:', error);\n      await this.seedInitialData();\n    }\n  }\n\n  async seedInitialData() {\n    // Создание начальных данных\n    \n    // Тренеры\n    const trainers = [\n      {\n        _id: 'trainer_1',\n        type: 'trainer',\n        name: 'Анна Петрова',\n        specialization: ['йога', 'пилатес'],\n        bio: 'Сертифицированный инструктор йоги с 8-летним опытом. Специализируется на хатха-йоге и пилатесе.',\n        photo: 'https://via.placeholder.com/300x300/2563eb/ffffff?text=Анна+Петрова',\n        experience: '8 лет'\n      },\n      {\n        _id: 'trainer_2',\n        type: 'trainer',\n        name: 'Михаил Сидоров',\n        specialization: ['функциональный тренинг', 'кроссфит'],\n        bio: 'Мастер спорта по тяжелой атлетике. Специалист по функциональному тренингу и кроссфиту.',\n        photo: 'https://via.placeholder.com/300x300/10b981/ffffff?text=Михаил+Сидоров',\n        experience: '12 лет'\n      },\n      {\n        _id: 'trainer_3',\n        type: 'trainer',\n        name: 'Елена Козлова',\n        specialization: ['танцы', 'аэробика'],\n        bio: 'Хореограф и фитнес-инструктор. Ведет занятия по современным танцам и аэробике.',\n        photo: 'https://via.placeholder.com/300x300/f59e0b/ffffff?text=Елена+Козлова',\n        experience: '6 лет'\n      }\n    ];\n\n    // Типы тренировок\n    const workouts = [\n      {\n        _id: 'workout_1',\n        type: 'workout',\n        name: 'Хатха-йога',\n        description: 'Классическая йога для начинающих и продолжающих. Развитие гибкости, силы и баланса.',\n        duration: 90,\n        max_participants: 12,\n        difficulty: 'beginner'\n      },\n      {\n        _id: 'workout_2',\n        type: 'workout',\n        name: 'Функциональный тренинг',\n        description: 'Интенсивная тренировка с использованием собственного веса и функциональных движений.',\n        duration: 60,\n        max_participants: 8,\n        difficulty: 'intermediate'\n      },\n      {\n        _id: 'workout_3',\n        type: 'workout',\n        name: 'Танцевальная аэробика',\n        description: 'Энергичная тренировка под музыку с элементами современных танцев.',\n        duration: 60,\n        max_participants: 15,\n        difficulty: 'beginner'\n      },\n      {\n        _id: 'workout_4',\n        type: 'workout',\n        name: 'Пилатес',\n        description: 'Система упражнений для укрепления мышц кора, улучшения осанки и гибкости.',\n        duration: 60,\n        max_participants: 10,\n        difficulty: 'beginner'\n      }\n    ];\n\n    // Расписание на неделю\n    const schedule = [];\n    const today = new Date();\n    for (let i = 0; i < 7; i++) {\n      const date = new Date(today);\n      date.setDate(today.getDate() + i);\n      const dateStr = date.toISOString().split('T')[0];\n      \n      // Утренние занятия\n      schedule.push({\n        _id: `schedule_${i}_morning`,\n        type: 'schedule',\n        workout_id: workouts[i % workouts.length]._id,\n        trainer_id: trainers[i % trainers.length]._id,\n        date: dateStr,\n        time: '09:00',\n        duration: workouts[i % workouts.length].duration,\n        max_participants: workouts[i % workouts.length].max_participants,\n        current_participants: Math.floor(Math.random() * 5)\n      });\n      \n      // Вечерние занятия\n      schedule.push({\n        _id: `schedule_${i}_evening`,\n        type: 'schedule',\n        workout_id: workouts[(i + 1) % workouts.length]._id,\n        trainer_id: trainers[(i + 1) % trainers.length]._id,\n        date: dateStr,\n        time: '19:00',\n        duration: workouts[(i + 1) % workouts.length].duration,\n        max_participants: workouts[(i + 1) % workouts.length].max_participants,\n        current_participants: Math.floor(Math.random() * 8)\n      });\n    }\n\n    // Контент страниц\n    const content = [\n      {\n        _id: 'content_home',\n        type: 'content',\n        page: 'home',\n        title: 'Добро пожаловать в FitStudio',\n        subtitle: 'Ваш путь к здоровью и красоте',\n        description: 'Современная фитнес-студия с профессиональными тренерами и разнообразными программами тренировок.',\n        updated_at: new Date().toISOString()\n      }\n    ];\n\n    // Слайды для главной страницы\n    const slider = [\n      {\n        _id: 'slide_1',\n        type: 'slide',\n        title: 'Йога и медитация',\n        subtitle: 'Найдите гармонию тела и души',\n        image: 'https://via.placeholder.com/800x400/2563eb/ffffff?text=Йога+и+медитация',\n        order: 1,\n        active: true\n      },\n      {\n        _id: 'slide_2',\n        type: 'slide',\n        title: 'Функциональный тренинг',\n        subtitle: 'Развивайте силу и выносливость',\n        image: 'https://via.placeholder.com/800x400/10b981/ffffff?text=Функциональный+тренинг',\n        order: 2,\n        active: true\n      }\n    ];\n\n    // Админ пользователь\n    const adminUser = {\n      _id: 'user_admin',\n      type: 'user',\n      email: '<EMAIL>',\n      name: 'Администратор',\n      phone: '+7 (495) 123-45-67',\n      password_hash: '$2a$10$N9qo8uLOickgx2ZMRZoMye.IjPeOXANBjH6dUeAMgMxudsKBC1o16', // password\n      role: 'admin',\n      created_at: new Date().toISOString()\n    };\n\n    // Сохранение данных\n    try {\n      await this.trainers.bulkDocs(trainers);\n      await this.workouts.bulkDocs(workouts);\n      await this.schedule.bulkDocs(schedule);\n      await this.content.bulkDocs(content);\n      await this.slider.bulkDocs(slider);\n      await this.users.put(adminUser);\n      \n      console.log('Начальные данные успешно созданы');\n    } catch (error) {\n      console.error('Ошибка при создании начальных данных:', error);\n    }\n  }\n\n  // Методы для работы с пользователями\n  async createUser(userData) {\n    const user = {\n      _id: `user_${Date.now()}`,\n      type: 'user',\n      ...userData,\n      role: 'user',\n      created_at: new Date().toISOString()\n    };\n    return await this.users.put(user);\n  }\n\n  async getUserByEmail(email) {\n    try {\n      const result = await this.users.find({\n        selector: { email: email, type: 'user' }\n      });\n      return result.docs[0] || null;\n    } catch (error) {\n      return null;\n    }\n  }\n\n  // Методы для работы с расписанием\n  async getSchedule(date = null) {\n    try {\n      let selector = { type: 'schedule' };\n      if (date) {\n        selector.date = date;\n      }\n      \n      const result = await this.schedule.find({\n        selector: selector,\n        sort: [{ date: 'asc' }, { time: 'asc' }]\n      });\n      \n      return result.docs;\n    } catch (error) {\n      console.error('Ошибка получения расписания:', error);\n      return [];\n    }\n  }\n\n  // Методы для работы с записями\n  async createBooking(userId, scheduleId) {\n    // Проверяем доступность места (эмуляция прямого запроса к CouchDB)\n    const scheduleItem = await this.schedule.get(scheduleId);\n    if (scheduleItem.current_participants >= scheduleItem.max_participants) {\n      throw new Error('Нет свободных мест');\n    }\n\n    // Проверяем, не записан ли уже пользователь\n    const existingBooking = await this.bookings.find({\n      selector: {\n        user_id: userId,\n        schedule_id: scheduleId,\n        status: 'active'\n      }\n    });\n\n    if (existingBooking.docs.length > 0) {\n      throw new Error('Вы уже записаны на эту тренировку');\n    }\n\n    // Создаем запись\n    const booking = {\n      _id: `booking_${Date.now()}`,\n      type: 'booking',\n      user_id: userId,\n      schedule_id: scheduleId,\n      status: 'active',\n      booked_at: new Date().toISOString()\n    };\n\n    // Увеличиваем количество участников\n    scheduleItem.current_participants += 1;\n    await this.schedule.put(scheduleItem);\n\n    return await this.bookings.put(booking);\n  }\n\n  async cancelBooking(bookingId, userId) {\n    const booking = await this.bookings.get(bookingId);\n    \n    if (booking.user_id !== userId) {\n      throw new Error('Нет прав для отмены этой записи');\n    }\n\n    // Проверяем время отмены (не позднее чем за час)\n    const scheduleItem = await this.schedule.get(booking.schedule_id);\n    const workoutDateTime = new Date(`${scheduleItem.date}T${scheduleItem.time}`);\n    const now = new Date();\n    const hourBeforeWorkout = new Date(workoutDateTime.getTime() - 60 * 60 * 1000);\n\n    if (now > hourBeforeWorkout) {\n      throw new Error('Отмена возможна не позднее чем за час до начала тренировки');\n    }\n\n    // Отменяем запись\n    booking.status = 'cancelled';\n    booking.cancelled_at = new Date().toISOString();\n\n    // Уменьшаем количество участников\n    scheduleItem.current_participants -= 1;\n    await this.schedule.put(scheduleItem);\n\n    return await this.bookings.put(booking);\n  }\n\n  async getUserBookings(userId) {\n    try {\n      const result = await this.bookings.find({\n        selector: { user_id: userId, type: 'booking' },\n        sort: [{ booked_at: 'desc' }]\n      });\n      return result.docs;\n    } catch (error) {\n      console.error('Ошибка получения записей пользователя:', error);\n      return [];\n    }\n  }\n\n  // Методы для работы с контентом (админ)\n  async updateContent(pageId, contentData) {\n    try {\n      const doc = await this.content.get(`content_${pageId}`);\n      const updatedDoc = {\n        ...doc,\n        ...contentData,\n        updated_at: new Date().toISOString()\n      };\n      return await this.content.put(updatedDoc);\n    } catch (error) {\n      // Если документ не существует, создаем новый\n      const newDoc = {\n        _id: `content_${pageId}`,\n        type: 'content',\n        page: pageId,\n        ...contentData,\n        updated_at: new Date().toISOString()\n      };\n      return await this.content.put(newDoc);\n    }\n  }\n\n  async getContent(pageId) {\n    try {\n      return await this.content.get(`content_${pageId}`);\n    } catch (error) {\n      return null;\n    }\n  }\n\n  // Методы для работы со слайдером\n  async getSlides() {\n    try {\n      const result = await this.slider.find({\n        selector: { type: 'slide', active: true },\n        sort: [{ order: 'asc' }]\n      });\n      return result.docs;\n    } catch (error) {\n      console.error('Ошибка получения слайдов:', error);\n      return [];\n    }\n  }\n\n  async updateSlide(slideId, slideData) {\n    try {\n      const doc = await this.slider.get(slideId);\n      const updatedDoc = {\n        ...doc,\n        ...slideData,\n        updated_at: new Date().toISOString()\n      };\n      return await this.slider.put(updatedDoc);\n    } catch (error) {\n      console.error('Ошибка обновления слайда:', error);\n      throw error;\n    }\n  }\n\n  // Получение данных для отображения\n  async getTrainers() {\n    try {\n      const result = await this.trainers.find({\n        selector: { type: 'trainer' }\n      });\n      return result.docs;\n    } catch (error) {\n      console.error('Ошибка получения тренеров:', error);\n      return [];\n    }\n  }\n\n  async getWorkouts() {\n    try {\n      const result = await this.workouts.find({\n        selector: { type: 'workout' }\n      });\n      return result.docs;\n    } catch (error) {\n      console.error('Ошибка получения тренировок:', error);\n      return [];\n    }\n  }\n\n  async getTrainer(trainerId) {\n    try {\n      return await this.trainers.get(trainerId);\n    } catch (error) {\n      return null;\n    }\n  }\n\n  async getWorkout(workoutId) {\n    try {\n      return await this.workouts.get(workoutId);\n    } catch (error) {\n      return null;\n    }\n  }\n}\n\n// Создаем единственный экземпляр сервиса\nexport const databaseService = new DatabaseService();\n", "import { LitElement, html, css } from '/node_modules/lit/index.js';\n\nclass AppHeader extends LitElement {\n  static styles = css`\n    :host {\n      display: block;\n      background: white;\n      box-shadow: var(--shadow-sm);\n      position: sticky;\n      top: 0;\n      z-index: 100;\n    }\n\n    .header {\n      max-width: var(--container-xl);\n      margin: 0 auto;\n      padding: 0 var(--spacing-4);\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n      height: 70px;\n    }\n\n    .logo {\n      display: flex;\n      align-items: center;\n      gap: var(--spacing-3);\n      font-size: var(--font-size-xl);\n      font-weight: 700;\n      color: var(--primary-color);\n      text-decoration: none;\n    }\n\n    .logo-icon {\n      width: 40px;\n      height: 40px;\n      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));\n      border-radius: var(--radius-lg);\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      color: white;\n      font-weight: bold;\n      font-size: var(--font-size-lg);\n    }\n\n    .nav {\n      display: flex;\n      align-items: center;\n      gap: var(--spacing-6);\n    }\n\n    .nav-links {\n      display: flex;\n      align-items: center;\n      gap: var(--spacing-6);\n      list-style: none;\n      margin: 0;\n      padding: 0;\n    }\n\n    .nav-link {\n      color: var(--gray-600);\n      text-decoration: none;\n      font-weight: 500;\n      padding: var(--spacing-2) var(--spacing-3);\n      border-radius: var(--radius-md);\n      transition: all var(--transition-fast);\n    }\n\n    .nav-link:hover {\n      color: var(--primary-color);\n      background-color: var(--gray-50);\n    }\n\n    .nav-link.active {\n      color: var(--primary-color);\n      background-color: var(--primary-color);\n      background-color: rgb(37 99 235 / 0.1);\n    }\n\n    .user-menu {\n      display: flex;\n      align-items: center;\n      gap: var(--spacing-4);\n    }\n\n    .user-info {\n      display: flex;\n      align-items: center;\n      gap: var(--spacing-3);\n      color: var(--gray-700);\n    }\n\n    .user-avatar {\n      width: 36px;\n      height: 36px;\n      border-radius: 50%;\n      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      color: white;\n      font-weight: 600;\n      font-size: var(--font-size-sm);\n    }\n\n    .mobile-menu-btn {\n      display: none;\n      background: none;\n      border: none;\n      font-size: var(--font-size-xl);\n      color: var(--gray-600);\n      cursor: pointer;\n      padding: var(--spacing-2);\n    }\n\n    .mobile-nav {\n      display: none;\n      position: absolute;\n      top: 100%;\n      left: 0;\n      right: 0;\n      background: white;\n      box-shadow: var(--shadow-lg);\n      padding: var(--spacing-4);\n    }\n\n    .mobile-nav.open {\n      display: block;\n    }\n\n    .mobile-nav-links {\n      list-style: none;\n      margin: 0;\n      padding: 0;\n    }\n\n    .mobile-nav-links li {\n      margin-bottom: var(--spacing-2);\n    }\n\n    .mobile-nav-link {\n      display: block;\n      padding: var(--spacing-3);\n      color: var(--gray-600);\n      text-decoration: none;\n      border-radius: var(--radius-md);\n      transition: all var(--transition-fast);\n    }\n\n    .mobile-nav-link:hover {\n      background-color: var(--gray-50);\n      color: var(--primary-color);\n    }\n\n    @media (max-width: 768px) {\n      .nav-links {\n        display: none;\n      }\n\n      .mobile-menu-btn {\n        display: block;\n      }\n\n      .user-info span {\n        display: none;\n      }\n    }\n  `;\n\n  static properties = {\n    currentUser: { type: Object },\n    mobileMenuOpen: { type: Boolean }\n  };\n\n  constructor() {\n    super();\n    this.currentUser = null;\n    this.mobileMenuOpen = false;\n  }\n\n  toggleMobileMenu() {\n    this.mobileMenuOpen = !this.mobileMenuOpen;\n  }\n\n  handleLogout() {\n    this.dispatchEvent(new CustomEvent('user-logout'));\n  }\n\n  getUserInitials(name) {\n    if (!name) return 'U';\n    return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);\n  }\n\n  render() {\n    return html`\n      <header class=\"header\">\n        <a href=\"/\" class=\"logo\">\n          <div class=\"logo-icon\">FS</div>\n          <span>FitStudio</span>\n        </a>\n\n        <nav class=\"nav\">\n          <ul class=\"nav-links\">\n            <li><a href=\"/\" class=\"nav-link\">Главная</a></li>\n            <li><a href=\"/schedule\" class=\"nav-link\">Расписание</a></li>\n            <li><a href=\"/trainers\" class=\"nav-link\">Тренеры</a></li>\n            <li><a href=\"/about\" class=\"nav-link\">О студии</a></li>\n            ${this.currentUser?.role === 'admin' ? html`\n              <li><a href=\"/admin\" class=\"nav-link\">Админ</a></li>\n            ` : ''}\n          </ul>\n\n          <div class=\"user-menu\">\n            ${this.currentUser ? html`\n              <div class=\"user-info\">\n                <div class=\"user-avatar\">\n                  ${this.getUserInitials(this.currentUser.name)}\n                </div>\n                <span>${this.currentUser.name}</span>\n              </div>\n              <button class=\"btn btn-ghost btn-sm\" @click=${this.handleLogout}>\n                Выйти\n              </button>\n            ` : html`\n              <a href=\"/login\" class=\"btn btn-outline btn-sm\">Войти</a>\n              <a href=\"/register\" class=\"btn btn-primary btn-sm\">Регистрация</a>\n            `}\n          </div>\n\n          <button class=\"mobile-menu-btn\" @click=${this.toggleMobileMenu}>\n            ☰\n          </button>\n        </nav>\n\n        <div class=\"mobile-nav ${this.mobileMenuOpen ? 'open' : ''}\">\n          <ul class=\"mobile-nav-links\">\n            <li><a href=\"/\" class=\"mobile-nav-link\" @click=${() => this.mobileMenuOpen = false}>Главная</a></li>\n            <li><a href=\"/schedule\" class=\"mobile-nav-link\" @click=${() => this.mobileMenuOpen = false}>Расписание</a></li>\n            <li><a href=\"/trainers\" class=\"mobile-nav-link\" @click=${() => this.mobileMenuOpen = false}>Тренеры</a></li>\n            <li><a href=\"/about\" class=\"mobile-nav-link\" @click=${() => this.mobileMenuOpen = false}>О студии</a></li>\n            ${this.currentUser?.role === 'admin' ? html`\n              <li><a href=\"/admin\" class=\"mobile-nav-link\" @click=${() => this.mobileMenuOpen = false}>Админ</a></li>\n            ` : ''}\n            ${!this.currentUser ? html`\n              <li><a href=\"/login\" class=\"mobile-nav-link\" @click=${() => this.mobileMenuOpen = false}>Войти</a></li>\n              <li><a href=\"/register\" class=\"mobile-nav-link\" @click=${() => this.mobileMenuOpen = false}>Регистрация</a></li>\n            ` : html`\n              <li>\n                <button class=\"mobile-nav-link\" style=\"width: 100%; text-align: left; background: none; border: none; cursor: pointer;\" \n                        @click=${() => { this.handleLogout(); this.mobileMenuOpen = false; }}>\n                  Выйти\n                </button>\n              </li>\n            `}\n          </ul>\n        </div>\n      </header>\n    `;\n  }\n}\n\ncustomElements.define('app-header', AppHeader);\n", "import { LitElement, html, css } from '/node_modules/lit/index.js';\nimport { databaseService } from './database.js';\n\nclass HomePage extends LitElement {\n  static styles = css`\n    :host {\n      display: block;\n    }\n\n    .hero {\n      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));\n      color: white;\n      padding: var(--spacing-20) 0;\n      text-align: center;\n      position: relative;\n      overflow: hidden;\n    }\n\n    .hero::before {\n      content: '';\n      position: absolute;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      background: linear-gradient(45deg, rgba(37, 99, 235, 0.1), rgba(16, 185, 129, 0.1));\n      opacity: 0.2;\n      z-index: 0;\n    }\n\n    .hero-content {\n      position: relative;\n      z-index: 1;\n      max-width: var(--container-lg);\n      margin: 0 auto;\n      padding: 0 var(--spacing-4);\n    }\n\n    .hero h1 {\n      font-size: var(--font-size-4xl);\n      font-weight: 700;\n      margin-bottom: var(--spacing-6);\n      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\n    }\n\n    .hero p {\n      font-size: var(--font-size-xl);\n      margin-bottom: var(--spacing-8);\n      opacity: 0.9;\n    }\n\n    .hero-buttons {\n      display: flex;\n      gap: var(--spacing-4);\n      justify-content: center;\n      flex-wrap: wrap;\n    }\n\n    .hero-buttons .btn {\n      padding: var(--spacing-4) var(--spacing-8);\n      font-size: var(--font-size-lg);\n      font-weight: 600;\n    }\n\n    .btn-white {\n      background-color: white;\n      color: var(--primary-color);\n      border: 2px solid white;\n    }\n\n    .btn-white:hover {\n      background-color: transparent;\n      color: white;\n    }\n\n    .slider {\n      margin: var(--spacing-16) 0;\n    }\n\n    .slider-container {\n      max-width: var(--container-xl);\n      margin: 0 auto;\n      padding: 0 var(--spacing-4);\n    }\n\n    .slider-title {\n      text-align: center;\n      margin-bottom: var(--spacing-8);\n    }\n\n    .slides {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n      gap: var(--spacing-6);\n    }\n\n    .slide {\n      background: white;\n      border-radius: var(--radius-xl);\n      overflow: hidden;\n      box-shadow: var(--shadow-lg);\n      transition: transform var(--transition-normal);\n    }\n\n    .slide:hover {\n      transform: translateY(-5px);\n    }\n\n    .slide-image {\n      width: 100%;\n      height: 200px;\n      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      color: white;\n      font-size: var(--font-size-4xl);\n    }\n\n    .slide-content {\n      padding: var(--spacing-6);\n    }\n\n    .slide h3 {\n      font-size: var(--font-size-xl);\n      margin-bottom: var(--spacing-3);\n      color: var(--gray-800);\n    }\n\n    .slide p {\n      color: var(--gray-600);\n      margin-bottom: 0;\n    }\n\n    .features {\n      background-color: white;\n      padding: var(--spacing-16) 0;\n    }\n\n    .features-container {\n      max-width: var(--container-xl);\n      margin: 0 auto;\n      padding: 0 var(--spacing-4);\n    }\n\n    .features-title {\n      text-align: center;\n      margin-bottom: var(--spacing-12);\n    }\n\n    .features-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n      gap: var(--spacing-8);\n    }\n\n    .feature {\n      text-align: center;\n      padding: var(--spacing-6);\n    }\n\n    .feature-icon {\n      width: 80px;\n      height: 80px;\n      margin: 0 auto var(--spacing-4);\n      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));\n      border-radius: 50%;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      color: white;\n      font-size: var(--font-size-2xl);\n    }\n\n    .feature h3 {\n      font-size: var(--font-size-xl);\n      margin-bottom: var(--spacing-3);\n      color: var(--gray-800);\n    }\n\n    .feature p {\n      color: var(--gray-600);\n      margin-bottom: 0;\n    }\n\n    .cta {\n      background: linear-gradient(135deg, var(--gray-800), var(--gray-700));\n      color: white;\n      padding: var(--spacing-16) 0;\n      text-align: center;\n    }\n\n    .cta-container {\n      max-width: var(--container-lg);\n      margin: 0 auto;\n      padding: 0 var(--spacing-4);\n    }\n\n    .cta h2 {\n      font-size: var(--font-size-3xl);\n      margin-bottom: var(--spacing-4);\n    }\n\n    .cta p {\n      font-size: var(--font-size-lg);\n      margin-bottom: var(--spacing-8);\n      opacity: 0.9;\n    }\n\n    @media (max-width: 768px) {\n      .hero h1 {\n        font-size: var(--font-size-3xl);\n      }\n\n      .hero p {\n        font-size: var(--font-size-lg);\n      }\n\n      .hero-buttons {\n        flex-direction: column;\n        align-items: center;\n      }\n\n      .hero-buttons .btn {\n        width: 100%;\n        max-width: 300px;\n      }\n\n      .slides {\n        grid-template-columns: 1fr;\n      }\n\n      .features-grid {\n        grid-template-columns: 1fr;\n      }\n    }\n  `;\n\n  static properties = {\n    currentUser: { type: Object },\n    slides: { type: Array },\n    content: { type: Object }\n  };\n\n  constructor() {\n    super();\n    this.currentUser = null;\n    this.slides = [];\n    this.content = null;\n    this.loadData();\n  }\n\n  async loadData() {\n    try {\n      // Загружаем слайды\n      this.slides = await databaseService.getSlides();\n      \n      // Загружаем контент главной страницы\n      this.content = await databaseService.getContent('home');\n      \n      this.requestUpdate();\n    } catch (error) {\n      console.error('Ошибка загрузки данных:', error);\n    }\n  }\n\n  render() {\n    return html`\n      <div class=\"hero\">\n        <div class=\"hero-content\">\n          <h1>${this.content?.title || 'Добро пожаловать в FitStudio'}</h1>\n          <p>${this.content?.subtitle || 'Ваш путь к здоровью и красоте'}</p>\n          <div class=\"hero-buttons\">\n            ${this.currentUser ? html`\n              <a href=\"/schedule\" class=\"btn btn-white\">Записаться на тренировку</a>\n            ` : html`\n              <a href=\"/register\" class=\"btn btn-white\">Начать заниматься</a>\n              <a href=\"/schedule\" class=\"btn btn-outline\" style=\"border-color: white; color: white;\">Посмотреть расписание</a>\n            `}\n          </div>\n        </div>\n      </div>\n\n      ${this.slides.length > 0 ? html`\n        <section class=\"slider\">\n          <div class=\"slider-container\">\n            <h2 class=\"slider-title\">Наши программы</h2>\n            <div class=\"slides\">\n              ${this.slides.map(slide => html`\n                <div class=\"slide\">\n                  <div class=\"slide-image\">\n                    🏃‍♀️\n                  </div>\n                  <div class=\"slide-content\">\n                    <h3>${slide.title}</h3>\n                    <p>${slide.subtitle}</p>\n                  </div>\n                </div>\n              `)}\n            </div>\n          </div>\n        </section>\n      ` : ''}\n\n      <section class=\"features\">\n        <div class=\"features-container\">\n          <h2 class=\"features-title\">Почему выбирают нас</h2>\n          <div class=\"features-grid\">\n            <div class=\"feature\">\n              <div class=\"feature-icon\">👨‍🏫</div>\n              <h3>Профессиональные тренеры</h3>\n              <p>Сертифицированные инструкторы с многолетним опытом помогут достичь ваших целей</p>\n            </div>\n            <div class=\"feature\">\n              <div class=\"feature-icon\">🏋️‍♀️</div>\n              <h3>Современное оборудование</h3>\n              <p>Новейшее фитнес-оборудование от ведущих мировых производителей</p>\n            </div>\n            <div class=\"feature\">\n              <div class=\"feature-icon\">📅</div>\n              <h3>Удобное расписание</h3>\n              <p>Гибкое расписание занятий, которое подойдет для любого образа жизни</p>\n            </div>\n            <div class=\"feature\">\n              <div class=\"feature-icon\">🎯</div>\n              <h3>Индивидуальный подход</h3>\n              <p>Персональные программы тренировок с учетом ваших особенностей и целей</p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <section class=\"cta\">\n        <div class=\"cta-container\">\n          <h2>Готовы начать свой путь к здоровью?</h2>\n          <p>Присоединяйтесь к нашему сообществу и откройте для себя мир фитнеса</p>\n          ${this.currentUser ? html`\n            <a href=\"/schedule\" class=\"btn btn-primary btn-lg\">Записаться на тренировку</a>\n          ` : html`\n            <a href=\"/register\" class=\"btn btn-primary btn-lg\">Зарегистрироваться сейчас</a>\n          `}\n        </div>\n      </section>\n    `;\n  }\n}\n\ncustomElements.define('home-page', HomePage);\n", "import { LitElement, html, css } from '/node_modules/lit/index.js';\nimport { databaseService } from './database.js';\n\nclass SchedulePage extends LitElement {\n  static styles = css`\n    :host {\n      display: block;\n    }\n\n    .schedule-container {\n      max-width: var(--container-xl);\n      margin: 0 auto;\n      padding: var(--spacing-6) var(--spacing-4);\n    }\n\n    .schedule-header {\n      text-align: center;\n      margin-bottom: var(--spacing-8);\n    }\n\n    .schedule-title {\n      font-size: var(--font-size-3xl);\n      font-weight: 700;\n      color: var(--gray-800);\n      margin-bottom: var(--spacing-4);\n    }\n\n    .schedule-subtitle {\n      font-size: var(--font-size-lg);\n      color: var(--gray-600);\n      max-width: 600px;\n      margin: 0 auto;\n    }\n\n    .schedule-filters {\n      background: white;\n      border-radius: var(--radius-xl);\n      box-shadow: var(--shadow-sm);\n      padding: var(--spacing-6);\n      margin-bottom: var(--spacing-8);\n      display: flex;\n      gap: var(--spacing-4);\n      align-items: center;\n      flex-wrap: wrap;\n    }\n\n    .filter-group {\n      display: flex;\n      flex-direction: column;\n      gap: var(--spacing-2);\n    }\n\n    .filter-label {\n      font-weight: 500;\n      color: var(--gray-700);\n      font-size: var(--font-size-sm);\n    }\n\n    .filter-select {\n      padding: var(--spacing-2) var(--spacing-3);\n      border: 2px solid var(--gray-200);\n      border-radius: var(--radius-md);\n      font-size: var(--font-size-sm);\n      background: white;\n      cursor: pointer;\n    }\n\n    .filter-select:focus {\n      outline: none;\n      border-color: var(--primary-color);\n    }\n\n    .date-navigation {\n      display: flex;\n      align-items: center;\n      gap: var(--spacing-4);\n      margin-left: auto;\n    }\n\n    .date-nav-btn {\n      background: none;\n      border: 2px solid var(--gray-200);\n      border-radius: var(--radius-md);\n      padding: var(--spacing-2);\n      cursor: pointer;\n      color: var(--gray-600);\n      transition: all var(--transition-fast);\n    }\n\n    .date-nav-btn:hover {\n      border-color: var(--primary-color);\n      color: var(--primary-color);\n    }\n\n    .current-date {\n      font-weight: 600;\n      color: var(--gray-800);\n      min-width: 120px;\n      text-align: center;\n    }\n\n    .schedule-grid {\n      display: grid;\n      gap: var(--spacing-6);\n    }\n\n    .day-section {\n      background: white;\n      border-radius: var(--radius-xl);\n      box-shadow: var(--shadow-sm);\n      overflow: hidden;\n    }\n\n    .day-header {\n      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));\n      color: white;\n      padding: var(--spacing-4) var(--spacing-6);\n      text-align: center;\n    }\n\n    .day-title {\n      font-size: var(--font-size-lg);\n      font-weight: 600;\n      margin-bottom: var(--spacing-1);\n    }\n\n    .day-date {\n      font-size: var(--font-size-sm);\n      opacity: 0.9;\n    }\n\n    .workouts-list {\n      padding: var(--spacing-6);\n    }\n\n    .workout-card {\n      border: 2px solid var(--gray-100);\n      border-radius: var(--radius-lg);\n      padding: var(--spacing-4);\n      margin-bottom: var(--spacing-4);\n      transition: all var(--transition-fast);\n      cursor: pointer;\n    }\n\n    .workout-card:hover {\n      border-color: var(--primary-color);\n      box-shadow: var(--shadow-md);\n      transform: translateY(-2px);\n    }\n\n    .workout-card:last-child {\n      margin-bottom: 0;\n    }\n\n    .workout-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: flex-start;\n      margin-bottom: var(--spacing-3);\n    }\n\n    .workout-info h3 {\n      font-size: var(--font-size-lg);\n      font-weight: 600;\n      color: var(--gray-800);\n      margin-bottom: var(--spacing-1);\n    }\n\n    .workout-time {\n      font-size: var(--font-size-sm);\n      color: var(--gray-600);\n      display: flex;\n      align-items: center;\n      gap: var(--spacing-1);\n    }\n\n    .workout-status {\n      display: flex;\n      flex-direction: column;\n      align-items: flex-end;\n      gap: var(--spacing-2);\n    }\n\n    .participants-info {\n      font-size: var(--font-size-sm);\n      color: var(--gray-600);\n      text-align: right;\n    }\n\n    .participants-count {\n      font-weight: 600;\n      color: var(--primary-color);\n    }\n\n    .workout-details {\n      display: grid;\n      grid-template-columns: 1fr auto;\n      gap: var(--spacing-4);\n      align-items: center;\n    }\n\n    .workout-meta {\n      display: flex;\n      gap: var(--spacing-4);\n      font-size: var(--font-size-sm);\n      color: var(--gray-600);\n    }\n\n    .meta-item {\n      display: flex;\n      align-items: center;\n      gap: var(--spacing-1);\n    }\n\n    .difficulty-badge {\n      padding: var(--spacing-1) var(--spacing-2);\n      border-radius: var(--radius-sm);\n      font-size: var(--font-size-xs);\n      font-weight: 500;\n      text-transform: uppercase;\n    }\n\n    .difficulty-beginner {\n      background-color: rgb(34 197 94 / 0.1);\n      color: var(--success-color);\n    }\n\n    .difficulty-intermediate {\n      background-color: rgb(251 191 36 / 0.1);\n      color: var(--warning-color);\n    }\n\n    .difficulty-advanced {\n      background-color: rgb(239 68 68 / 0.1);\n      color: var(--error-color);\n    }\n\n    .book-btn {\n      padding: var(--spacing-2) var(--spacing-4);\n      background-color: var(--primary-color);\n      color: white;\n      border: none;\n      border-radius: var(--radius-md);\n      font-size: var(--font-size-sm);\n      font-weight: 500;\n      cursor: pointer;\n      transition: all var(--transition-fast);\n    }\n\n    .book-btn:hover:not(:disabled) {\n      background-color: var(--primary-dark);\n      transform: translateY(-1px);\n    }\n\n    .book-btn:disabled {\n      background-color: var(--gray-400);\n      cursor: not-allowed;\n      transform: none;\n    }\n\n    .book-btn.booked {\n      background-color: var(--success-color);\n    }\n\n    .book-btn.full {\n      background-color: var(--error-color);\n    }\n\n    .empty-state {\n      text-align: center;\n      padding: var(--spacing-12);\n      color: var(--gray-500);\n    }\n\n    .empty-state-icon {\n      font-size: 4rem;\n      margin-bottom: var(--spacing-4);\n    }\n\n    .loading-state {\n      text-align: center;\n      padding: var(--spacing-12);\n    }\n\n    .spinner {\n      width: 40px;\n      height: 40px;\n      border: 4px solid var(--gray-200);\n      border-top: 4px solid var(--primary-color);\n      border-radius: 50%;\n      animation: spin 1s linear infinite;\n      margin: 0 auto var(--spacing-4);\n    }\n\n    @keyframes spin {\n      0% { transform: rotate(0deg); }\n      100% { transform: rotate(360deg); }\n    }\n\n    .notification {\n      position: fixed;\n      top: var(--spacing-4);\n      right: var(--spacing-4);\n      padding: var(--spacing-4) var(--spacing-6);\n      border-radius: var(--radius-lg);\n      color: white;\n      font-weight: 500;\n      z-index: 1000;\n      animation: slideIn 0.3s ease-out;\n    }\n\n    .notification.success {\n      background-color: var(--success-color);\n    }\n\n    .notification.error {\n      background-color: var(--error-color);\n    }\n\n    @keyframes slideIn {\n      from {\n        transform: translateX(100%);\n        opacity: 0;\n      }\n      to {\n        transform: translateX(0);\n        opacity: 1;\n      }\n    }\n\n    @media (max-width: 768px) {\n      .schedule-filters {\n        flex-direction: column;\n        align-items: stretch;\n      }\n\n      .date-navigation {\n        margin-left: 0;\n        justify-content: center;\n      }\n\n      .workout-header {\n        flex-direction: column;\n        gap: var(--spacing-2);\n      }\n\n      .workout-details {\n        grid-template-columns: 1fr;\n        gap: var(--spacing-3);\n      }\n\n      .workout-meta {\n        flex-wrap: wrap;\n      }\n    }\n  `;\n\n  static properties = {\n    currentUser: { type: Object },\n    schedule: { type: Array },\n    workouts: { type: Array },\n    trainers: { type: Array },\n    userBookings: { type: Array },\n    loading: { type: Boolean },\n    selectedDate: { type: String },\n    filterWorkout: { type: String },\n    filterTrainer: { type: String }\n  };\n\n  constructor() {\n    super();\n    this.currentUser = null;\n    this.schedule = [];\n    this.workouts = [];\n    this.trainers = [];\n    this.userBookings = [];\n    this.loading = true;\n    this.selectedDate = new Date().toISOString().split('T')[0];\n    this.filterWorkout = '';\n    this.filterTrainer = '';\n    \n    this.loadData();\n  }\n\n  async loadData() {\n    try {\n      this.loading = true;\n      \n      // Загружаем все необходимые данные\n      const [schedule, workouts, trainers] = await Promise.all([\n        databaseService.getSchedule(),\n        databaseService.getWorkouts(),\n        databaseService.getTrainers()\n      ]);\n      \n      this.schedule = schedule;\n      this.workouts = workouts;\n      this.trainers = trainers;\n      \n      // Загружаем записи пользователя, если он авторизован\n      if (this.currentUser) {\n        this.userBookings = await databaseService.getUserBookings(this.currentUser._id);\n      }\n      \n    } catch (error) {\n      console.error('Ошибка загрузки данных:', error);\n      this.showNotification('Ошибка загрузки данных', 'error');\n    } finally {\n      this.loading = false;\n    }\n  }\n\n  getWorkoutById(workoutId) {\n    return this.workouts.find(w => w._id === workoutId);\n  }\n\n  getTrainerById(trainerId) {\n    return this.trainers.find(t => t._id === trainerId);\n  }\n\n  isUserBooked(scheduleId) {\n    return this.userBookings.some(booking => \n      booking.schedule_id === scheduleId && booking.status === 'active'\n    );\n  }\n\n  getUserBooking(scheduleId) {\n    return this.userBookings.find(booking => \n      booking.schedule_id === scheduleId && booking.status === 'active'\n    );\n  }\n\n  canCancelBooking(scheduleItem) {\n    const workoutDateTime = new Date(`${scheduleItem.date}T${scheduleItem.time}`);\n    const now = new Date();\n    const hourBeforeWorkout = new Date(workoutDateTime.getTime() - 60 * 60 * 1000);\n    return now <= hourBeforeWorkout;\n  }\n\n  async handleBooking(scheduleItem) {\n    if (!this.currentUser) {\n      this.showNotification('Необходимо войти в систему для записи на тренировки', 'error');\n      return;\n    }\n\n    try {\n      const isBooked = this.isUserBooked(scheduleItem._id);\n      \n      if (isBooked) {\n        // Отмена записи\n        if (!this.canCancelBooking(scheduleItem)) {\n          this.showNotification('Отмена возможна не позднее чем за час до начала тренировки', 'error');\n          return;\n        }\n        \n        const booking = this.getUserBooking(scheduleItem._id);\n        await databaseService.cancelBooking(booking._id, this.currentUser._id);\n        this.showNotification('Запись отменена', 'success');\n      } else {\n        // Новая запись\n        if (scheduleItem.current_participants >= scheduleItem.max_participants) {\n          this.showNotification('Нет свободных мест', 'error');\n          return;\n        }\n        \n        await databaseService.createBooking(this.currentUser._id, scheduleItem._id);\n        this.showNotification('Вы успешно записались на тренировку', 'success');\n      }\n      \n      // Обновляем данные\n      await this.loadData();\n      \n    } catch (error) {\n      console.error('Ошибка при записи/отмене:', error);\n      this.showNotification(error.message || 'Произошла ошибка', 'error');\n    }\n  }\n\n  showNotification(message, type = 'info') {\n    const notification = document.createElement('div');\n    notification.className = `notification ${type}`;\n    notification.textContent = message;\n    document.body.appendChild(notification);\n    \n    setTimeout(() => {\n      notification.remove();\n    }, 5000);\n  }\n\n  changeDate(direction) {\n    const currentDate = new Date(this.selectedDate);\n    currentDate.setDate(currentDate.getDate() + direction);\n    this.selectedDate = currentDate.toISOString().split('T')[0];\n  }\n\n  formatDate(dateString) {\n    const date = new Date(dateString);\n    const options = { \n      weekday: 'long', \n      year: 'numeric', \n      month: 'long', \n      day: 'numeric' \n    };\n    return date.toLocaleDateString('ru-RU', options);\n  }\n\n  formatTime(timeString) {\n    return timeString.slice(0, 5);\n  }\n\n  getDifficultyText(difficulty) {\n    const map = {\n      'beginner': 'Начинающий',\n      'intermediate': 'Средний',\n      'advanced': 'Продвинутый'\n    };\n    return map[difficulty] || difficulty;\n  }\n\n  getFilteredSchedule() {\n    let filtered = this.schedule;\n    \n    // Фильтр по дате\n    filtered = filtered.filter(item => item.date === this.selectedDate);\n    \n    // Фильтр по типу тренировки\n    if (this.filterWorkout) {\n      filtered = filtered.filter(item => item.workout_id === this.filterWorkout);\n    }\n    \n    // Фильтр по тренеру\n    if (this.filterTrainer) {\n      filtered = filtered.filter(item => item.trainer_id === this.filterTrainer);\n    }\n    \n    return filtered;\n  }\n\n  render() {\n    if (this.loading) {\n      return html`\n        <div class=\"schedule-container\">\n          <div class=\"loading-state\">\n            <div class=\"spinner\"></div>\n            <p>Загрузка расписания...</p>\n          </div>\n        </div>\n      `;\n    }\n\n    const filteredSchedule = this.getFilteredSchedule();\n\n    return html`\n      <div class=\"schedule-container\">\n        <div class=\"schedule-header\">\n          <h1 class=\"schedule-title\">Расписание тренировок</h1>\n          <p class=\"schedule-subtitle\">\n            Выберите подходящую тренировку и запишитесь онлайн. \n            Отмена возможна не позднее чем за час до начала занятия.\n          </p>\n        </div>\n\n        <div class=\"schedule-filters\">\n          <div class=\"filter-group\">\n            <label class=\"filter-label\">Тип тренировки</label>\n            <select class=\"filter-select\" .value=${this.filterWorkout} \n                    @change=${(e) => this.filterWorkout = e.target.value}>\n              <option value=\"\">Все тренировки</option>\n              ${this.workouts.map(workout => html`\n                <option value=${workout._id}>${workout.name}</option>\n              `)}\n            </select>\n          </div>\n\n          <div class=\"filter-group\">\n            <label class=\"filter-label\">Тренер</label>\n            <select class=\"filter-select\" .value=${this.filterTrainer} \n                    @change=${(e) => this.filterTrainer = e.target.value}>\n              <option value=\"\">Все тренеры</option>\n              ${this.trainers.map(trainer => html`\n                <option value=${trainer._id}>${trainer.name}</option>\n              `)}\n            </select>\n          </div>\n\n          <div class=\"date-navigation\">\n            <button class=\"date-nav-btn\" @click=${() => this.changeDate(-1)}>\n              ←\n            </button>\n            <div class=\"current-date\">\n              ${this.formatDate(this.selectedDate)}\n            </div>\n            <button class=\"date-nav-btn\" @click=${() => this.changeDate(1)}>\n              →\n            </button>\n          </div>\n        </div>\n\n        <div class=\"schedule-grid\">\n          ${filteredSchedule.length > 0 ? html`\n            <div class=\"day-section\">\n              <div class=\"day-header\">\n                <div class=\"day-title\">${this.formatDate(this.selectedDate)}</div>\n                <div class=\"day-date\">${filteredSchedule.length} тренировок</div>\n              </div>\n              <div class=\"workouts-list\">\n                ${filteredSchedule.map(scheduleItem => {\n                  const workout = this.getWorkoutById(scheduleItem.workout_id);\n                  const trainer = this.getTrainerById(scheduleItem.trainer_id);\n                  const isBooked = this.isUserBooked(scheduleItem._id);\n                  const isFull = scheduleItem.current_participants >= scheduleItem.max_participants;\n                  const canCancel = isBooked && this.canCancelBooking(scheduleItem);\n                  \n                  return html`\n                    <div class=\"workout-card\">\n                      <div class=\"workout-header\">\n                        <div class=\"workout-info\">\n                          <h3>${workout?.name || 'Неизвестная тренировка'}</h3>\n                          <div class=\"workout-time\">\n                            🕐 ${this.formatTime(scheduleItem.time)} - ${this.formatTime(\n                              new Date(new Date(`2000-01-01T${scheduleItem.time}`).getTime() + scheduleItem.duration * 60000)\n                                .toTimeString().slice(0, 5)\n                            )}\n                          </div>\n                        </div>\n                        <div class=\"workout-status\">\n                          <div class=\"participants-info\">\n                            <span class=\"participants-count\">${scheduleItem.current_participants}</span>\n                            /${scheduleItem.max_participants} мест\n                          </div>\n                        </div>\n                      </div>\n                      \n                      <div class=\"workout-details\">\n                        <div class=\"workout-meta\">\n                          <div class=\"meta-item\">\n                            👨‍🏫 ${trainer?.name || 'Неизвестный тренер'}\n                          </div>\n                          <div class=\"meta-item\">\n                            ⏱️ ${scheduleItem.duration} мин\n                          </div>\n                          <div class=\"meta-item\">\n                            <span class=\"difficulty-badge difficulty-${workout?.difficulty}\">\n                              ${this.getDifficultyText(workout?.difficulty)}\n                            </span>\n                          </div>\n                        </div>\n                        \n                        ${this.currentUser ? html`\n                          <button \n                            class=\"book-btn ${isBooked ? 'booked' : ''} ${isFull && !isBooked ? 'full' : ''}\"\n                            ?disabled=${isFull && !isBooked}\n                            @click=${() => this.handleBooking(scheduleItem)}\n                          >\n                            ${isBooked \n                              ? (canCancel ? 'Отменить запись' : 'Записан') \n                              : (isFull ? 'Нет мест' : 'Записаться')\n                            }\n                          </button>\n                        ` : html`\n                          <a href=\"/login\" class=\"book-btn\">\n                            Войти для записи\n                          </a>\n                        `}\n                      </div>\n                    </div>\n                  `;\n                })}\n              </div>\n            </div>\n          ` : html`\n            <div class=\"empty-state\">\n              <div class=\"empty-state-icon\">📅</div>\n              <h3>Нет тренировок на выбранную дату</h3>\n              <p>Попробуйте выбрать другую дату или изменить фильтры</p>\n            </div>\n          `}\n        </div>\n      </div>\n    `;\n  }\n}\n\ncustomElements.define('schedule-page', SchedulePage);\n", "import { LitElement, html, css } from '/node_modules/lit/index.js';\nimport { databaseService } from './database.js';\n\nclass TrainersPage extends LitElement {\n  static styles = css`\n    :host {\n      display: block;\n    }\n\n    .trainers-container {\n      max-width: var(--container-xl);\n      margin: 0 auto;\n      padding: var(--spacing-6) var(--spacing-4);\n    }\n\n    .trainers-header {\n      text-align: center;\n      margin-bottom: var(--spacing-12);\n    }\n\n    .trainers-title {\n      font-size: var(--font-size-3xl);\n      font-weight: 700;\n      color: var(--gray-800);\n      margin-bottom: var(--spacing-4);\n    }\n\n    .trainers-subtitle {\n      font-size: var(--font-size-lg);\n      color: var(--gray-600);\n      max-width: 600px;\n      margin: 0 auto;\n      line-height: 1.6;\n    }\n\n    .trainers-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\n      gap: var(--spacing-8);\n    }\n\n    .trainer-card {\n      background: white;\n      border-radius: var(--radius-2xl);\n      box-shadow: var(--shadow-lg);\n      overflow: hidden;\n      transition: all var(--transition-normal);\n      position: relative;\n    }\n\n    .trainer-card:hover {\n      transform: translateY(-8px);\n      box-shadow: var(--shadow-xl);\n    }\n\n    .trainer-image {\n      width: 100%;\n      height: 280px;\n      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      color: white;\n      font-size: 4rem;\n      position: relative;\n      overflow: hidden;\n    }\n\n    .trainer-image::before {\n      content: '';\n      position: absolute;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);\n      transform: translateX(-100%);\n      transition: transform 0.6s ease;\n    }\n\n    .trainer-card:hover .trainer-image::before {\n      transform: translateX(100%);\n    }\n\n    .trainer-content {\n      padding: var(--spacing-6);\n    }\n\n    .trainer-name {\n      font-size: var(--font-size-xl);\n      font-weight: 700;\n      color: var(--gray-800);\n      margin-bottom: var(--spacing-2);\n    }\n\n    .trainer-specializations {\n      display: flex;\n      flex-wrap: wrap;\n      gap: var(--spacing-2);\n      margin-bottom: var(--spacing-4);\n    }\n\n    .specialization-tag {\n      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));\n      color: white;\n      padding: var(--spacing-1) var(--spacing-3);\n      border-radius: var(--radius-xl);\n      font-size: var(--font-size-xs);\n      font-weight: 500;\n      text-transform: uppercase;\n      letter-spacing: 0.5px;\n    }\n\n    .trainer-bio {\n      color: var(--gray-600);\n      line-height: 1.6;\n      margin-bottom: var(--spacing-4);\n    }\n\n    .trainer-experience {\n      display: flex;\n      align-items: center;\n      gap: var(--spacing-2);\n      color: var(--gray-700);\n      font-weight: 500;\n      margin-bottom: var(--spacing-4);\n    }\n\n    .experience-icon {\n      width: 24px;\n      height: 24px;\n      background: var(--secondary-color);\n      border-radius: 50%;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      color: white;\n      font-size: var(--font-size-sm);\n    }\n\n    .trainer-actions {\n      display: flex;\n      gap: var(--spacing-3);\n    }\n\n    .action-btn {\n      flex: 1;\n      padding: var(--spacing-3) var(--spacing-4);\n      border: none;\n      border-radius: var(--radius-lg);\n      font-size: var(--font-size-sm);\n      font-weight: 500;\n      cursor: pointer;\n      transition: all var(--transition-fast);\n      text-decoration: none;\n      text-align: center;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      gap: var(--spacing-2);\n    }\n\n    .btn-primary {\n      background-color: var(--primary-color);\n      color: white;\n    }\n\n    .btn-primary:hover {\n      background-color: var(--primary-dark);\n      transform: translateY(-1px);\n    }\n\n    .btn-outline {\n      background-color: transparent;\n      color: var(--primary-color);\n      border: 2px solid var(--primary-color);\n    }\n\n    .btn-outline:hover {\n      background-color: var(--primary-color);\n      color: white;\n    }\n\n    .loading-state {\n      text-align: center;\n      padding: var(--spacing-12);\n    }\n\n    .spinner {\n      width: 40px;\n      height: 40px;\n      border: 4px solid var(--gray-200);\n      border-top: 4px solid var(--primary-color);\n      border-radius: 50%;\n      animation: spin 1s linear infinite;\n      margin: 0 auto var(--spacing-4);\n    }\n\n    @keyframes spin {\n      0% { transform: rotate(0deg); }\n      100% { transform: rotate(360deg); }\n    }\n\n    .empty-state {\n      text-align: center;\n      padding: var(--spacing-12);\n      color: var(--gray-500);\n    }\n\n    .empty-state-icon {\n      font-size: 4rem;\n      margin-bottom: var(--spacing-4);\n    }\n\n    .stats-section {\n      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));\n      color: white;\n      padding: var(--spacing-12) 0;\n      margin: var(--spacing-16) 0;\n      border-radius: var(--radius-2xl);\n    }\n\n    .stats-container {\n      max-width: var(--container-lg);\n      margin: 0 auto;\n      padding: 0 var(--spacing-4);\n      text-align: center;\n    }\n\n    .stats-title {\n      font-size: var(--font-size-2xl);\n      font-weight: 700;\n      margin-bottom: var(--spacing-8);\n    }\n\n    .stats-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n      gap: var(--spacing-6);\n    }\n\n    .stat-item {\n      text-align: center;\n    }\n\n    .stat-number {\n      font-size: var(--font-size-3xl);\n      font-weight: 700;\n      margin-bottom: var(--spacing-2);\n    }\n\n    .stat-label {\n      font-size: var(--font-size-base);\n      opacity: 0.9;\n    }\n\n    .trainer-modal {\n      position: fixed;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      background: rgba(0, 0, 0, 0.5);\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      z-index: 1000;\n      padding: var(--spacing-4);\n    }\n\n    .modal-content {\n      background: white;\n      border-radius: var(--radius-xl);\n      max-width: 600px;\n      width: 100%;\n      max-height: 90vh;\n      overflow-y: auto;\n      position: relative;\n    }\n\n    .modal-header {\n      padding: var(--spacing-6);\n      border-bottom: 1px solid var(--gray-200);\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n    }\n\n    .modal-title {\n      font-size: var(--font-size-xl);\n      font-weight: 700;\n      color: var(--gray-800);\n    }\n\n    .close-btn {\n      background: none;\n      border: none;\n      font-size: var(--font-size-xl);\n      color: var(--gray-500);\n      cursor: pointer;\n      padding: var(--spacing-2);\n      border-radius: var(--radius-md);\n      transition: all var(--transition-fast);\n    }\n\n    .close-btn:hover {\n      background-color: var(--gray-100);\n      color: var(--gray-700);\n    }\n\n    .modal-body {\n      padding: var(--spacing-6);\n    }\n\n    @media (max-width: 768px) {\n      .trainers-grid {\n        grid-template-columns: 1fr;\n      }\n\n      .trainer-actions {\n        flex-direction: column;\n      }\n\n      .stats-grid {\n        grid-template-columns: repeat(2, 1fr);\n      }\n\n      .modal-content {\n        margin: var(--spacing-4);\n        max-height: calc(100vh - 2rem);\n      }\n    }\n\n    @media (max-width: 480px) {\n      .stats-grid {\n        grid-template-columns: 1fr;\n      }\n    }\n  `;\n\n  static properties = {\n    trainers: { type: Array },\n    loading: { type: Boolean },\n    selectedTrainer: { type: Object },\n    showModal: { type: Boolean }\n  };\n\n  constructor() {\n    super();\n    this.trainers = [];\n    this.loading = true;\n    this.selectedTrainer = null;\n    this.showModal = false;\n    \n    this.loadTrainers();\n  }\n\n  async loadTrainers() {\n    try {\n      this.loading = true;\n      this.trainers = await databaseService.getTrainers();\n    } catch (error) {\n      console.error('Ошибка загрузки тренеров:', error);\n    } finally {\n      this.loading = false;\n    }\n  }\n\n  openTrainerModal(trainer) {\n    this.selectedTrainer = trainer;\n    this.showModal = true;\n    document.body.style.overflow = 'hidden';\n  }\n\n  closeTrainerModal() {\n    this.showModal = false;\n    this.selectedTrainer = null;\n    document.body.style.overflow = '';\n  }\n\n  handleModalClick(event) {\n    if (event.target === event.currentTarget) {\n      this.closeTrainerModal();\n    }\n  }\n\n  getTrainerInitials(name) {\n    return name.split(' ').map(n => n[0]).join('').toUpperCase();\n  }\n\n  render() {\n    if (this.loading) {\n      return html`\n        <div class=\"trainers-container\">\n          <div class=\"loading-state\">\n            <div class=\"spinner\"></div>\n            <p>Загрузка информации о тренерах...</p>\n          </div>\n        </div>\n      `;\n    }\n\n    if (this.trainers.length === 0) {\n      return html`\n        <div class=\"trainers-container\">\n          <div class=\"empty-state\">\n            <div class=\"empty-state-icon\">👨‍🏫</div>\n            <h3>Информация о тренерах недоступна</h3>\n            <p>Попробуйте обновить страницу позже</p>\n          </div>\n        </div>\n      `;\n    }\n\n    return html`\n      <div class=\"trainers-container\">\n        <div class=\"trainers-header\">\n          <h1 class=\"trainers-title\">Наши тренеры</h1>\n          <p class=\"trainers-subtitle\">\n            Познакомьтесь с нашей командой профессиональных тренеров. \n            Каждый из них имеет многолетний опыт и поможет вам достичь ваших целей в фитнесе.\n          </p>\n        </div>\n\n        <div class=\"stats-section\">\n          <div class=\"stats-container\">\n            <h2 class=\"stats-title\">Наша команда в цифрах</h2>\n            <div class=\"stats-grid\">\n              <div class=\"stat-item\">\n                <div class=\"stat-number\">${this.trainers.length}</div>\n                <div class=\"stat-label\">Профессиональных тренеров</div>\n              </div>\n              <div class=\"stat-item\">\n                <div class=\"stat-number\">50+</div>\n                <div class=\"stat-label\">Лет общего опыта</div>\n              </div>\n              <div class=\"stat-item\">\n                <div class=\"stat-number\">1000+</div>\n                <div class=\"stat-label\">Довольных клиентов</div>\n              </div>\n              <div class=\"stat-item\">\n                <div class=\"stat-number\">15+</div>\n                <div class=\"stat-label\">Видов тренировок</div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"trainers-grid\">\n          ${this.trainers.map(trainer => html`\n            <div class=\"trainer-card\">\n              <div class=\"trainer-image\">\n                ${this.getTrainerInitials(trainer.name)}\n              </div>\n              <div class=\"trainer-content\">\n                <h3 class=\"trainer-name\">${trainer.name}</h3>\n                \n                <div class=\"trainer-specializations\">\n                  ${trainer.specialization.map(spec => html`\n                    <span class=\"specialization-tag\">${spec}</span>\n                  `)}\n                </div>\n                \n                <p class=\"trainer-bio\">${trainer.bio}</p>\n                \n                <div class=\"trainer-experience\">\n                  <div class=\"experience-icon\">⭐</div>\n                  <span>Опыт работы: ${trainer.experience}</span>\n                </div>\n                \n                <div class=\"trainer-actions\">\n                  <button class=\"action-btn btn-primary\" \n                          @click=${() => this.openTrainerModal(trainer)}>\n                    👁️ Подробнее\n                  </button>\n                  <a href=\"/schedule\" class=\"action-btn btn-outline\">\n                    📅 Расписание\n                  </a>\n                </div>\n              </div>\n            </div>\n          `)}\n        </div>\n      </div>\n\n      ${this.showModal && this.selectedTrainer ? html`\n        <div class=\"trainer-modal\" @click=${this.handleModalClick}>\n          <div class=\"modal-content\">\n            <div class=\"modal-header\">\n              <h2 class=\"modal-title\">${this.selectedTrainer.name}</h2>\n              <button class=\"close-btn\" @click=${this.closeTrainerModal}>×</button>\n            </div>\n            <div class=\"modal-body\">\n              <div class=\"trainer-image\" style=\"height: 200px; margin-bottom: var(--spacing-6);\">\n                ${this.getTrainerInitials(this.selectedTrainer.name)}\n              </div>\n              \n              <div class=\"trainer-specializations\" style=\"margin-bottom: var(--spacing-4);\">\n                ${this.selectedTrainer.specialization.map(spec => html`\n                  <span class=\"specialization-tag\">${spec}</span>\n                `)}\n              </div>\n              \n              <div class=\"trainer-experience\" style=\"margin-bottom: var(--spacing-4);\">\n                <div class=\"experience-icon\">⭐</div>\n                <span>Опыт работы: ${this.selectedTrainer.experience}</span>\n              </div>\n              \n              <p class=\"trainer-bio\" style=\"margin-bottom: var(--spacing-6);\">\n                ${this.selectedTrainer.bio}\n              </p>\n              \n              <div style=\"display: flex; gap: var(--spacing-3);\">\n                <a href=\"/schedule\" class=\"action-btn btn-primary\" style=\"flex: 1;\">\n                  📅 Посмотреть расписание\n                </a>\n                <button class=\"action-btn btn-outline\" @click=${this.closeTrainerModal}>\n                  Закрыть\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      ` : ''}\n    `;\n  }\n}\n\ncustomElements.define('trainers-page', TrainersPage);\n", "import { LitElement, html, css } from '/node_modules/lit/index.js';\nimport { databaseService } from './database.js';\n\nclass AboutPage extends LitElement {\n  static styles = css`\n    :host {\n      display: block;\n    }\n\n    .about-container {\n      max-width: var(--container-xl);\n      margin: 0 auto;\n      padding: var(--spacing-6) var(--spacing-4);\n    }\n\n    .hero-section {\n      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));\n      color: white;\n      padding: var(--spacing-16) 0;\n      margin: 0 calc(-1 * var(--spacing-4)) var(--spacing-16);\n      text-align: center;\n      position: relative;\n      overflow: hidden;\n    }\n\n    .hero-section::before {\n      content: '';\n      position: absolute;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      background: linear-gradient(45deg, rgba(37, 99, 235, 0.1), rgba(16, 185, 129, 0.1));\n      opacity: 0.2;\n      z-index: 0;\n    }\n\n    .hero-content {\n      position: relative;\n      z-index: 1;\n      max-width: var(--container-lg);\n      margin: 0 auto;\n      padding: 0 var(--spacing-4);\n    }\n\n    .hero-title {\n      font-size: var(--font-size-4xl);\n      font-weight: 700;\n      margin-bottom: var(--spacing-6);\n      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\n    }\n\n    .hero-subtitle {\n      font-size: var(--font-size-xl);\n      margin-bottom: var(--spacing-8);\n      opacity: 0.9;\n      line-height: 1.6;\n    }\n\n    .content-section {\n      margin-bottom: var(--spacing-16);\n    }\n\n    .section-title {\n      font-size: var(--font-size-2xl);\n      font-weight: 700;\n      color: var(--gray-800);\n      margin-bottom: var(--spacing-6);\n      text-align: center;\n    }\n\n    .content-grid {\n      display: grid;\n      grid-template-columns: 1fr 1fr;\n      gap: var(--spacing-8);\n      align-items: center;\n    }\n\n    .content-text {\n      font-size: var(--font-size-base);\n      line-height: 1.7;\n      color: var(--gray-600);\n    }\n\n    .content-text h3 {\n      color: var(--gray-800);\n      font-size: var(--font-size-lg);\n      font-weight: 600;\n      margin: var(--spacing-6) 0 var(--spacing-3) 0;\n    }\n\n    .content-text ul {\n      margin: var(--spacing-4) 0;\n      padding-left: var(--spacing-6);\n    }\n\n    .content-text li {\n      margin-bottom: var(--spacing-2);\n    }\n\n    .content-image {\n      width: 100%;\n      height: 300px;\n      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));\n      border-radius: var(--radius-xl);\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      color: white;\n      font-size: 4rem;\n      position: relative;\n      overflow: hidden;\n    }\n\n    .content-image::before {\n      content: '';\n      position: absolute;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);\n      transform: translateX(-100%);\n      animation: shimmer 3s infinite;\n    }\n\n    @keyframes shimmer {\n      0% { transform: translateX(-100%); }\n      100% { transform: translateX(100%); }\n    }\n\n    .features-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n      gap: var(--spacing-6);\n      margin-top: var(--spacing-8);\n    }\n\n    .feature-card {\n      background: white;\n      border-radius: var(--radius-xl);\n      padding: var(--spacing-6);\n      text-align: center;\n      box-shadow: var(--shadow-md);\n      transition: all var(--transition-normal);\n    }\n\n    .feature-card:hover {\n      transform: translateY(-5px);\n      box-shadow: var(--shadow-xl);\n    }\n\n    .feature-icon {\n      width: 80px;\n      height: 80px;\n      margin: 0 auto var(--spacing-4);\n      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));\n      border-radius: 50%;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      color: white;\n      font-size: var(--font-size-2xl);\n    }\n\n    .feature-title {\n      font-size: var(--font-size-lg);\n      font-weight: 600;\n      color: var(--gray-800);\n      margin-bottom: var(--spacing-3);\n    }\n\n    .feature-description {\n      color: var(--gray-600);\n      line-height: 1.6;\n    }\n\n    .contact-section {\n      background: white;\n      border-radius: var(--radius-2xl);\n      padding: var(--spacing-8);\n      box-shadow: var(--shadow-lg);\n      margin-top: var(--spacing-16);\n    }\n\n    .contact-grid {\n      display: grid;\n      grid-template-columns: 1fr 1fr;\n      gap: var(--spacing-8);\n    }\n\n    .contact-info h3 {\n      font-size: var(--font-size-xl);\n      font-weight: 700;\n      color: var(--gray-800);\n      margin-bottom: var(--spacing-4);\n    }\n\n    .contact-item {\n      display: flex;\n      align-items: flex-start;\n      gap: var(--spacing-3);\n      margin-bottom: var(--spacing-4);\n    }\n\n    .contact-icon {\n      width: 24px;\n      height: 24px;\n      background: var(--primary-color);\n      border-radius: var(--radius-md);\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      color: white;\n      font-size: var(--font-size-sm);\n      flex-shrink: 0;\n      margin-top: 2px;\n    }\n\n    .contact-details {\n      color: var(--gray-600);\n      line-height: 1.6;\n    }\n\n    .contact-details strong {\n      color: var(--gray-800);\n      display: block;\n      margin-bottom: var(--spacing-1);\n    }\n\n    .map-placeholder {\n      width: 100%;\n      height: 300px;\n      background: linear-gradient(135deg, var(--gray-200), var(--gray-300));\n      border-radius: var(--radius-lg);\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      color: var(--gray-500);\n      font-size: var(--font-size-lg);\n      position: relative;\n      overflow: hidden;\n    }\n\n    .map-placeholder::before {\n      content: '🗺️';\n      font-size: 3rem;\n      margin-bottom: var(--spacing-2);\n    }\n\n    .stats-section {\n      background: linear-gradient(135deg, var(--gray-800), var(--gray-700));\n      color: white;\n      padding: var(--spacing-12) 0;\n      margin: var(--spacing-16) calc(-1 * var(--spacing-4));\n      border-radius: var(--radius-2xl);\n    }\n\n    .stats-container {\n      max-width: var(--container-lg);\n      margin: 0 auto;\n      padding: 0 var(--spacing-4);\n      text-align: center;\n    }\n\n    .stats-title {\n      font-size: var(--font-size-2xl);\n      font-weight: 700;\n      margin-bottom: var(--spacing-8);\n    }\n\n    .stats-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n      gap: var(--spacing-6);\n    }\n\n    .stat-item {\n      text-align: center;\n    }\n\n    .stat-number {\n      font-size: var(--font-size-3xl);\n      font-weight: 700;\n      margin-bottom: var(--spacing-2);\n      background: linear-gradient(135deg, var(--primary-light), var(--secondary-color));\n      -webkit-background-clip: text;\n      -webkit-text-fill-color: transparent;\n      background-clip: text;\n    }\n\n    .stat-label {\n      font-size: var(--font-size-base);\n      opacity: 0.9;\n    }\n\n    .loading-state {\n      text-align: center;\n      padding: var(--spacing-12);\n    }\n\n    .spinner {\n      width: 40px;\n      height: 40px;\n      border: 4px solid var(--gray-200);\n      border-top: 4px solid var(--primary-color);\n      border-radius: 50%;\n      animation: spin 1s linear infinite;\n      margin: 0 auto var(--spacing-4);\n    }\n\n    @keyframes spin {\n      0% { transform: rotate(0deg); }\n      100% { transform: rotate(360deg); }\n    }\n\n    @media (max-width: 768px) {\n      .hero-title {\n        font-size: var(--font-size-3xl);\n      }\n\n      .hero-subtitle {\n        font-size: var(--font-size-lg);\n      }\n\n      .content-grid {\n        grid-template-columns: 1fr;\n        gap: var(--spacing-6);\n      }\n\n      .contact-grid {\n        grid-template-columns: 1fr;\n      }\n\n      .stats-grid {\n        grid-template-columns: repeat(2, 1fr);\n      }\n\n      .features-grid {\n        grid-template-columns: 1fr;\n      }\n    }\n\n    @media (max-width: 480px) {\n      .stats-grid {\n        grid-template-columns: 1fr;\n      }\n    }\n  `;\n\n  static properties = {\n    content: { type: Object },\n    loading: { type: Boolean }\n  };\n\n  constructor() {\n    super();\n    this.content = null;\n    this.loading = true;\n    \n    this.loadContent();\n  }\n\n  async loadContent() {\n    try {\n      this.loading = true;\n      this.content = await databaseService.getContent('about');\n    } catch (error) {\n      console.error('Ошибка загрузки контента:', error);\n    } finally {\n      this.loading = false;\n    }\n  }\n\n  render() {\n    if (this.loading) {\n      return html`\n        <div class=\"about-container\">\n          <div class=\"loading-state\">\n            <div class=\"spinner\"></div>\n            <p>Загрузка информации о студии...</p>\n          </div>\n        </div>\n      `;\n    }\n\n    return html`\n      <div class=\"hero-section\">\n        <div class=\"hero-content\">\n          <h1 class=\"hero-title\">О нашей студии</h1>\n          <p class=\"hero-subtitle\">\n            FitStudio - это современное пространство для занятий фитнесом, \n            где каждый найдет подходящую программу тренировок\n          </p>\n        </div>\n      </div>\n\n      <div class=\"about-container\">\n        <section class=\"content-section\">\n          <h2 class=\"section-title\">Наша история</h2>\n          <div class=\"content-grid\">\n            <div class=\"content-text\">\n              <p>\n                FitStudio была основана в 2018 году с целью создания современного \n                и комфортного пространства для занятий фитнесом. Мы верим, что \n                здоровый образ жизни должен быть доступен каждому.\n              </p>\n              <p>\n                За годы работы мы помогли тысячам людей достичь своих целей в фитнесе, \n                улучшить здоровье и обрести уверенность в себе. Наша команда \n                профессиональных тренеров постоянно совершенствует свои навыки \n                и изучает новые методики тренировок.\n              </p>\n            </div>\n            <div class=\"content-image\">\n              🏢\n            </div>\n          </div>\n        </section>\n\n        <section class=\"content-section\">\n          <h2 class=\"section-title\">Наши преимущества</h2>\n          <div class=\"features-grid\">\n            <div class=\"feature-card\">\n              <div class=\"feature-icon\">👨‍🏫</div>\n              <h3 class=\"feature-title\">Профессиональные тренеры</h3>\n              <p class=\"feature-description\">\n                Сертифицированные инструкторы с многолетним опытом \n                и постоянным повышением квалификации\n              </p>\n            </div>\n            <div class=\"feature-card\">\n              <div class=\"feature-icon\">🏋️‍♀️</div>\n              <h3 class=\"feature-title\">Современное оборудование</h3>\n              <p class=\"feature-description\">\n                Новейшее фитнес-оборудование от ведущих мировых \n                производителей для эффективных тренировок\n              </p>\n            </div>\n            <div class=\"feature-card\">\n              <div class=\"feature-icon\">📅</div>\n              <h3 class=\"feature-title\">Удобное расписание</h3>\n              <p class=\"feature-description\">\n                Гибкое расписание занятий с утра до вечера, \n                которое подойдет для любого образа жизни\n              </p>\n            </div>\n            <div class=\"feature-card\">\n              <div class=\"feature-icon\">🎯</div>\n              <h3 class=\"feature-title\">Индивидуальный подход</h3>\n              <p class=\"feature-description\">\n                Персональные программы тренировок с учетом \n                ваших особенностей, целей и уровня подготовки\n              </p>\n            </div>\n            <div class=\"feature-card\">\n              <div class=\"feature-icon\">🏆</div>\n              <h3 class=\"feature-title\">Результативность</h3>\n              <p class=\"feature-description\">\n                Проверенные методики и постоянный контроль \n                прогресса для достижения ваших целей\n              </p>\n            </div>\n            <div class=\"feature-card\">\n              <div class=\"feature-icon\">💚</div>\n              <h3 class=\"feature-title\">Дружелюбная атмосфера</h3>\n              <p class=\"feature-description\">\n                Комфортная и поддерживающая среда, где каждый \n                чувствует себя как дома\n              </p>\n            </div>\n          </div>\n        </section>\n\n        <div class=\"stats-section\">\n          <div class=\"stats-container\">\n            <h2 class=\"stats-title\">FitStudio в цифрах</h2>\n            <div class=\"stats-grid\">\n              <div class=\"stat-item\">\n                <div class=\"stat-number\">2000+</div>\n                <div class=\"stat-label\">Довольных клиентов</div>\n              </div>\n              <div class=\"stat-item\">\n                <div class=\"stat-number\">15</div>\n                <div class=\"stat-label\">Видов тренировок</div>\n              </div>\n              <div class=\"stat-item\">\n                <div class=\"stat-number\">8</div>\n                <div class=\"stat-label\">Профессиональных тренеров</div>\n              </div>\n              <div class=\"stat-item\">\n                <div class=\"stat-number\">500м²</div>\n                <div class=\"stat-label\">Площадь студии</div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <section class=\"content-section\">\n          <h2 class=\"section-title\">Наши программы</h2>\n          <div class=\"content-grid\">\n            <div class=\"content-image\">\n              🧘‍♀️\n            </div>\n            <div class=\"content-text\">\n              <h3>Йога и пилатес</h3>\n              <p>\n                Развитие гибкости, силы и баланса через древние практики йоги \n                и современные методики пилатеса. Подходит для всех уровней подготовки.\n              </p>\n              \n              <h3>Функциональный тренинг</h3>\n              <p>\n                Интенсивные тренировки с использованием собственного веса и \n                функциональных движений для развития силы, выносливости и координации.\n              </p>\n              \n              <h3>Танцевальные программы</h3>\n              <p>\n                Энергичные занятия под музыку, которые помогают сжигать калории, \n                улучшать координацию и получать удовольствие от движения.\n              </p>\n            </div>\n          </div>\n        </section>\n\n        <div class=\"contact-section\">\n          <h2 class=\"section-title\">Контакты и расположение</h2>\n          <div class=\"contact-grid\">\n            <div class=\"contact-info\">\n              <h3>Как нас найти</h3>\n              \n              <div class=\"contact-item\">\n                <div class=\"contact-icon\">📍</div>\n                <div class=\"contact-details\">\n                  <strong>Адрес</strong>\n                  г. Москва, ул. Спортивная, д. 15<br>\n                  (5 минут от метро \"Спортивная\")\n                </div>\n              </div>\n              \n              <div class=\"contact-item\">\n                <div class=\"contact-icon\">📞</div>\n                <div class=\"contact-details\">\n                  <strong>Телефон</strong>\n                  +7 (495) 123-45-67<br>\n                  Ежедневно с 07:00 до 23:00\n                </div>\n              </div>\n              \n              <div class=\"contact-item\">\n                <div class=\"contact-icon\">✉️</div>\n                <div class=\"contact-details\">\n                  <strong>Email</strong>\n                  <EMAIL><br>\n                  Ответим в течение 24 часов\n                </div>\n              </div>\n              \n              <div class=\"contact-item\">\n                <div class=\"contact-icon\">🕒</div>\n                <div class=\"contact-details\">\n                  <strong>Режим работы</strong>\n                  Понедельник - Пятница: 07:00 - 23:00<br>\n                  Суббота - Воскресенье: 09:00 - 21:00\n                </div>\n              </div>\n            </div>\n            \n            <div class=\"map-placeholder\">\n              Интерактивная карта\n            </div>\n          </div>\n        </div>\n      </div>\n    `;\n  }\n}\n\ncustomElements.define('about-page', AboutPage);\n", "// Эмуляция bcrypt для демо-версии\nconst bcrypt = {\n  async hash(password, saltRounds) {\n    // Простое хеширование для демо\n    return btoa(password + saltRounds);\n  },\n  async compare(password, hash) {\n    // Простое сравнение для демо\n    return btoa(password + '10') === hash;\n  }\n};\nimport { databaseService } from './database.js';\n\nclass AuthService {\n  constructor() {\n    this.currentUser = null;\n    this.loadUserFromStorage();\n  }\n\n  loadUserFromStorage() {\n    try {\n      const savedUser = localStorage.getItem('currentUser');\n      if (savedUser) {\n        this.currentUser = JSON.parse(savedUser);\n      }\n    } catch (error) {\n      console.error('Ошибка загрузки пользователя из localStorage:', error);\n      localStorage.removeItem('currentUser');\n    }\n  }\n\n  saveUserToStorage(user) {\n    try {\n      localStorage.setItem('currentUser', JSON.stringify(user));\n      this.currentUser = user;\n    } catch (error) {\n      console.error('Ошибка сохранения пользователя в localStorage:', error);\n    }\n  }\n\n  removeUserFromStorage() {\n    localStorage.removeItem('currentUser');\n    this.currentUser = null;\n  }\n\n  async register(userData) {\n    try {\n      // Валидация данных\n      const validation = this.validateRegistrationData(userData);\n      if (!validation.isValid) {\n        throw new Error(validation.errors.join(', '));\n      }\n\n      // Проверка существования пользователя\n      const existingUser = await databaseService.getUserByEmail(userData.email);\n      if (existingUser) {\n        throw new Error('Пользователь с таким email уже существует');\n      }\n\n      // Хеширование пароля\n      const saltRounds = 10;\n      const passwordHash = await bcrypt.hash(userData.password, saltRounds);\n\n      // Создание пользователя\n      const newUser = {\n        email: userData.email,\n        name: userData.name,\n        phone: userData.phone,\n        password_hash: passwordHash\n      };\n\n      const result = await databaseService.createUser(newUser);\n      \n      // Получаем созданного пользователя без пароля\n      const user = await databaseService.getUserByEmail(userData.email);\n      const userWithoutPassword = this.removePasswordFromUser(user);\n      \n      // Сохраняем в localStorage\n      this.saveUserToStorage(userWithoutPassword);\n\n      return {\n        success: true,\n        user: userWithoutPassword,\n        message: 'Регистрация прошла успешно'\n      };\n\n    } catch (error) {\n      console.error('Ошибка регистрации:', error);\n      return {\n        success: false,\n        error: error.message || 'Ошибка при регистрации'\n      };\n    }\n  }\n\n  async login(email, password) {\n    try {\n      // Валидация входных данных\n      if (!email || !password) {\n        throw new Error('Email и пароль обязательны для заполнения');\n      }\n\n      if (!this.isValidEmail(email)) {\n        throw new Error('Некорректный формат email');\n      }\n\n      // Поиск пользователя\n      const user = await databaseService.getUserByEmail(email);\n      if (!user) {\n        throw new Error('Пользователь с таким email не найден');\n      }\n\n      // Проверка пароля\n      const isPasswordValid = await bcrypt.compare(password, user.password_hash);\n      if (!isPasswordValid) {\n        throw new Error('Неверный пароль');\n      }\n\n      // Успешная авторизация\n      const userWithoutPassword = this.removePasswordFromUser(user);\n      this.saveUserToStorage(userWithoutPassword);\n\n      return {\n        success: true,\n        user: userWithoutPassword,\n        message: 'Вход выполнен успешно'\n      };\n\n    } catch (error) {\n      console.error('Ошибка входа:', error);\n      return {\n        success: false,\n        error: error.message || 'Ошибка при входе в систему'\n      };\n    }\n  }\n\n  logout() {\n    this.removeUserFromStorage();\n    return {\n      success: true,\n      message: 'Выход выполнен успешно'\n    };\n  }\n\n  getCurrentUser() {\n    return this.currentUser;\n  }\n\n  isAuthenticated() {\n    return this.currentUser !== null;\n  }\n\n  isAdmin() {\n    return this.currentUser && this.currentUser.role === 'admin';\n  }\n\n  validateRegistrationData(userData) {\n    const errors = [];\n\n    // Проверка имени\n    if (!userData.name || userData.name.trim().length < 2) {\n      errors.push('Имя должно содержать минимум 2 символа');\n    }\n\n    // Проверка email\n    if (!userData.email || !this.isValidEmail(userData.email)) {\n      errors.push('Некорректный формат email');\n    }\n\n    // Проверка телефона\n    if (!userData.phone || !this.isValidPhone(userData.phone)) {\n      errors.push('Некорректный формат телефона');\n    }\n\n    // Проверка пароля\n    if (!userData.password || userData.password.length < 6) {\n      errors.push('Пароль должен содержать минимум 6 символов');\n    }\n\n    // Проверка подтверждения пароля\n    if (userData.password !== userData.confirmPassword) {\n      errors.push('Пароли не совпадают');\n    }\n\n    return {\n      isValid: errors.length === 0,\n      errors: errors\n    };\n  }\n\n  isValidEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n  }\n\n  isValidPhone(phone) {\n    // Простая проверка российского номера телефона\n    const phoneRegex = /^(\\+7|8)?[\\s\\-]?\\(?[489][0-9]{2}\\)?[\\s\\-]?[0-9]{3}[\\s\\-]?[0-9]{2}[\\s\\-]?[0-9]{2}$/;\n    return phoneRegex.test(phone.replace(/\\s/g, ''));\n  }\n\n  removePasswordFromUser(user) {\n    const { password_hash, ...userWithoutPassword } = user;\n    return userWithoutPassword;\n  }\n\n  // Метод для обновления профиля пользователя\n  async updateProfile(userData) {\n    try {\n      if (!this.isAuthenticated()) {\n        throw new Error('Необходима авторизация');\n      }\n\n      // Валидация данных\n      const errors = [];\n      \n      if (!userData.name || userData.name.trim().length < 2) {\n        errors.push('Имя должно содержать минимум 2 символа');\n      }\n\n      if (!userData.phone || !this.isValidPhone(userData.phone)) {\n        errors.push('Некорректный формат телефона');\n      }\n\n      if (errors.length > 0) {\n        throw new Error(errors.join(', '));\n      }\n\n      // Обновление данных пользователя в базе\n      const updatedUser = {\n        ...this.currentUser,\n        name: userData.name,\n        phone: userData.phone\n      };\n\n      // Здесь должно быть обновление в базе данных\n      // Пока просто обновляем в localStorage\n      this.saveUserToStorage(updatedUser);\n\n      return {\n        success: true,\n        user: updatedUser,\n        message: 'Профиль обновлен успешно'\n      };\n\n    } catch (error) {\n      console.error('Ошибка обновления профиля:', error);\n      return {\n        success: false,\n        error: error.message || 'Ошибка при обновлении профиля'\n      };\n    }\n  }\n\n  // Метод для смены пароля\n  async changePassword(currentPassword, newPassword, confirmPassword) {\n    try {\n      if (!this.isAuthenticated()) {\n        throw new Error('Необходима авторизация');\n      }\n\n      // Валидация\n      if (!currentPassword || !newPassword || !confirmPassword) {\n        throw new Error('Все поля обязательны для заполнения');\n      }\n\n      if (newPassword.length < 6) {\n        throw new Error('Новый пароль должен содержать минимум 6 символов');\n      }\n\n      if (newPassword !== confirmPassword) {\n        throw new Error('Новые пароли не совпадают');\n      }\n\n      // Получаем полные данные пользователя для проверки текущего пароля\n      const fullUser = await databaseService.getUserByEmail(this.currentUser.email);\n      \n      // Проверяем текущий пароль\n      const isCurrentPasswordValid = await bcrypt.compare(currentPassword, fullUser.password_hash);\n      if (!isCurrentPasswordValid) {\n        throw new Error('Неверный текущий пароль');\n      }\n\n      // Хешируем новый пароль\n      const saltRounds = 10;\n      const newPasswordHash = await bcrypt.hash(newPassword, saltRounds);\n\n      // Здесь должно быть обновление пароля в базе данных\n      // Пока просто возвращаем успех\n      \n      return {\n        success: true,\n        message: 'Пароль изменен успешно'\n      };\n\n    } catch (error) {\n      console.error('Ошибка смены пароля:', error);\n      return {\n        success: false,\n        error: error.message || 'Ошибка при смене пароля'\n      };\n    }\n  }\n}\n\n// Создаем единственный экземпляр сервиса\nexport const authService = new AuthService();\n", "import { LitElement, html, css } from '/node_modules/lit/index.js';\nimport { authService } from './auth.js';\n\nclass LoginPage extends LitElement {\n  static styles = css`\n    :host {\n      display: block;\n    }\n\n    .login-container {\n      max-width: 400px;\n      margin: var(--spacing-12) auto;\n      padding: 0 var(--spacing-4);\n    }\n\n    .login-card {\n      background: white;\n      border-radius: var(--radius-xl);\n      box-shadow: var(--shadow-xl);\n      padding: var(--spacing-8);\n    }\n\n    .login-header {\n      text-align: center;\n      margin-bottom: var(--spacing-8);\n    }\n\n    .login-title {\n      font-size: var(--font-size-3xl);\n      font-weight: 700;\n      color: var(--gray-800);\n      margin-bottom: var(--spacing-2);\n    }\n\n    .login-subtitle {\n      color: var(--gray-600);\n      font-size: var(--font-size-base);\n    }\n\n    .form-group {\n      margin-bottom: var(--spacing-6);\n    }\n\n    .form-label {\n      display: block;\n      font-weight: 500;\n      color: var(--gray-700);\n      margin-bottom: var(--spacing-2);\n    }\n\n    .form-input {\n      width: 100%;\n      padding: var(--spacing-3);\n      border: 2px solid var(--gray-200);\n      border-radius: var(--radius-lg);\n      font-size: var(--font-size-base);\n      transition: border-color var(--transition-fast);\n    }\n\n    .form-input:focus {\n      outline: none;\n      border-color: var(--primary-color);\n      box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);\n    }\n\n    .form-input.error {\n      border-color: var(--error-color);\n    }\n\n    .form-error {\n      color: var(--error-color);\n      font-size: var(--font-size-sm);\n      margin-top: var(--spacing-1);\n    }\n\n    .login-button {\n      width: 100%;\n      padding: var(--spacing-4);\n      background-color: var(--primary-color);\n      color: white;\n      border: none;\n      border-radius: var(--radius-lg);\n      font-size: var(--font-size-base);\n      font-weight: 600;\n      cursor: pointer;\n      transition: all var(--transition-fast);\n      margin-bottom: var(--spacing-6);\n    }\n\n    .login-button:hover:not(:disabled) {\n      background-color: var(--primary-dark);\n      transform: translateY(-1px);\n      box-shadow: var(--shadow-md);\n    }\n\n    .login-button:disabled {\n      opacity: 0.5;\n      cursor: not-allowed;\n      transform: none;\n    }\n\n    .login-footer {\n      text-align: center;\n      padding-top: var(--spacing-6);\n      border-top: 1px solid var(--gray-200);\n    }\n\n    .login-footer p {\n      color: var(--gray-600);\n      margin-bottom: var(--spacing-2);\n    }\n\n    .login-footer a {\n      color: var(--primary-color);\n      text-decoration: none;\n      font-weight: 500;\n    }\n\n    .login-footer a:hover {\n      text-decoration: underline;\n    }\n\n    .alert {\n      padding: var(--spacing-3);\n      border-radius: var(--radius-md);\n      margin-bottom: var(--spacing-6);\n      font-size: var(--font-size-sm);\n    }\n\n    .alert-error {\n      background-color: rgb(254 242 242);\n      border: 1px solid rgb(252 165 165);\n      color: var(--error-color);\n    }\n\n    .alert-success {\n      background-color: rgb(240 253 244);\n      border: 1px solid rgb(167 243 208);\n      color: var(--success-color);\n    }\n\n    .loading-spinner {\n      display: inline-block;\n      width: 16px;\n      height: 16px;\n      border: 2px solid transparent;\n      border-top: 2px solid currentColor;\n      border-radius: 50%;\n      animation: spin 1s linear infinite;\n      margin-right: var(--spacing-2);\n    }\n\n    @keyframes spin {\n      0% { transform: rotate(0deg); }\n      100% { transform: rotate(360deg); }\n    }\n\n    .demo-credentials {\n      background-color: var(--gray-50);\n      border: 1px solid var(--gray-200);\n      border-radius: var(--radius-md);\n      padding: var(--spacing-4);\n      margin-bottom: var(--spacing-6);\n    }\n\n    .demo-credentials h4 {\n      font-size: var(--font-size-sm);\n      font-weight: 600;\n      color: var(--gray-700);\n      margin-bottom: var(--spacing-2);\n    }\n\n    .demo-credentials p {\n      font-size: var(--font-size-xs);\n      color: var(--gray-600);\n      margin: var(--spacing-1) 0;\n    }\n\n    @media (max-width: 480px) {\n      .login-container {\n        margin: var(--spacing-6) auto;\n      }\n\n      .login-card {\n        padding: var(--spacing-6);\n      }\n    }\n  `;\n\n  static properties = {\n    loading: { type: Boolean },\n    error: { type: String },\n    success: { type: String },\n    formData: { type: Object }\n  };\n\n  constructor() {\n    super();\n    this.loading = false;\n    this.error = '';\n    this.success = '';\n    this.formData = {\n      email: '',\n      password: ''\n    };\n  }\n\n  handleInputChange(event) {\n    const { name, value } = event.target;\n    this.formData = {\n      ...this.formData,\n      [name]: value\n    };\n    \n    // Очищаем ошибки при изменении полей\n    if (this.error) {\n      this.error = '';\n    }\n  }\n\n  async handleSubmit(event) {\n    event.preventDefault();\n    \n    if (this.loading) return;\n\n    this.loading = true;\n    this.error = '';\n    this.success = '';\n\n    try {\n      const result = await authService.login(this.formData.email, this.formData.password);\n      \n      if (result.success) {\n        this.success = result.message;\n        \n        // Отправляем событие успешного входа\n        this.dispatchEvent(new CustomEvent('user-login', {\n          detail: { user: result.user }\n        }));\n\n        // Перенаправляем на главную страницу через небольшую задержку\n        setTimeout(() => {\n          window.history.pushState({}, '', '/');\n          window.dispatchEvent(new PopStateEvent('popstate'));\n        }, 1000);\n        \n      } else {\n        this.error = result.error;\n      }\n    } catch (error) {\n      this.error = 'Произошла ошибка при входе в систему';\n      console.error('Login error:', error);\n    } finally {\n      this.loading = false;\n    }\n  }\n\n  fillDemoCredentials(type) {\n    if (type === 'admin') {\n      this.formData = {\n        email: '<EMAIL>',\n        password: 'password'\n      };\n    } else {\n      this.formData = {\n        email: '<EMAIL>',\n        password: 'password'\n      };\n    }\n    this.requestUpdate();\n  }\n\n  render() {\n    return html`\n      <div class=\"login-container\">\n        <div class=\"login-card\">\n          <div class=\"login-header\">\n            <h1 class=\"login-title\">Вход в систему</h1>\n            <p class=\"login-subtitle\">Войдите в свой аккаунт, чтобы записаться на тренировки</p>\n          </div>\n\n          <div class=\"demo-credentials\">\n            <h4>Демо-аккаунты для тестирования:</h4>\n            <p><strong>Администратор:</strong> <EMAIL> / password \n              <button class=\"btn btn-ghost btn-sm\" @click=${() => this.fillDemoCredentials('admin')}>Заполнить</button>\n            </p>\n          </div>\n\n          ${this.error ? html`\n            <div class=\"alert alert-error\">\n              ${this.error}\n            </div>\n          ` : ''}\n\n          ${this.success ? html`\n            <div class=\"alert alert-success\">\n              ${this.success}\n            </div>\n          ` : ''}\n\n          <form @submit=${this.handleSubmit}>\n            <div class=\"form-group\">\n              <label class=\"form-label\" for=\"email\">Email</label>\n              <input\n                type=\"email\"\n                id=\"email\"\n                name=\"email\"\n                class=\"form-input\"\n                .value=${this.formData.email}\n                @input=${this.handleInputChange}\n                required\n                autocomplete=\"email\"\n                placeholder=\"<EMAIL>\"\n              />\n            </div>\n\n            <div class=\"form-group\">\n              <label class=\"form-label\" for=\"password\">Пароль</label>\n              <input\n                type=\"password\"\n                id=\"password\"\n                name=\"password\"\n                class=\"form-input\"\n                .value=${this.formData.password}\n                @input=${this.handleInputChange}\n                required\n                autocomplete=\"current-password\"\n                placeholder=\"Введите пароль\"\n              />\n            </div>\n\n            <button type=\"submit\" class=\"login-button\" ?disabled=${this.loading}>\n              ${this.loading ? html`\n                <span class=\"loading-spinner\"></span>\n                Вход...\n              ` : 'Войти'}\n            </button>\n          </form>\n\n          <div class=\"login-footer\">\n            <p>Нет аккаунта? <a href=\"/register\">Зарегистрироваться</a></p>\n          </div>\n        </div>\n      </div>\n    `;\n  }\n}\n\ncustomElements.define('login-page', LoginPage);\n", "import { LitElement, html, css } from '/node_modules/lit/index.js';\nimport { authService } from './auth.js';\n\nclass RegisterPage extends LitElement {\n  static styles = css`\n    :host {\n      display: block;\n    }\n\n    .register-container {\n      max-width: 500px;\n      margin: var(--spacing-12) auto;\n      padding: 0 var(--spacing-4);\n    }\n\n    .register-card {\n      background: white;\n      border-radius: var(--radius-xl);\n      box-shadow: var(--shadow-xl);\n      padding: var(--spacing-8);\n    }\n\n    .register-header {\n      text-align: center;\n      margin-bottom: var(--spacing-8);\n    }\n\n    .register-title {\n      font-size: var(--font-size-3xl);\n      font-weight: 700;\n      color: var(--gray-800);\n      margin-bottom: var(--spacing-2);\n    }\n\n    .register-subtitle {\n      color: var(--gray-600);\n      font-size: var(--font-size-base);\n    }\n\n    .form-row {\n      display: grid;\n      grid-template-columns: 1fr 1fr;\n      gap: var(--spacing-4);\n    }\n\n    .form-group {\n      margin-bottom: var(--spacing-6);\n    }\n\n    .form-label {\n      display: block;\n      font-weight: 500;\n      color: var(--gray-700);\n      margin-bottom: var(--spacing-2);\n    }\n\n    .form-input {\n      width: 100%;\n      padding: var(--spacing-3);\n      border: 2px solid var(--gray-200);\n      border-radius: var(--radius-lg);\n      font-size: var(--font-size-base);\n      transition: border-color var(--transition-fast);\n    }\n\n    .form-input:focus {\n      outline: none;\n      border-color: var(--primary-color);\n      box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);\n    }\n\n    .form-input.error {\n      border-color: var(--error-color);\n    }\n\n    .form-error {\n      color: var(--error-color);\n      font-size: var(--font-size-sm);\n      margin-top: var(--spacing-1);\n    }\n\n    .password-strength {\n      margin-top: var(--spacing-2);\n    }\n\n    .strength-bar {\n      height: 4px;\n      background-color: var(--gray-200);\n      border-radius: 2px;\n      overflow: hidden;\n      margin-bottom: var(--spacing-1);\n    }\n\n    .strength-fill {\n      height: 100%;\n      transition: width var(--transition-fast), background-color var(--transition-fast);\n    }\n\n    .strength-weak { background-color: var(--error-color); }\n    .strength-medium { background-color: var(--warning-color); }\n    .strength-strong { background-color: var(--success-color); }\n\n    .strength-text {\n      font-size: var(--font-size-xs);\n      color: var(--gray-600);\n    }\n\n    .register-button {\n      width: 100%;\n      padding: var(--spacing-4);\n      background-color: var(--primary-color);\n      color: white;\n      border: none;\n      border-radius: var(--radius-lg);\n      font-size: var(--font-size-base);\n      font-weight: 600;\n      cursor: pointer;\n      transition: all var(--transition-fast);\n      margin-bottom: var(--spacing-6);\n    }\n\n    .register-button:hover:not(:disabled) {\n      background-color: var(--primary-dark);\n      transform: translateY(-1px);\n      box-shadow: var(--shadow-md);\n    }\n\n    .register-button:disabled {\n      opacity: 0.5;\n      cursor: not-allowed;\n      transform: none;\n    }\n\n    .register-footer {\n      text-align: center;\n      padding-top: var(--spacing-6);\n      border-top: 1px solid var(--gray-200);\n    }\n\n    .register-footer p {\n      color: var(--gray-600);\n      margin-bottom: var(--spacing-2);\n    }\n\n    .register-footer a {\n      color: var(--primary-color);\n      text-decoration: none;\n      font-weight: 500;\n    }\n\n    .register-footer a:hover {\n      text-decoration: underline;\n    }\n\n    .alert {\n      padding: var(--spacing-3);\n      border-radius: var(--radius-md);\n      margin-bottom: var(--spacing-6);\n      font-size: var(--font-size-sm);\n    }\n\n    .alert-error {\n      background-color: rgb(254 242 242);\n      border: 1px solid rgb(252 165 165);\n      color: var(--error-color);\n    }\n\n    .alert-success {\n      background-color: rgb(240 253 244);\n      border: 1px solid rgb(167 243 208);\n      color: var(--success-color);\n    }\n\n    .loading-spinner {\n      display: inline-block;\n      width: 16px;\n      height: 16px;\n      border: 2px solid transparent;\n      border-top: 2px solid currentColor;\n      border-radius: 50%;\n      animation: spin 1s linear infinite;\n      margin-right: var(--spacing-2);\n    }\n\n    @keyframes spin {\n      0% { transform: rotate(0deg); }\n      100% { transform: rotate(360deg); }\n    }\n\n    .terms {\n      font-size: var(--font-size-sm);\n      color: var(--gray-600);\n      margin-bottom: var(--spacing-6);\n      line-height: 1.5;\n    }\n\n    .terms a {\n      color: var(--primary-color);\n      text-decoration: none;\n    }\n\n    .terms a:hover {\n      text-decoration: underline;\n    }\n\n    @media (max-width: 640px) {\n      .register-container {\n        margin: var(--spacing-6) auto;\n      }\n\n      .register-card {\n        padding: var(--spacing-6);\n      }\n\n      .form-row {\n        grid-template-columns: 1fr;\n      }\n    }\n  `;\n\n  static properties = {\n    loading: { type: Boolean },\n    error: { type: String },\n    success: { type: String },\n    formData: { type: Object },\n    fieldErrors: { type: Object }\n  };\n\n  constructor() {\n    super();\n    this.loading = false;\n    this.error = '';\n    this.success = '';\n    this.formData = {\n      name: '',\n      email: '',\n      phone: '',\n      password: '',\n      confirmPassword: ''\n    };\n    this.fieldErrors = {};\n  }\n\n  handleInputChange(event) {\n    const { name, value } = event.target;\n    this.formData = {\n      ...this.formData,\n      [name]: value\n    };\n    \n    // Очищаем ошибки при изменении полей\n    if (this.error) {\n      this.error = '';\n    }\n    \n    if (this.fieldErrors[name]) {\n      this.fieldErrors = {\n        ...this.fieldErrors,\n        [name]: ''\n      };\n    }\n\n    this.requestUpdate();\n  }\n\n  validateField(name, value) {\n    switch (name) {\n      case 'name':\n        return value.trim().length >= 2 ? '' : 'Имя должно содержать минимум 2 символа';\n      \n      case 'email':\n        const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n        return emailRegex.test(value) ? '' : 'Некорректный формат email';\n      \n      case 'phone':\n        const phoneRegex = /^(\\+7|8)?[\\s\\-]?\\(?[489][0-9]{2}\\)?[\\s\\-]?[0-9]{3}[\\s\\-]?[0-9]{2}[\\s\\-]?[0-9]{2}$/;\n        return phoneRegex.test(value.replace(/\\s/g, '')) ? '' : 'Некорректный формат телефона';\n      \n      case 'password':\n        return value.length >= 6 ? '' : 'Пароль должен содержать минимум 6 символов';\n      \n      case 'confirmPassword':\n        return value === this.formData.password ? '' : 'Пароли не совпадают';\n      \n      default:\n        return '';\n    }\n  }\n\n  getPasswordStrength(password) {\n    if (!password) return { strength: 0, text: '' };\n    \n    let score = 0;\n    \n    // Длина\n    if (password.length >= 8) score += 1;\n    if (password.length >= 12) score += 1;\n    \n    // Содержит цифры\n    if (/\\d/.test(password)) score += 1;\n    \n    // Содержит строчные буквы\n    if (/[a-z]/.test(password)) score += 1;\n    \n    // Содержит заглавные буквы\n    if (/[A-Z]/.test(password)) score += 1;\n    \n    // Содержит специальные символы\n    if (/[^A-Za-z0-9]/.test(password)) score += 1;\n\n    if (score <= 2) return { strength: 1, text: 'Слабый пароль' };\n    if (score <= 4) return { strength: 2, text: 'Средний пароль' };\n    return { strength: 3, text: 'Сильный пароль' };\n  }\n\n  async handleSubmit(event) {\n    event.preventDefault();\n    \n    if (this.loading) return;\n\n    // Валидация всех полей\n    const errors = {};\n    Object.keys(this.formData).forEach(field => {\n      const error = this.validateField(field, this.formData[field]);\n      if (error) errors[field] = error;\n    });\n\n    if (Object.keys(errors).length > 0) {\n      this.fieldErrors = errors;\n      this.error = 'Пожалуйста, исправьте ошибки в форме';\n      return;\n    }\n\n    this.loading = true;\n    this.error = '';\n    this.success = '';\n    this.fieldErrors = {};\n\n    try {\n      const result = await authService.register(this.formData);\n      \n      if (result.success) {\n        this.success = result.message;\n        \n        // Отправляем событие успешной регистрации\n        this.dispatchEvent(new CustomEvent('user-login', {\n          detail: { user: result.user }\n        }));\n\n        // Перенаправляем на главную страницу через небольшую задержку\n        setTimeout(() => {\n          window.history.pushState({}, '', '/');\n          window.dispatchEvent(new PopStateEvent('popstate'));\n        }, 1500);\n        \n      } else {\n        this.error = result.error;\n      }\n    } catch (error) {\n      this.error = 'Произошла ошибка при регистрации';\n      console.error('Registration error:', error);\n    } finally {\n      this.loading = false;\n    }\n  }\n\n  render() {\n    const passwordStrength = this.getPasswordStrength(this.formData.password);\n    \n    return html`\n      <div class=\"register-container\">\n        <div class=\"register-card\">\n          <div class=\"register-header\">\n            <h1 class=\"register-title\">Регистрация</h1>\n            <p class=\"register-subtitle\">Создайте аккаунт, чтобы записываться на тренировки</p>\n          </div>\n\n          ${this.error ? html`\n            <div class=\"alert alert-error\">\n              ${this.error}\n            </div>\n          ` : ''}\n\n          ${this.success ? html`\n            <div class=\"alert alert-success\">\n              ${this.success}\n            </div>\n          ` : ''}\n\n          <form @submit=${this.handleSubmit}>\n            <div class=\"form-group\">\n              <label class=\"form-label\" for=\"name\">Полное имя</label>\n              <input\n                type=\"text\"\n                id=\"name\"\n                name=\"name\"\n                class=\"form-input ${this.fieldErrors.name ? 'error' : ''}\"\n                .value=${this.formData.name}\n                @input=${this.handleInputChange}\n                required\n                autocomplete=\"name\"\n                placeholder=\"Иван Иванов\"\n              />\n              ${this.fieldErrors.name ? html`\n                <div class=\"form-error\">${this.fieldErrors.name}</div>\n              ` : ''}\n            </div>\n\n            <div class=\"form-row\">\n              <div class=\"form-group\">\n                <label class=\"form-label\" for=\"email\">Email</label>\n                <input\n                  type=\"email\"\n                  id=\"email\"\n                  name=\"email\"\n                  class=\"form-input ${this.fieldErrors.email ? 'error' : ''}\"\n                  .value=${this.formData.email}\n                  @input=${this.handleInputChange}\n                  required\n                  autocomplete=\"email\"\n                  placeholder=\"<EMAIL>\"\n                />\n                ${this.fieldErrors.email ? html`\n                  <div class=\"form-error\">${this.fieldErrors.email}</div>\n                ` : ''}\n              </div>\n\n              <div class=\"form-group\">\n                <label class=\"form-label\" for=\"phone\">Телефон</label>\n                <input\n                  type=\"tel\"\n                  id=\"phone\"\n                  name=\"phone\"\n                  class=\"form-input ${this.fieldErrors.phone ? 'error' : ''}\"\n                  .value=${this.formData.phone}\n                  @input=${this.handleInputChange}\n                  required\n                  autocomplete=\"tel\"\n                  placeholder=\"+7 (999) 123-45-67\"\n                />\n                ${this.fieldErrors.phone ? html`\n                  <div class=\"form-error\">${this.fieldErrors.phone}</div>\n                ` : ''}\n              </div>\n            </div>\n\n            <div class=\"form-group\">\n              <label class=\"form-label\" for=\"password\">Пароль</label>\n              <input\n                type=\"password\"\n                id=\"password\"\n                name=\"password\"\n                class=\"form-input ${this.fieldErrors.password ? 'error' : ''}\"\n                .value=${this.formData.password}\n                @input=${this.handleInputChange}\n                required\n                autocomplete=\"new-password\"\n                placeholder=\"Минимум 6 символов\"\n              />\n              ${this.fieldErrors.password ? html`\n                <div class=\"form-error\">${this.fieldErrors.password}</div>\n              ` : ''}\n              \n              ${this.formData.password ? html`\n                <div class=\"password-strength\">\n                  <div class=\"strength-bar\">\n                    <div class=\"strength-fill strength-${passwordStrength.strength === 1 ? 'weak' : passwordStrength.strength === 2 ? 'medium' : 'strong'}\" \n                         style=\"width: ${(passwordStrength.strength / 3) * 100}%\"></div>\n                  </div>\n                  <div class=\"strength-text\">${passwordStrength.text}</div>\n                </div>\n              ` : ''}\n            </div>\n\n            <div class=\"form-group\">\n              <label class=\"form-label\" for=\"confirmPassword\">Подтверждение пароля</label>\n              <input\n                type=\"password\"\n                id=\"confirmPassword\"\n                name=\"confirmPassword\"\n                class=\"form-input ${this.fieldErrors.confirmPassword ? 'error' : ''}\"\n                .value=${this.formData.confirmPassword}\n                @input=${this.handleInputChange}\n                required\n                autocomplete=\"new-password\"\n                placeholder=\"Повторите пароль\"\n              />\n              ${this.fieldErrors.confirmPassword ? html`\n                <div class=\"form-error\">${this.fieldErrors.confirmPassword}</div>\n              ` : ''}\n            </div>\n\n            <div class=\"terms\">\n              Регистрируясь, вы соглашаетесь с <a href=\"#\">Условиями использования</a> \n              и <a href=\"#\">Политикой конфиденциальности</a>\n            </div>\n\n            <button type=\"submit\" class=\"register-button\" ?disabled=${this.loading}>\n              ${this.loading ? html`\n                <span class=\"loading-spinner\"></span>\n                Регистрация...\n              ` : 'Зарегистрироваться'}\n            </button>\n          </form>\n\n          <div class=\"register-footer\">\n            <p>Уже есть аккаунт? <a href=\"/login\">Войти</a></p>\n          </div>\n        </div>\n      </div>\n    `;\n  }\n}\n\ncustomElements.define('register-page', RegisterPage);\n", "import { LitElement, html, css } from '/node_modules/lit/index.js';\nimport { databaseService } from './database.js';\n\nclass AdminPage extends LitElement {\n  static styles = css`\n    :host {\n      display: block;\n    }\n\n    .admin-container {\n      max-width: var(--container-xl);\n      margin: 0 auto;\n      padding: var(--spacing-6) var(--spacing-4);\n    }\n\n    .admin-header {\n      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));\n      color: white;\n      padding: var(--spacing-8);\n      border-radius: var(--radius-xl);\n      margin-bottom: var(--spacing-8);\n      text-align: center;\n    }\n\n    .admin-title {\n      font-size: var(--font-size-3xl);\n      font-weight: 700;\n      margin-bottom: var(--spacing-2);\n    }\n\n    .admin-subtitle {\n      font-size: var(--font-size-lg);\n      opacity: 0.9;\n    }\n\n    .admin-tabs {\n      display: flex;\n      background: white;\n      border-radius: var(--radius-xl);\n      box-shadow: var(--shadow-sm);\n      margin-bottom: var(--spacing-8);\n      overflow: hidden;\n    }\n\n    .tab-button {\n      flex: 1;\n      padding: var(--spacing-4) var(--spacing-6);\n      background: none;\n      border: none;\n      font-size: var(--font-size-base);\n      font-weight: 500;\n      color: var(--gray-600);\n      cursor: pointer;\n      transition: all var(--transition-fast);\n      position: relative;\n    }\n\n    .tab-button.active {\n      color: var(--primary-color);\n      background-color: rgb(37 99 235 / 0.05);\n    }\n\n    .tab-button.active::after {\n      content: '';\n      position: absolute;\n      bottom: 0;\n      left: 0;\n      right: 0;\n      height: 3px;\n      background: var(--primary-color);\n    }\n\n    .tab-button:hover:not(.active) {\n      background-color: var(--gray-50);\n    }\n\n    .tab-content {\n      background: white;\n      border-radius: var(--radius-xl);\n      box-shadow: var(--shadow-sm);\n      padding: var(--spacing-8);\n    }\n\n    .section-title {\n      font-size: var(--font-size-xl);\n      font-weight: 700;\n      color: var(--gray-800);\n      margin-bottom: var(--spacing-6);\n      display: flex;\n      align-items: center;\n      gap: var(--spacing-3);\n    }\n\n    .form-group {\n      margin-bottom: var(--spacing-6);\n    }\n\n    .form-label {\n      display: block;\n      font-weight: 500;\n      color: var(--gray-700);\n      margin-bottom: var(--spacing-2);\n    }\n\n    .form-input,\n    .form-textarea {\n      width: 100%;\n      padding: var(--spacing-3);\n      border: 2px solid var(--gray-200);\n      border-radius: var(--radius-lg);\n      font-size: var(--font-size-base);\n      transition: border-color var(--transition-fast);\n      font-family: inherit;\n    }\n\n    .form-textarea {\n      min-height: 120px;\n      resize: vertical;\n    }\n\n    .form-input:focus,\n    .form-textarea:focus {\n      outline: none;\n      border-color: var(--primary-color);\n      box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);\n    }\n\n    .save-button {\n      background-color: var(--primary-color);\n      color: white;\n      border: none;\n      border-radius: var(--radius-lg);\n      padding: var(--spacing-3) var(--spacing-6);\n      font-size: var(--font-size-base);\n      font-weight: 600;\n      cursor: pointer;\n      transition: all var(--transition-fast);\n      display: flex;\n      align-items: center;\n      gap: var(--spacing-2);\n    }\n\n    .save-button:hover:not(:disabled) {\n      background-color: var(--primary-dark);\n      transform: translateY(-1px);\n      box-shadow: var(--shadow-md);\n    }\n\n    .save-button:disabled {\n      opacity: 0.5;\n      cursor: not-allowed;\n      transform: none;\n    }\n\n    .slides-grid {\n      display: grid;\n      gap: var(--spacing-6);\n    }\n\n    .slide-card {\n      border: 2px solid var(--gray-200);\n      border-radius: var(--radius-lg);\n      padding: var(--spacing-6);\n      transition: border-color var(--transition-fast);\n    }\n\n    .slide-card:hover {\n      border-color: var(--primary-color);\n    }\n\n    .slide-header {\n      display: flex;\n      justify-content: between;\n      align-items: center;\n      margin-bottom: var(--spacing-4);\n    }\n\n    .slide-number {\n      background: var(--primary-color);\n      color: white;\n      width: 32px;\n      height: 32px;\n      border-radius: 50%;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-weight: 600;\n      font-size: var(--font-size-sm);\n    }\n\n    .slide-actions {\n      display: flex;\n      gap: var(--spacing-2);\n    }\n\n    .action-button {\n      padding: var(--spacing-2) var(--spacing-3);\n      border: none;\n      border-radius: var(--radius-md);\n      font-size: var(--font-size-sm);\n      font-weight: 500;\n      cursor: pointer;\n      transition: all var(--transition-fast);\n    }\n\n    .btn-edit {\n      background-color: var(--warning-color);\n      color: white;\n    }\n\n    .btn-edit:hover {\n      background-color: #d97706;\n    }\n\n    .btn-delete {\n      background-color: var(--error-color);\n      color: white;\n    }\n\n    .btn-delete:hover {\n      background-color: #dc2626;\n    }\n\n    .slide-form {\n      display: grid;\n      grid-template-columns: 1fr 1fr;\n      gap: var(--spacing-4);\n    }\n\n    .slide-preview {\n      grid-column: 1 / -1;\n      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));\n      color: white;\n      padding: var(--spacing-6);\n      border-radius: var(--radius-lg);\n      text-align: center;\n      margin-bottom: var(--spacing-4);\n    }\n\n    .preview-title {\n      font-size: var(--font-size-xl);\n      font-weight: 700;\n      margin-bottom: var(--spacing-2);\n    }\n\n    .preview-subtitle {\n      font-size: var(--font-size-base);\n      opacity: 0.9;\n    }\n\n    .stats-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n      gap: var(--spacing-6);\n      margin-bottom: var(--spacing-8);\n    }\n\n    .stat-card {\n      background: white;\n      border-radius: var(--radius-lg);\n      padding: var(--spacing-6);\n      text-align: center;\n      box-shadow: var(--shadow-sm);\n      border: 2px solid var(--gray-100);\n      transition: all var(--transition-fast);\n    }\n\n    .stat-card:hover {\n      border-color: var(--primary-color);\n      transform: translateY(-2px);\n    }\n\n    .stat-icon {\n      width: 60px;\n      height: 60px;\n      margin: 0 auto var(--spacing-3);\n      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));\n      border-radius: 50%;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      color: white;\n      font-size: var(--font-size-xl);\n    }\n\n    .stat-number {\n      font-size: var(--font-size-2xl);\n      font-weight: 700;\n      color: var(--gray-800);\n      margin-bottom: var(--spacing-1);\n    }\n\n    .stat-label {\n      color: var(--gray-600);\n      font-size: var(--font-size-sm);\n    }\n\n    .notification {\n      position: fixed;\n      top: var(--spacing-4);\n      right: var(--spacing-4);\n      padding: var(--spacing-4) var(--spacing-6);\n      border-radius: var(--radius-lg);\n      color: white;\n      font-weight: 500;\n      z-index: 1000;\n      animation: slideIn 0.3s ease-out;\n    }\n\n    .notification.success {\n      background-color: var(--success-color);\n    }\n\n    .notification.error {\n      background-color: var(--error-color);\n    }\n\n    @keyframes slideIn {\n      from {\n        transform: translateX(100%);\n        opacity: 0;\n      }\n      to {\n        transform: translateX(0);\n        opacity: 1;\n      }\n    }\n\n    .loading-spinner {\n      display: inline-block;\n      width: 16px;\n      height: 16px;\n      border: 2px solid transparent;\n      border-top: 2px solid currentColor;\n      border-radius: 50%;\n      animation: spin 1s linear infinite;\n    }\n\n    @keyframes spin {\n      0% { transform: rotate(0deg); }\n      100% { transform: rotate(360deg); }\n    }\n\n    @media (max-width: 768px) {\n      .admin-tabs {\n        flex-direction: column;\n      }\n\n      .slide-form {\n        grid-template-columns: 1fr;\n      }\n\n      .stats-grid {\n        grid-template-columns: repeat(2, 1fr);\n      }\n    }\n\n    @media (max-width: 480px) {\n      .stats-grid {\n        grid-template-columns: 1fr;\n      }\n    }\n  `;\n\n  static properties = {\n    currentUser: { type: Object },\n    activeTab: { type: String },\n    homeContent: { type: Object },\n    slides: { type: Array },\n    loading: { type: Boolean },\n    stats: { type: Object }\n  };\n\n  constructor() {\n    super();\n    this.currentUser = null;\n    this.activeTab = 'dashboard';\n    this.homeContent = {};\n    this.slides = [];\n    this.loading = false;\n    this.stats = {\n      totalUsers: 0,\n      totalBookings: 0,\n      totalTrainers: 0,\n      totalWorkouts: 0\n    };\n    \n    this.loadData();\n  }\n\n  async loadData() {\n    try {\n      this.loading = true;\n      \n      // Загружаем контент главной страницы\n      const content = await databaseService.getContent('home');\n      if (content) {\n        this.homeContent = {\n          title: content.title || '',\n          subtitle: content.subtitle || '',\n          description: content.description || ''\n        };\n      }\n      \n      // Загружаем слайды\n      this.slides = await databaseService.getSlides();\n      \n      // Загружаем статистику (эмуляция)\n      this.stats = {\n        totalUsers: 156,\n        totalBookings: 342,\n        totalTrainers: 8,\n        totalWorkouts: 15\n      };\n      \n    } catch (error) {\n      console.error('Ошибка загрузки данных:', error);\n      this.showNotification('Ошибка загрузки данных', 'error');\n    } finally {\n      this.loading = false;\n    }\n  }\n\n  switchTab(tab) {\n    this.activeTab = tab;\n  }\n\n  async saveHomeContent() {\n    try {\n      this.loading = true;\n      \n      await databaseService.updateContent('home', this.homeContent);\n      this.showNotification('Контент главной страницы сохранен', 'success');\n      \n    } catch (error) {\n      console.error('Ошибка сохранения контента:', error);\n      this.showNotification('Ошибка сохранения контента', 'error');\n    } finally {\n      this.loading = false;\n    }\n  }\n\n  async saveSlide(slideId, slideData) {\n    try {\n      this.loading = true;\n      \n      await databaseService.updateSlide(slideId, slideData);\n      await this.loadData(); // Перезагружаем данные\n      this.showNotification('Слайд сохранен', 'success');\n      \n    } catch (error) {\n      console.error('Ошибка сохранения слайда:', error);\n      this.showNotification('Ошибка сохранения слайда', 'error');\n    } finally {\n      this.loading = false;\n    }\n  }\n\n  handleContentInput(field, value) {\n    this.homeContent = {\n      ...this.homeContent,\n      [field]: value\n    };\n  }\n\n  handleSlideInput(slideId, field, value) {\n    this.slides = this.slides.map(slide => \n      slide._id === slideId \n        ? { ...slide, [field]: value }\n        : slide\n    );\n  }\n\n  showNotification(message, type = 'info') {\n    const notification = document.createElement('div');\n    notification.className = `notification ${type}`;\n    notification.textContent = message;\n    document.body.appendChild(notification);\n    \n    setTimeout(() => {\n      notification.remove();\n    }, 5000);\n  }\n\n  renderDashboard() {\n    return html`\n      <div class=\"section-title\">\n        📊 Панель управления\n      </div>\n      \n      <div class=\"stats-grid\">\n        <div class=\"stat-card\">\n          <div class=\"stat-icon\">👥</div>\n          <div class=\"stat-number\">${this.stats.totalUsers}</div>\n          <div class=\"stat-label\">Пользователей</div>\n        </div>\n        <div class=\"stat-card\">\n          <div class=\"stat-icon\">📅</div>\n          <div class=\"stat-number\">${this.stats.totalBookings}</div>\n          <div class=\"stat-label\">Записей на тренировки</div>\n        </div>\n        <div class=\"stat-card\">\n          <div class=\"stat-icon\">👨‍🏫</div>\n          <div class=\"stat-number\">${this.stats.totalTrainers}</div>\n          <div class=\"stat-label\">Тренеров</div>\n        </div>\n        <div class=\"stat-card\">\n          <div class=\"stat-icon\">🏋️‍♀️</div>\n          <div class=\"stat-number\">${this.stats.totalWorkouts}</div>\n          <div class=\"stat-label\">Видов тренировок</div>\n        </div>\n      </div>\n      \n      <div class=\"section-title\">\n        📈 Быстрые действия\n      </div>\n      \n      <div style=\"display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: var(--spacing-4);\">\n        <button class=\"save-button\" @click=${() => this.switchTab('content')}>\n          ✏️ Редактировать главную страницу\n        </button>\n        <button class=\"save-button\" @click=${() => this.switchTab('slider')}>\n          🖼️ Управление слайдером\n        </button>\n        <button class=\"save-button\" @click=${() => window.open('/schedule', '_blank')}>\n          📅 Посмотреть расписание\n        </button>\n        <button class=\"save-button\" @click=${() => window.open('/trainers', '_blank')}>\n          👨‍🏫 Управление тренерами\n        </button>\n      </div>\n    `;\n  }\n\n  renderContentEditor() {\n    return html`\n      <div class=\"section-title\">\n        ✏️ Редактирование главной страницы\n      </div>\n      \n      <form @submit=${(e) => { e.preventDefault(); this.saveHomeContent(); }}>\n        <div class=\"form-group\">\n          <label class=\"form-label\">Заголовок</label>\n          <input\n            type=\"text\"\n            class=\"form-input\"\n            .value=${this.homeContent.title || ''}\n            @input=${(e) => this.handleContentInput('title', e.target.value)}\n            placeholder=\"Добро пожаловать в FitStudio\"\n          />\n        </div>\n        \n        <div class=\"form-group\">\n          <label class=\"form-label\">Подзаголовок</label>\n          <input\n            type=\"text\"\n            class=\"form-input\"\n            .value=${this.homeContent.subtitle || ''}\n            @input=${(e) => this.handleContentInput('subtitle', e.target.value)}\n            placeholder=\"Ваш путь к здоровью и красоте\"\n          />\n        </div>\n        \n        <div class=\"form-group\">\n          <label class=\"form-label\">Описание</label>\n          <textarea\n            class=\"form-textarea\"\n            .value=${this.homeContent.description || ''}\n            @input=${(e) => this.handleContentInput('description', e.target.value)}\n            placeholder=\"Современная фитнес-студия с профессиональными тренерами...\"\n          ></textarea>\n        </div>\n        \n        <button type=\"submit\" class=\"save-button\" ?disabled=${this.loading}>\n          ${this.loading ? html`<span class=\"loading-spinner\"></span>` : '💾'}\n          Сохранить изменения\n        </button>\n      </form>\n    `;\n  }\n\n  renderSliderEditor() {\n    return html`\n      <div class=\"section-title\">\n        🖼️ Управление слайдером\n      </div>\n      \n      <div class=\"slides-grid\">\n        ${this.slides.map((slide, index) => html`\n          <div class=\"slide-card\">\n            <div class=\"slide-header\">\n              <div class=\"slide-number\">${index + 1}</div>\n              <div class=\"slide-actions\">\n                <button class=\"action-button btn-edit\" \n                        @click=${() => this.saveSlide(slide._id, slide)}>\n                  💾 Сохранить\n                </button>\n              </div>\n            </div>\n            \n            <div class=\"slide-preview\">\n              <div class=\"preview-title\">${slide.title || 'Заголовок слайда'}</div>\n              <div class=\"preview-subtitle\">${slide.subtitle || 'Подзаголовок слайда'}</div>\n            </div>\n            \n            <div class=\"slide-form\">\n              <div class=\"form-group\">\n                <label class=\"form-label\">Заголовок</label>\n                <input\n                  type=\"text\"\n                  class=\"form-input\"\n                  .value=${slide.title || ''}\n                  @input=${(e) => this.handleSlideInput(slide._id, 'title', e.target.value)}\n                  placeholder=\"Заголовок слайда\"\n                />\n              </div>\n              \n              <div class=\"form-group\">\n                <label class=\"form-label\">Подзаголовок</label>\n                <input\n                  type=\"text\"\n                  class=\"form-input\"\n                  .value=${slide.subtitle || ''}\n                  @input=${(e) => this.handleSlideInput(slide._id, 'subtitle', e.target.value)}\n                  placeholder=\"Подзаголовок слайда\"\n                />\n              </div>\n              \n              <div class=\"form-group\">\n                <label class=\"form-label\">Порядок отображения</label>\n                <input\n                  type=\"number\"\n                  class=\"form-input\"\n                  .value=${slide.order || 1}\n                  @input=${(e) => this.handleSlideInput(slide._id, 'order', parseInt(e.target.value))}\n                  min=\"1\"\n                />\n              </div>\n              \n              <div class=\"form-group\">\n                <label class=\"form-label\">Статус</label>\n                <select\n                  class=\"form-input\"\n                  .value=${slide.active ? 'true' : 'false'}\n                  @change=${(e) => this.handleSlideInput(slide._id, 'active', e.target.value === 'true')}\n                >\n                  <option value=\"true\">Активен</option>\n                  <option value=\"false\">Неактивен</option>\n                </select>\n              </div>\n            </div>\n          </div>\n        `)}\n      </div>\n    `;\n  }\n\n  render() {\n    // Проверка прав доступа\n    if (!this.currentUser || this.currentUser.role !== 'admin') {\n      return html`\n        <div class=\"admin-container\">\n          <div style=\"text-align: center; padding: var(--spacing-12);\">\n            <h2>Доступ запрещен</h2>\n            <p>У вас нет прав для доступа к админ-панели</p>\n            <a href=\"/\" class=\"save-button\" style=\"margin-top: var(--spacing-4); display: inline-flex;\">\n              Вернуться на главную\n            </a>\n          </div>\n        </div>\n      `;\n    }\n\n    return html`\n      <div class=\"admin-container\">\n        <div class=\"admin-header\">\n          <h1 class=\"admin-title\">Панель администратора</h1>\n          <p class=\"admin-subtitle\">Управление контентом и настройками FitStudio</p>\n        </div>\n\n        <div class=\"admin-tabs\">\n          <button \n            class=\"tab-button ${this.activeTab === 'dashboard' ? 'active' : ''}\"\n            @click=${() => this.switchTab('dashboard')}\n          >\n            📊 Панель управления\n          </button>\n          <button \n            class=\"tab-button ${this.activeTab === 'content' ? 'active' : ''}\"\n            @click=${() => this.switchTab('content')}\n          >\n            ✏️ Главная страница\n          </button>\n          <button \n            class=\"tab-button ${this.activeTab === 'slider' ? 'active' : ''}\"\n            @click=${() => this.switchTab('slider')}\n          >\n            🖼️ Слайдер\n          </button>\n        </div>\n\n        <div class=\"tab-content\">\n          ${this.activeTab === 'dashboard' ? this.renderDashboard() : ''}\n          ${this.activeTab === 'content' ? this.renderContentEditor() : ''}\n          ${this.activeTab === 'slider' ? this.renderSliderEditor() : ''}\n        </div>\n      </div>\n    `;\n  }\n}\n\ncustomElements.define('admin-page', AdminPage);\n", "import { LitElement, html, css } from '/node_modules/lit/index.js';\n\nclass NotFoundPage extends LitElement {\n  static styles = css`\n    :host {\n      display: block;\n    }\n\n    .not-found {\n      text-align: center;\n      padding: var(--spacing-20) var(--spacing-4);\n      max-width: var(--container-md);\n      margin: 0 auto;\n    }\n\n    .error-code {\n      font-size: 8rem;\n      font-weight: 700;\n      color: var(--primary-color);\n      margin-bottom: var(--spacing-4);\n      line-height: 1;\n    }\n\n    .error-title {\n      font-size: var(--font-size-3xl);\n      font-weight: 600;\n      color: var(--gray-800);\n      margin-bottom: var(--spacing-4);\n    }\n\n    .error-message {\n      font-size: var(--font-size-lg);\n      color: var(--gray-600);\n      margin-bottom: var(--spacing-8);\n      line-height: 1.6;\n    }\n\n    .error-actions {\n      display: flex;\n      gap: var(--spacing-4);\n      justify-content: center;\n      flex-wrap: wrap;\n    }\n\n    .illustration {\n      width: 200px;\n      height: 200px;\n      margin: 0 auto var(--spacing-8);\n      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));\n      border-radius: 50%;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-size: 4rem;\n      color: white;\n    }\n\n    @media (max-width: 768px) {\n      .error-code {\n        font-size: 6rem;\n      }\n\n      .error-title {\n        font-size: var(--font-size-2xl);\n      }\n\n      .error-message {\n        font-size: var(--font-size-base);\n      }\n\n      .error-actions {\n        flex-direction: column;\n        align-items: center;\n      }\n\n      .error-actions .btn {\n        width: 100%;\n        max-width: 300px;\n      }\n    }\n  `;\n\n  static properties = {\n    message: { type: String }\n  };\n\n  constructor() {\n    super();\n    this.message = '';\n  }\n\n  render() {\n    const isAccessDenied = this.message === 'Доступ запрещен';\n    \n    return html`\n      <div class=\"not-found\">\n        <div class=\"illustration\">\n          ${isAccessDenied ? '🔒' : '🤔'}\n        </div>\n        \n        <div class=\"error-code\">\n          ${isAccessDenied ? '403' : '404'}\n        </div>\n        \n        <h1 class=\"error-title\">\n          ${isAccessDenied ? 'Доступ запрещен' : 'Страница не найдена'}\n        </h1>\n        \n        <p class=\"error-message\">\n          ${isAccessDenied \n            ? 'У вас нет прав для доступа к этой странице. Обратитесь к администратору для получения доступа.'\n            : 'Извините, но страница, которую вы ищете, не существует. Возможно, она была перемещена или удалена.'\n          }\n        </p>\n        \n        <div class=\"error-actions\">\n          <a href=\"/\" class=\"btn btn-primary\">\n            Вернуться на главную\n          </a>\n          ${!isAccessDenied ? html`\n            <button class=\"btn btn-outline\" @click=${() => window.history.back()}>\n              Назад\n            </button>\n          ` : ''}\n        </div>\n      </div>\n    `;\n  }\n}\n\ncustomElements.define('not-found-page', NotFoundPage);\n", "import { LitElement, html, css } from '/node_modules/lit/index.js';\nimport './home-page.js';\nimport './schedule-page.js';\nimport './trainers-page.js';\nimport './about-page.js';\nimport './login-page.js';\nimport './register-page.js';\nimport './admin-page.js';\nimport './not-found-page.js';\n\nclass AppRouter extends LitElement {\n  static styles = css`\n    :host {\n      display: block;\n    }\n\n    .page-container {\n      min-height: 60vh;\n      animation: fadeIn 0.3s ease-in-out;\n    }\n\n    @keyframes fadeIn {\n      from {\n        opacity: 0;\n        transform: translateY(20px);\n      }\n      to {\n        opacity: 1;\n        transform: translateY(0);\n      }\n    }\n  `;\n\n  static properties = {\n    currentRoute: { type: String },\n    currentUser: { type: Object }\n  };\n\n  constructor() {\n    super();\n    this.currentRoute = 'home';\n    this.currentUser = null;\n  }\n\n  handleUserLogin(event) {\n    this.dispatchEvent(new CustomEvent('user-login', {\n      detail: event.detail\n    }));\n  }\n\n  handleNavigation(event) {\n    this.dispatchEvent(new CustomEvent('navigate', {\n      detail: event.detail\n    }));\n  }\n\n  renderPage() {\n    // Проверка доступа к админ-страницам\n    if (this.currentRoute === 'admin' && (!this.currentUser || this.currentUser.role !== 'admin')) {\n      return html`<not-found-page message=\"Доступ запрещен\"></not-found-page>`;\n    }\n\n    // Перенаправление авторизованных пользователей с страниц входа/регистрации\n    if ((this.currentRoute === 'login' || this.currentRoute === 'register') && this.currentUser) {\n      setTimeout(() => {\n        this.dispatchEvent(new CustomEvent('navigate', {\n          detail: { path: '/' }\n        }));\n      }, 0);\n      return html`<home-page .currentUser=${this.currentUser}></home-page>`;\n    }\n\n    switch (this.currentRoute) {\n      case 'home':\n        return html`<home-page .currentUser=${this.currentUser}></home-page>`;\n      \n      case 'schedule':\n        return html`<schedule-page .currentUser=${this.currentUser}></schedule-page>`;\n      \n      case 'trainers':\n        return html`<trainers-page></trainers-page>`;\n      \n      case 'about':\n        return html`<about-page></about-page>`;\n      \n      case 'login':\n        return html`<login-page @user-login=${this.handleUserLogin}></login-page>`;\n      \n      case 'register':\n        return html`<register-page @user-login=${this.handleUserLogin}></register-page>`;\n      \n      case 'admin':\n        return html`<admin-page .currentUser=${this.currentUser}></admin-page>`;\n      \n      default:\n        return html`<not-found-page></not-found-page>`;\n    }\n  }\n\n  render() {\n    return html`\n      <div class=\"page-container\">\n        ${this.renderPage()}\n      </div>\n    `;\n  }\n}\n\ncustomElements.define('app-router', AppRouter);\n", "import { LitElement, html, css } from '/node_modules/lit/index.js';\n\nclass AppFooter extends LitElement {\n  static styles = css`\n    :host {\n      display: block;\n      background-color: var(--gray-800);\n      color: var(--gray-300);\n      margin-top: auto;\n    }\n\n    .footer {\n      max-width: var(--container-xl);\n      margin: 0 auto;\n      padding: var(--spacing-12) var(--spacing-4) var(--spacing-6);\n    }\n\n    .footer-content {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n      gap: var(--spacing-8);\n      margin-bottom: var(--spacing-8);\n    }\n\n    .footer-section h3 {\n      color: white;\n      font-size: var(--font-size-lg);\n      font-weight: 600;\n      margin-bottom: var(--spacing-4);\n    }\n\n    .footer-section p,\n    .footer-section li {\n      color: var(--gray-400);\n      line-height: 1.6;\n      margin-bottom: var(--spacing-2);\n    }\n\n    .footer-section ul {\n      list-style: none;\n      padding: 0;\n      margin: 0;\n    }\n\n    .footer-section a {\n      color: var(--gray-400);\n      text-decoration: none;\n      transition: color var(--transition-fast);\n    }\n\n    .footer-section a:hover {\n      color: var(--primary-light);\n    }\n\n    .contact-info {\n      display: flex;\n      flex-direction: column;\n      gap: var(--spacing-3);\n    }\n\n    .contact-item {\n      display: flex;\n      align-items: center;\n      gap: var(--spacing-3);\n    }\n\n    .contact-icon {\n      width: 20px;\n      height: 20px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      background-color: var(--primary-color);\n      border-radius: var(--radius-md);\n      color: white;\n      font-size: var(--font-size-sm);\n    }\n\n    .social-links {\n      display: flex;\n      gap: var(--spacing-4);\n      margin-top: var(--spacing-4);\n    }\n\n    .social-link {\n      width: 40px;\n      height: 40px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      background-color: var(--gray-700);\n      border-radius: var(--radius-lg);\n      color: var(--gray-400);\n      text-decoration: none;\n      transition: all var(--transition-fast);\n    }\n\n    .social-link:hover {\n      background-color: var(--primary-color);\n      color: white;\n      transform: translateY(-2px);\n    }\n\n    .footer-bottom {\n      border-top: 1px solid var(--gray-700);\n      padding-top: var(--spacing-6);\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      flex-wrap: wrap;\n      gap: var(--spacing-4);\n    }\n\n    .footer-bottom p {\n      margin: 0;\n      color: var(--gray-500);\n      font-size: var(--font-size-sm);\n    }\n\n    .footer-links {\n      display: flex;\n      gap: var(--spacing-6);\n      list-style: none;\n      margin: 0;\n      padding: 0;\n    }\n\n    .footer-links a {\n      color: var(--gray-500);\n      text-decoration: none;\n      font-size: var(--font-size-sm);\n      transition: color var(--transition-fast);\n    }\n\n    .footer-links a:hover {\n      color: var(--primary-light);\n    }\n\n    @media (max-width: 768px) {\n      .footer-content {\n        grid-template-columns: 1fr;\n        gap: var(--spacing-6);\n      }\n\n      .footer-bottom {\n        flex-direction: column;\n        text-align: center;\n      }\n\n      .footer-links {\n        justify-content: center;\n      }\n    }\n  `;\n\n  render() {\n    return html`\n      <footer class=\"footer\">\n        <div class=\"footer-content\">\n          <div class=\"footer-section\">\n            <h3>FitStudio</h3>\n            <p>Современная фитнес-студия с профессиональными тренерами и разнообразными программами тренировок. Мы поможем вам достичь ваших целей в фитнесе.</p>\n            <div class=\"social-links\">\n              <a href=\"#\" class=\"social-link\" title=\"Instagram\">📷</a>\n              <a href=\"#\" class=\"social-link\" title=\"Facebook\">📘</a>\n              <a href=\"#\" class=\"social-link\" title=\"VKontakte\">🔵</a>\n              <a href=\"#\" class=\"social-link\" title=\"Telegram\">✈️</a>\n            </div>\n          </div>\n\n          <div class=\"footer-section\">\n            <h3>Быстрые ссылки</h3>\n            <ul>\n              <li><a href=\"/\">Главная</a></li>\n              <li><a href=\"/schedule\">Расписание</a></li>\n              <li><a href=\"/trainers\">Тренеры</a></li>\n              <li><a href=\"/about\">О студии</a></li>\n            </ul>\n          </div>\n\n          <div class=\"footer-section\">\n            <h3>Программы</h3>\n            <ul>\n              <li><a href=\"/schedule\">Йога</a></li>\n              <li><a href=\"/schedule\">Пилатес</a></li>\n              <li><a href=\"/schedule\">Функциональный тренинг</a></li>\n              <li><a href=\"/schedule\">Танцевальная аэробика</a></li>\n            </ul>\n          </div>\n\n          <div class=\"footer-section\">\n            <h3>Контакты</h3>\n            <div class=\"contact-info\">\n              <div class=\"contact-item\">\n                <div class=\"contact-icon\">📍</div>\n                <span>г. Москва, ул. Спортивная, д. 15</span>\n              </div>\n              <div class=\"contact-item\">\n                <div class=\"contact-icon\">📞</div>\n                <span>+7 (495) 123-45-67</span>\n              </div>\n              <div class=\"contact-item\">\n                <div class=\"contact-icon\">✉️</div>\n                <span><EMAIL></span>\n              </div>\n              <div class=\"contact-item\">\n                <div class=\"contact-icon\">🕒</div>\n                <span>Пн-Пт: 07:00-23:00<br>Сб-Вс: 09:00-21:00</span>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"footer-bottom\">\n          <p>&copy; 2025 FitStudio. Все права защищены.</p>\n          <ul class=\"footer-links\">\n            <li><a href=\"#\">Политика конфиденциальности</a></li>\n            <li><a href=\"#\">Условия использования</a></li>\n            <li><a href=\"#\">Публичная оферта</a></li>\n          </ul>\n        </div>\n      </footer>\n    `;\n  }\n}\n\ncustomElements.define('app-footer', AppFooter);\n", "import { LitElement, html, css } from '/node_modules/lit/index.js';\nimport { databaseService } from './database.js';\nimport './app-header.js';\nimport './app-router.js';\nimport './app-footer.js';\n\nclass FitnessApp extends LitElement {\n  static styles = css`\n    :host {\n      display: block;\n      min-height: 100vh;\n      background-color: var(--gray-50);\n    }\n\n    .app-container {\n      display: flex;\n      flex-direction: column;\n      min-height: 100vh;\n    }\n\n    main {\n      flex: 1;\n      padding: var(--spacing-4) 0;\n    }\n\n    .loading-overlay {\n      position: fixed;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      background: rgba(255, 255, 255, 0.9);\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      z-index: 9999;\n      transition: opacity 0.3s ease;\n    }\n\n    .loading-overlay.hidden {\n      opacity: 0;\n      pointer-events: none;\n    }\n\n    .loading-spinner {\n      text-align: center;\n    }\n\n    .spinner {\n      width: 40px;\n      height: 40px;\n      border: 4px solid var(--gray-200);\n      border-top: 4px solid var(--primary-color);\n      border-radius: 50%;\n      animation: spin 1s linear infinite;\n      margin: 0 auto 1rem;\n    }\n\n    @keyframes spin {\n      0% { transform: rotate(0deg); }\n      100% { transform: rotate(360deg); }\n    }\n  `;\n\n  static properties = {\n    loading: { type: Boolean },\n    currentUser: { type: Object },\n    currentRoute: { type: String }\n  };\n\n  constructor() {\n    super();\n    this.loading = true;\n    this.currentUser = null;\n    this.currentRoute = 'home';\n    \n    console.log('App: Constructor called');\n    this.initializeApp();\n  }\n\n  async initializeApp() {\n    try {\n      // Инициализация базы данных\n      await this.initializeDatabase();\n      \n      // Проверка сохраненной сессии пользователя\n      this.checkUserSession();\n      \n      // Настройка маршрутизации\n      this.setupRouting();\n      \n      // Скрытие индикатора загрузки\n      setTimeout(() => {\n        this.loading = false;\n        const loadingElement = document.getElementById('loading');\n        if (loadingElement) {\n          loadingElement.classList.add('hidden');\n        }\n      }, 1000);\n      \n    } catch (error) {\n      console.error('Ошибка инициализации приложения:', error);\n      this.loading = false;\n    }\n  }\n\n  async initializeDatabase() {\n    // База данных инициализируется автоматически при создании экземпляра\n    console.log('База данных инициализирована');\n  }\n\n  checkUserSession() {\n    // Проверяем сохраненную сессию в localStorage\n    const savedUser = localStorage.getItem('currentUser');\n    if (savedUser) {\n      try {\n        this.currentUser = JSON.parse(savedUser);\n      } catch (error) {\n        console.error('Ошибка восстановления сессии:', error);\n        localStorage.removeItem('currentUser');\n      }\n    }\n  }\n\n  setupRouting() {\n    // Обработка изменения URL\n    window.addEventListener('popstate', () => {\n      this.updateRoute();\n    });\n    \n    // Обработка кликов по ссылкам\n    document.addEventListener('click', (e) => {\n      if (e.target.matches('a[href^=\"/\"]') || e.target.closest('a[href^=\"/\"]')) {\n        e.preventDefault();\n        const link = e.target.matches('a') ? e.target : e.target.closest('a');\n        this.navigateTo(link.getAttribute('href'));\n      }\n    });\n    \n    // Установка начального маршрута\n    this.updateRoute();\n  }\n\n  updateRoute() {\n    const path = window.location.pathname;\n    this.currentRoute = path === '/' ? 'home' : path.slice(1);\n    this.requestUpdate();\n  }\n\n  navigateTo(path) {\n    window.history.pushState({}, '', path);\n    this.updateRoute();\n  }\n\n  handleUserLogin(event) {\n    this.currentUser = event.detail.user;\n    localStorage.setItem('currentUser', JSON.stringify(this.currentUser));\n    this.requestUpdate();\n  }\n\n  handleUserLogout() {\n    this.currentUser = null;\n    localStorage.removeItem('currentUser');\n    this.navigateTo('/');\n    this.requestUpdate();\n  }\n\n  render() {\n    return html`\n      <div class=\"app-container\">\n        <app-header \n          .currentUser=${this.currentUser}\n          @user-logout=${this.handleUserLogout}\n        ></app-header>\n        \n        <main>\n          <app-router \n            .currentRoute=${this.currentRoute}\n            .currentUser=${this.currentUser}\n            @user-login=${this.handleUserLogin}\n            @navigate=${(e) => this.navigateTo(e.detail.path)}\n          ></app-router>\n        </main>\n        \n        <app-footer></app-footer>\n        \n        ${this.loading ? html`\n          <div class=\"loading-overlay\">\n            <div class=\"loading-spinner\">\n              <div class=\"spinner\"></div>\n              <p>Загрузка приложения...</p>\n            </div>\n          </div>\n        ` : ''}\n      </div>\n    `;\n  }\n}\n\ncustomElements.define('fitness-app', FitnessApp);\n"], "names": ["databaseService", "constructor", "this", "users", "PouchDB", "trainers", "workouts", "schedule", "bookings", "content", "slider", "remoteUrl", "isOnline", "initializeData", "info", "doc_count", "seedInitialData", "error", "console", "log", "_id", "type", "name", "specialization", "bio", "photo", "experience", "description", "duration", "max_participants", "difficulty", "today", "Date", "i", "date", "setDate", "getDate", "dateStr", "toISOString", "split", "push", "workout_id", "length", "trainer_id", "time", "current_participants", "Math", "floor", "random", "page", "title", "subtitle", "updated_at", "image", "order", "active", "adminUser", "email", "phone", "password_hash", "role", "created_at", "bulkDocs", "put", "createUser", "userData", "user", "now", "getUserByEmail", "find", "selector", "docs", "getSchedule", "sort", "createBooking", "userId", "scheduleId", "scheduleItem", "get", "Error", "user_id", "schedule_id", "status", "booking", "booked_at", "cancelBooking", "bookingId", "workoutDateTime", "getTime", "cancelled_at", "getUserBookings", "updateContent", "pageId", "contentData", "updatedDoc", "newDoc", "get<PERSON>ontent", "getSlides", "updateSlide", "slideId", "slideData", "getTrainers", "getWorkouts", "get<PERSON>rainer", "trainerId", "getWorkout", "workoutId", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "LitElement", "static", "css", "currentUser", "Object", "mobileMenuOpen", "Boolean", "super", "toggleMobileMenu", "handleLogout", "dispatchEvent", "CustomEvent", "getUserInitials", "map", "n", "join", "toUpperCase", "slice", "render", "html", "customElements", "define", "HomePage", "slides", "Array", "loadData", "requestUpdate", "slide", "SchedulePage", "userBookings", "loading", "selectedDate", "String", "filterWorkout", "filterTrainer", "Promise", "all", "showNotification", "getWorkoutById", "w", "getTrainerById", "t", "isUserBooked", "some", "getUserBooking", "canCancelBooking", "handleBooking", "message", "notification", "document", "createElement", "className", "textContent", "body", "append<PERSON><PERSON><PERSON>", "setTimeout", "remove", "changeDate", "direction", "currentDate", "formatDate", "dateString", "toLocaleDateString", "weekday", "year", "month", "day", "formatTime", "timeString", "getDifficultyText", "beginner", "intermediate", "advanced", "getFilteredSchedule", "filtered", "filter", "item", "filteredSchedule", "e", "target", "value", "workout", "trainer", "isBooked", "isFull", "canCancel", "toTimeString", "TrainersPage", "<PERSON><PERSON><PERSON><PERSON>", "showModal", "loadTrainers", "openTrainerModal", "style", "overflow", "closeTrainerModal", "handleModalClick", "event", "currentTarget", "getTrainerInitials", "spec", "AboutPage", "loadContent", "bcrypt", "async", "password", "saltRounds", "btoa", "hash", "authService", "loadUserFromStorage", "savedUser", "localStorage", "getItem", "JSON", "parse", "removeItem", "saveUserToStorage", "setItem", "stringify", "removeUserFromStorage", "register", "validation", "validateRegistrationData", "<PERSON><PERSON><PERSON><PERSON>", "errors", "passwordHash", "newUser", "userWithoutPassword", "removePasswordFromUser", "success", "login", "isValidEmail", "compare", "logout", "getCurrentUser", "isAuthenticated", "isAdmin", "trim", "isValidPhone", "confirmPassword", "test", "replace", "updateProfile", "updatedUser", "changePassword", "currentPassword", "newPassword", "fullUser", "LoginPage", "formData", "handleInputChange", "handleSubmit", "preventDefault", "result", "detail", "window", "history", "pushState", "PopStateEvent", "fillDemoCredentials", "RegisterPage", "fieldErrors", "validateField", "getPasswordStrength", "strength", "text", "score", "keys", "for<PERSON>ach", "field", "passwordStrength", "AdminPage", "activeTab", "homeContent", "stats", "totalUsers", "totalBookings", "totalTrainers", "totalWorkouts", "switchTab", "tab", "saveHomeContent", "saveSlide", "handleContentInput", "handleSlideInput", "renderDashboard", "open", "renderContentEditor", "renderSliderEditor", "index", "parseInt", "NotFoundPage", "isAccessDenied", "back", "AppRouter", "currentRoute", "handleUserLogin", "handleNavigation", "renderPage", "path", "AppFooter", "FitnessApp", "initializeApp", "initializeDatabase", "checkUserSession", "setupRouting", "loadingElement", "getElementById", "classList", "add", "addEventListener", "updateRoute", "matches", "closest", "link", "navigateTo", "getAttribute", "location", "pathname", "handleUserLogout"], "mappings": "sFA8aO,MAAMA,EAAkB,IA3a/B,MACE,WAAAC,GAEEC,KAAKC,MAAQ,IAAIC,QAAQ,SACzBF,KAAKG,SAAW,IAAID,QAAQ,YAC5BF,KAAKI,SAAW,IAAIF,QAAQ,YAC5BF,KAAKK,SAAW,IAAIH,QAAQ,YAC5BF,KAAKM,SAAW,IAAIJ,QAAQ,YAC5BF,KAAKO,QAAU,IAAIL,QAAQ,WAC3BF,KAAKQ,OAAS,IAAIN,QAAQ,UAG1BF,KAAKS,UAAY,wBACjBT,KAAKU,UAAW,EAEhBV,KAAKW,gBACP,CAEA,oBAAMA,GACJ,IAG8B,WADJX,KAAKC,MAAMW,QACrBC,iBACNb,KAAKc,iBAEf,CAAE,MAAOC,GACPC,QAAQC,IAAI,wBAAyBF,SAC/Bf,KAAKc,iBACb,CACF,CAEA,qBAAMA,GAIJ,MAAMX,EAAW,CACf,CACEe,IAAK,YACLC,KAAM,UACNC,KAAM,eACNC,eAAgB,CAAC,OAAQ,WACzBC,IAAK,kGACLC,MAAO,sEACPC,WAAY,SAEd,CACEN,IAAK,YACLC,KAAM,UACNC,KAAM,iBACNC,eAAgB,CAAC,yBAA0B,YAC3CC,IAAK,yFACLC,MAAO,wEACPC,WAAY,UAEd,CACEN,IAAK,YACLC,KAAM,UACNC,KAAM,gBACNC,eAAgB,CAAC,QAAS,YAC1BC,IAAK,iFACLC,MAAO,uEACPC,WAAY,UAKVpB,EAAW,CACf,CACEc,IAAK,YACLC,KAAM,UACNC,KAAM,aACNK,YAAa,sFACbC,SAAU,GACVC,iBAAkB,GAClBC,WAAY,YAEd,CACEV,IAAK,YACLC,KAAM,UACNC,KAAM,yBACNK,YAAa,uFACbC,SAAU,GACVC,iBAAkB,EAClBC,WAAY,gBAEd,CACEV,IAAK,YACLC,KAAM,UACNC,KAAM,wBACNK,YAAa,oEACbC,SAAU,GACVC,iBAAkB,GAClBC,WAAY,YAEd,CACEV,IAAK,YACLC,KAAM,UACNC,KAAM,UACNK,YAAa,4EACbC,SAAU,GACVC,iBAAkB,GAClBC,WAAY,aAKVvB,EAAW,GACXwB,EAAQ,IAAIC,KAClB,IAAK,IAAIC,EAAI,EAAGA,EAAI,EAAGA,IAAK,CAC1B,MAAMC,EAAO,IAAIF,KAAKD,GACtBG,EAAKC,QAAQJ,EAAMK,UAAYH,GAC/B,MAAMI,EAAUH,EAAKI,cAAcC,MAAM,KAAK,GAG9ChC,EAASiC,KAAK,CACZpB,IAAK,YAAYa,YACjBZ,KAAM,WACNoB,WAAYnC,EAAS2B,EAAI3B,EAASoC,QAAQtB,IAC1CuB,WAAYtC,EAAS4B,EAAI5B,EAASqC,QAAQtB,IAC1Cc,KAAMG,EACNO,KAAM,QACNhB,SAAUtB,EAAS2B,EAAI3B,EAASoC,QAAQd,SACxCC,iBAAkBvB,EAAS2B,EAAI3B,EAASoC,QAAQb,iBAChDgB,qBAAsBC,KAAKC,MAAsB,EAAhBD,KAAKE,YAIxCzC,EAASiC,KAAK,CACZpB,IAAK,YAAYa,YACjBZ,KAAM,WACNoB,WAAYnC,GAAU2B,EAAI,GAAK3B,EAASoC,QAAQtB,IAChDuB,WAAYtC,GAAU4B,EAAI,GAAK5B,EAASqC,QAAQtB,IAChDc,KAAMG,EACNO,KAAM,QACNhB,SAAUtB,GAAU2B,EAAI,GAAK3B,EAASoC,QAAQd,SAC9CC,iBAAkBvB,GAAU2B,EAAI,GAAK3B,EAASoC,QAAQb,iBACtDgB,qBAAsBC,KAAKC,MAAsB,EAAhBD,KAAKE,WAE1C,CAGA,MAAMvC,EAAU,CACd,CACEW,IAAK,eACLC,KAAM,UACN4B,KAAM,OACNC,MAAO,+BACPC,SAAU,gCACVxB,YAAa,mGACbyB,YAAY,IAAIpB,MAAOM,gBAKrB5B,EAAS,CACb,CACEU,IAAK,UACLC,KAAM,QACN6B,MAAO,mBACPC,SAAU,+BACVE,MAAO,0EACPC,MAAO,EACPC,QAAQ,GAEV,CACEnC,IAAK,UACLC,KAAM,QACN6B,MAAO,yBACPC,SAAU,iCACVE,MAAO,gFACPC,MAAO,EACPC,QAAQ,IAKNC,EAAY,CAChBpC,IAAK,aACLC,KAAM,OACNoC,MAAO,qBACPnC,KAAM,gBACNoC,MAAO,qBACPC,cAAe,+DACfC,KAAM,QACNC,YAAY,IAAI7B,MAAOM,eAIzB,UACQpC,KAAKG,SAASyD,SAASzD,SACvBH,KAAKI,SAASwD,SAASxD,SACvBJ,KAAKK,SAASuD,SAASvD,SACvBL,KAAKO,QAAQqD,SAASrD,SACtBP,KAAKQ,OAAOoD,SAASpD,SACrBR,KAAKC,MAAM4D,IAAIP,GAErBtC,QAAQC,IAAI,mCACd,CAAE,MAAOF,GACPC,QAAQD,MAAM,wCAAyCA,EACzD,CACF,CAGA,gBAAM+C,CAAWC,GACf,MAAMC,EAAO,CACX9C,IAAK,QAAQY,KAAKmC,QAClB9C,KAAM,UACH4C,EACHL,KAAM,OACNC,YAAY,IAAI7B,MAAOM,eAEzB,aAAapC,KAAKC,MAAM4D,IAAIG,EAC9B,CAEA,oBAAME,CAAeX,GACnB,IAIE,aAHqBvD,KAAKC,MAAMkE,KAAK,CACnCC,SAAU,CAAEb,MAAOA,EAAOpC,KAAM,WAEpBkD,KAAK,IAAM,IAC3B,CAAE,MAAOtD,GACP,OAAO,IACT,CACF,CAGA,iBAAMuD,CAAYtC,EAAO,MACvB,IACE,IAAIoC,EAAW,CAAEjD,KAAM,YACnBa,IACFoC,EAASpC,KAAOA,GAQlB,aALqBhC,KAAKK,SAAS8D,KAAK,CACtCC,SAAUA,EACVG,KAAM,CAAC,CAAEvC,KAAM,OAAS,CAAEU,KAAM,WAGpB2B,IAChB,CAAE,MAAOtD,GAEP,OADAC,QAAQD,MAAM,+BAAgCA,GACvC,EACT,CACF,CAGA,mBAAMyD,CAAcC,EAAQC,GAE1B,MAAMC,QAAqB3E,KAAKK,SAASuE,IAAIF,GAC7C,GAAIC,EAAahC,sBAAwBgC,EAAahD,iBACpD,MAAM,IAAIkD,MAAM,sBAYlB,UAR8B7E,KAAKM,SAAS6D,KAAK,CAC/CC,SAAU,CACRU,QAASL,EACTM,YAAaL,EACbM,OAAQ,aAIQX,KAAK7B,OAAS,EAChC,MAAM,IAAIqC,MAAM,qCAIlB,MAAMI,EAAU,CACd/D,IAAK,WAAWY,KAAKmC,QACrB9C,KAAM,UACN2D,QAASL,EACTM,YAAaL,EACbM,OAAQ,SACRE,WAAW,IAAIpD,MAAOM,eAOxB,OAHAuC,EAAahC,sBAAwB,QAC/B3C,KAAKK,SAASwD,IAAIc,SAEX3E,KAAKM,SAASuD,IAAIoB,EACjC,CAEA,mBAAME,CAAcC,EAAWX,GAC7B,MAAMQ,QAAgBjF,KAAKM,SAASsE,IAAIQ,GAExC,GAAIH,EAAQH,UAAYL,EACtB,MAAM,IAAII,MAAM,mCAIlB,MAAMF,QAAqB3E,KAAKK,SAASuE,IAAIK,EAAQF,aAC/CM,EAAkB,IAAIvD,KAAK,GAAG6C,EAAa3C,QAAQ2C,EAAajC,QAItE,GAHY,IAAIZ,KACU,IAAIA,KAAKuD,EAAgBC,UAAY,MAG7D,MAAM,IAAIT,MAAM,8DAWlB,OAPAI,EAAQD,OAAS,YACjBC,EAAQM,cAAe,IAAIzD,MAAOM,cAGlCuC,EAAahC,sBAAwB,QAC/B3C,KAAKK,SAASwD,IAAIc,SAEX3E,KAAKM,SAASuD,IAAIoB,EACjC,CAEA,qBAAMO,CAAgBf,GACpB,IAKE,aAJqBzE,KAAKM,SAAS6D,KAAK,CACtCC,SAAU,CAAEU,QAASL,EAAQtD,KAAM,WACnCoD,KAAM,CAAC,CAAEW,UAAW,YAERb,IAChB,CAAE,MAAOtD,GAEP,OADAC,QAAQD,MAAM,yCAA0CA,GACjD,EACT,CACF,CAGA,mBAAM0E,CAAcC,EAAQC,GAC1B,IACE,MACMC,EAAa,UADD5F,KAAKO,QAAQqE,IAAI,WAAWc,QAGzCC,EACHzC,YAAY,IAAIpB,MAAOM,eAEzB,aAAapC,KAAKO,QAAQsD,IAAI+B,EAChC,CAAE,MAAO7E,GAEP,MAAM8E,EAAS,CACb3E,IAAK,WAAWwE,IAChBvE,KAAM,UACN4B,KAAM2C,KACHC,EACHzC,YAAY,IAAIpB,MAAOM,eAEzB,aAAapC,KAAKO,QAAQsD,IAAIgC,EAChC,CACF,CAEA,gBAAMC,CAAWJ,GACf,IACE,aAAa1F,KAAKO,QAAQqE,IAAI,WAAWc,IAC3C,CAAE,MAAO3E,GACP,OAAO,IACT,CACF,CAGA,eAAMgF,GACJ,IAKE,aAJqB/F,KAAKQ,OAAO2D,KAAK,CACpCC,SAAU,CAAEjD,KAAM,QAASkC,QAAQ,GACnCkB,KAAM,CAAC,CAAEnB,MAAO,WAEJiB,IAChB,CAAE,MAAOtD,GAEP,OADAC,QAAQD,MAAM,4BAA6BA,GACpC,EACT,CACF,CAEA,iBAAMiF,CAAYC,EAASC,GACzB,IACE,MACMN,EAAa,UADD5F,KAAKQ,OAAOoE,IAAIqB,MAG7BC,EACHhD,YAAY,IAAIpB,MAAOM,eAEzB,aAAapC,KAAKQ,OAAOqD,IAAI+B,EAC/B,CAAE,MAAO7E,GAEP,MADAC,QAAQD,MAAM,4BAA6BA,GACrCA,CACR,CACF,CAGA,iBAAMoF,GACJ,IAIE,aAHqBnG,KAAKG,SAASgE,KAAK,CACtCC,SAAU,CAAEjD,KAAM,cAENkD,IAChB,CAAE,MAAOtD,GAEP,OADAC,QAAQD,MAAM,6BAA8BA,GACrC,EACT,CACF,CAEA,iBAAMqF,GACJ,IAIE,aAHqBpG,KAAKI,SAAS+D,KAAK,CACtCC,SAAU,CAAEjD,KAAM,cAENkD,IAChB,CAAE,MAAOtD,GAEP,OADAC,QAAQD,MAAM,+BAAgCA,GACvC,EACT,CACF,CAEA,gBAAMsF,CAAWC,GACf,IACE,aAAatG,KAAKG,SAASyE,IAAI0B,EACjC,CAAE,MAAOvF,GACP,OAAO,IACT,CACF,CAEA,gBAAMwF,CAAWC,GACf,IACE,aAAaxG,KAAKI,SAASwE,IAAI4B,EACjC,CAAE,MAAOzF,GACP,OAAO,IACT,CACF,GCxaF,MAAM0F,UAAkBC,EACtBC,cAAgBC,CAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAwKnBD,kBAAoB,CAClBE,YAAa,CAAE1F,KAAM2F,QACrBC,eAAgB,CAAE5F,KAAM6F,UAG1B,WAAAjH,GACEkH,QACAjH,KAAK6G,YAAc,KACnB7G,KAAK+G,gBAAiB,CACxB,CAEA,gBAAAG,GACElH,KAAK+G,gBAAkB/G,KAAK+G,cAC9B,CAEA,YAAAI,GACEnH,KAAKoH,cAAc,IAAIC,YAAY,eACrC,CAEA,eAAAC,CAAgBlG,GACd,OAAKA,EACEA,EAAKiB,MAAM,KAAKkF,IAAIC,GAAKA,EAAE,IAAIC,KAAK,IAAIC,cAAcC,MAAM,EAAG,GADpD,GAEpB,CAEA,MAAAC,GACE,OAAOC,CAAI;;;;;;;;;;;;;cAa0B,UAA3B7H,KAAK6G,aAAanD,KAAmBmE,CAAI;;cAEvC;;;;cAIF7H,KAAK6G,YAAcgB,CAAI;;;oBAGjB7H,KAAKsH,gBAAgBtH,KAAK6G,YAAYzF;;wBAElCpB,KAAK6G,YAAYzF;;4DAEmBpB,KAAKmH;;;cAGjDU,CAAI;;;;;;mDAM+B7H,KAAKkH;;;;;iCAKvBlH,KAAK+G,eAAiB,OAAS;;6DAEH,IAAM/G,KAAK+G,gBAAiB;qEACpB,IAAM/G,KAAK+G,gBAAiB;qEAC5B,IAAM/G,KAAK+G,gBAAiB;kEAC/B,IAAM/G,KAAK+G,gBAAiB;cACrD,UAA3B/G,KAAK6G,aAAanD,KAAmBmE,CAAI;oEACa,IAAM7H,KAAK+G,gBAAiB;cAChF;cACD/G,KAAK6G,YAGJgB,CAAI;;;iCAGa,KAAQ7H,KAAKmH,eAAgBnH,KAAK+G,gBAAiB;;;;cANlDc,CAAI;oEAC8B,IAAM7H,KAAK+G,gBAAiB;uEACzB,IAAM/G,KAAK+G,gBAAiB;;;;;KAajG,EAGFe,eAAeC,OAAO,aAActB,GCpQpC,MAAMuB,UAAiBtB,EACrBC,cAAgBC,CAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA0OnBD,kBAAoB,CAClBE,YAAa,CAAE1F,KAAM2F,QACrBmB,OAAQ,CAAE9G,KAAM+G,OAChB3H,QAAS,CAAEY,KAAM2F,SAGnB,WAAA/G,GACEkH,QACAjH,KAAK6G,YAAc,KACnB7G,KAAKiI,OAAS,GACdjI,KAAKO,QAAU,KACfP,KAAKmI,UACP,CAEA,cAAMA,GACJ,IAEEnI,KAAKiI,aAAenI,EAAgBiG,YAGpC/F,KAAKO,cAAgBT,EAAgBgG,WAAW,QAEhD9F,KAAKoI,eACP,CAAE,MAAOrH,GACPC,QAAQD,MAAM,0BAA2BA,EAC3C,CACF,CAEA,MAAA6G,GACE,OAAOC,CAAI;;;gBAGC7H,KAAKO,SAASyC,OAAS;eACxBhD,KAAKO,SAAS0C,UAAY;;cAE3BjD,KAAK6G,YAAcgB,CAAI;;cAErBA,CAAI;;;;;;;;QAQZ7H,KAAKiI,OAAOzF,OAAS,EAAIqF,CAAI;;;;;gBAKrB7H,KAAKiI,OAAOV,IAAIc,GAASR,CAAI;;;;;;0BAMnBQ,EAAMrF;yBACPqF,EAAMpF;;;;;;;QAOrB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAkCEjD,KAAK6G,YAAcgB,CAAI;;YAErBA,CAAI;;;;;KAMhB,EAGFC,eAAeC,OAAO,YAAaC,GCxVnC,MAAMM,UAAqB5B,EACzBC,cAAgBC,CAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAiWnBD,kBAAoB,CAClBE,YAAa,CAAE1F,KAAM2F,QACrBzG,SAAU,CAAEc,KAAM+G,OAClB9H,SAAU,CAAEe,KAAM+G,OAClB/H,SAAU,CAAEgB,KAAM+G,OAClBK,aAAc,CAAEpH,KAAM+G,OACtBM,QAAS,CAAErH,KAAM6F,SACjByB,aAAc,CAAEtH,KAAMuH,QACtBC,cAAe,CAAExH,KAAMuH,QACvBE,cAAe,CAAEzH,KAAMuH,SAGzB,WAAA3I,GACEkH,QACAjH,KAAK6G,YAAc,KACnB7G,KAAKK,SAAW,GAChBL,KAAKI,SAAW,GAChBJ,KAAKG,SAAW,GAChBH,KAAKuI,aAAe,GACpBvI,KAAKwI,SAAU,EACfxI,KAAKyI,cAAe,IAAI3G,MAAOM,cAAcC,MAAM,KAAK,GACxDrC,KAAK2I,cAAgB,GACrB3I,KAAK4I,cAAgB,GAErB5I,KAAKmI,UACP,CAEA,cAAMA,GACJ,IACEnI,KAAKwI,SAAU,EAGf,MAAOnI,EAAUD,EAAUD,SAAkB0I,QAAQC,IAAI,CACvDhJ,EAAgBwE,cAChBxE,EAAgBsG,cAChBtG,EAAgBqG,gBAGlBnG,KAAKK,SAAWA,EAChBL,KAAKI,SAAWA,EAChBJ,KAAKG,SAAWA,EAGZH,KAAK6G,cACP7G,KAAKuI,mBAAqBzI,EAAgB0F,gBAAgBxF,KAAK6G,YAAY3F,KAG/E,CAAE,MAAOH,GACPC,QAAQD,MAAM,0BAA2BA,GACzCf,KAAK+I,iBAAiB,yBAA0B,QAClD,CAAC,QACC/I,KAAKwI,SAAU,CACjB,CACF,CAEA,cAAAQ,CAAexC,GACb,OAAOxG,KAAKI,SAAS+D,KAAK8E,GAAKA,EAAE/H,MAAQsF,EAC3C,CAEA,cAAA0C,CAAe5C,GACb,OAAOtG,KAAKG,SAASgE,KAAKgF,GAAKA,EAAEjI,MAAQoF,EAC3C,CAEA,YAAA8C,CAAa1E,GACX,OAAO1E,KAAKuI,aAAac,KAAKpE,GAC5BA,EAAQF,cAAgBL,GAAiC,WAAnBO,EAAQD,OAElD,CAEA,cAAAsE,CAAe5E,GACb,OAAO1E,KAAKuI,aAAapE,KAAKc,GAC5BA,EAAQF,cAAgBL,GAAiC,WAAnBO,EAAQD,OAElD,CAEA,gBAAAuE,CAAiB5E,GACf,MAAMU,EAAkB,IAAIvD,KAAK,GAAG6C,EAAa3C,QAAQ2C,EAAajC,QAGtE,OAFY,IAAIZ,MACU,IAAIA,KAAKuD,EAAgBC,UAAY,KAEjE,CAEA,mBAAMkE,CAAc7E,GAClB,GAAK3E,KAAK6G,YAKV,IAGE,GAFiB7G,KAAKoJ,aAAazE,EAAazD,KAElC,CAEZ,IAAKlB,KAAKuJ,iBAAiB5E,GAEzB,YADA3E,KAAK+I,iBAAiB,6DAA8D,SAItF,MAAM9D,EAAUjF,KAAKsJ,eAAe3E,EAAazD,WAC3CpB,EAAgBqF,cAAcF,EAAQ/D,IAAKlB,KAAK6G,YAAY3F,KAClElB,KAAK+I,iBAAiB,kBAAmB,UAC3C,KAAO,CAEL,GAAIpE,EAAahC,sBAAwBgC,EAAahD,iBAEpD,YADA3B,KAAK+I,iBAAiB,qBAAsB,eAIxCjJ,EAAgB0E,cAAcxE,KAAK6G,YAAY3F,IAAKyD,EAAazD,KACvElB,KAAK+I,iBAAiB,sCAAuC,UAC/D,OAGM/I,KAAKmI,UAEb,CAAE,MAAOpH,GACPC,QAAQD,MAAM,4BAA6BA,GAC3Cf,KAAK+I,iBAAiBhI,EAAM0I,SAAW,mBAAoB,QAC7D,MAlCEzJ,KAAK+I,iBAAiB,sDAAuD,QAmCjF,CAEA,gBAAAA,CAAiBU,EAAStI,EAAO,QAC/B,MAAMuI,EAAeC,SAASC,cAAc,OAC5CF,EAAaG,UAAY,gBAAgB1I,IACzCuI,EAAaI,YAAcL,EAC3BE,SAASI,KAAKC,YAAYN,GAE1BO,WAAW,KACTP,EAAaQ,UACZ,IACL,CAEA,UAAAC,CAAWC,GACT,MAAMC,EAAc,IAAIvI,KAAK9B,KAAKyI,cAClC4B,EAAYpI,QAAQoI,EAAYnI,UAAYkI,GAC5CpK,KAAKyI,aAAe4B,EAAYjI,cAAcC,MAAM,KAAK,EAC3D,CAEA,UAAAiI,CAAWC,GAQT,OAPa,IAAIzI,KAAKyI,GAOVC,mBAAmB,QANf,CACdC,QAAS,OACTC,KAAM,UACNC,MAAO,OACPC,IAAK,WAGT,CAEA,UAAAC,CAAWC,GACT,OAAOA,EAAWnD,MAAM,EAAG,EAC7B,CAEA,iBAAAoD,CAAkBnJ,GAMhB,MALY,CACVoJ,SAAY,aACZC,aAAgB,UAChBC,SAAY,eAEHtJ,IAAeA,CAC5B,CAEA,mBAAAuJ,GACE,IAAIC,EAAWpL,KAAKK,SAepB,OAZA+K,EAAWA,EAASC,OAAOC,GAAQA,EAAKtJ,OAAShC,KAAKyI,cAGlDzI,KAAK2I,gBACPyC,EAAWA,EAASC,OAAOC,GAAQA,EAAK/I,aAAevC,KAAK2I,gBAI1D3I,KAAK4I,gBACPwC,EAAWA,EAASC,OAAOC,GAAQA,EAAK7I,aAAezC,KAAK4I,gBAGvDwC,CACT,CAEA,MAAAxD,GACE,GAAI5H,KAAKwI,QACP,OAAOX,CAAI;;;;;;;QAUb,MAAM0D,EAAmBvL,KAAKmL,sBAE9B,OAAOtD,CAAI;;;;;;;;;;;;;mDAaoC7H,KAAK2I;8BACzB6C,GAAMxL,KAAK2I,cAAgB6C,EAAEC,OAAOC;;gBAEnD1L,KAAKI,SAASmH,IAAIoE,GAAW9D,CAAI;gCACjB8D,EAAQzK,OAAOyK,EAAQvK;;;;;;;mDAOJpB,KAAK4I;8BACzB4C,GAAMxL,KAAK4I,cAAgB4C,EAAEC,OAAOC;;gBAEnD1L,KAAKG,SAASoH,IAAIqE,GAAW/D,CAAI;gCACjB+D,EAAQ1K,OAAO0K,EAAQxK;;;;;;kDAML,IAAMpB,KAAKmK,YAAW;;;;gBAIxDnK,KAAKsK,WAAWtK,KAAKyI;;kDAEa,IAAMzI,KAAKmK,WAAW;;;;;;;YAO5DoB,EAAiB/I,OAAS,EAAIqF,CAAI;;;yCAGL7H,KAAKsK,WAAWtK,KAAKyI;wCACtB8C,EAAiB/I;;;kBAGvC+I,EAAiBhE,IAAI5C,IACrB,MAAMgH,EAAU3L,KAAKgJ,eAAerE,EAAapC,YAC3CqJ,EAAU5L,KAAKkJ,eAAevE,EAAalC,YAC3CoJ,EAAW7L,KAAKoJ,aAAazE,EAAazD,KAC1C4K,EAASnH,EAAahC,sBAAwBgC,EAAahD,iBAC3DoK,EAAYF,GAAY7L,KAAKuJ,iBAAiB5E,GAEpD,OAAOkD,CAAI;;;;gCAIG8D,GAASvK,MAAQ;;iCAEhBpB,KAAK6K,WAAWlG,EAAajC,WAAW1C,KAAK6K,WAChD,IAAI/I,KAAK,IAAIA,KAAK,cAAc6C,EAAajC,QAAQ4C,UAAoC,IAAxBX,EAAajD,UAC3EsK,eAAerE,MAAM,EAAG;;;;;+DAMMhD,EAAahC;+BAC7CgC,EAAahD;;;;;;;;oCAQRiK,GAASxK,MAAQ;;;iCAGpBuD,EAAajD;;;uEAGyBiK,GAAS/J;gCAChD5B,KAAK+K,kBAAkBY,GAAS/J;;;;;0BAKtC5B,KAAK6G,YAAcgB,CAAI;;8CAEHgE,EAAW,SAAW,MAAMC,IAAWD,EAAW,OAAS;wCACjEC,IAAWD;qCACd,IAAM7L,KAAKwJ,cAAc7E;;8BAEhCkH,EACGE,EAAY,kBAAoB,UAChCD,EAAS,WAAa;;0BAG3BjE,CAAI;;;;;;;;;;YAWlBA,CAAI;;;;;;;;;KAUhB,EAGFC,eAAeC,OAAO,gBAAiBO,GCxqBvC,MAAM2D,UAAqBvF,EACzBC,cAAgBC,CAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAgVnBD,kBAAoB,CAClBxG,SAAU,CAAEgB,KAAM+G,OAClBM,QAAS,CAAErH,KAAM6F,SACjBkF,gBAAiB,CAAE/K,KAAM2F,QACzBqF,UAAW,CAAEhL,KAAM6F,UAGrB,WAAAjH,GACEkH,QACAjH,KAAKG,SAAW,GAChBH,KAAKwI,SAAU,EACfxI,KAAKkM,gBAAkB,KACvBlM,KAAKmM,WAAY,EAEjBnM,KAAKoM,cACP,CAEA,kBAAMA,GACJ,IACEpM,KAAKwI,SAAU,EACfxI,KAAKG,eAAiBL,EAAgBqG,aACxC,CAAE,MAAOpF,GACPC,QAAQD,MAAM,4BAA6BA,EAC7C,CAAC,QACCf,KAAKwI,SAAU,CACjB,CACF,CAEA,gBAAA6D,CAAiBT,GACf5L,KAAKkM,gBAAkBN,EACvB5L,KAAKmM,WAAY,EACjBxC,SAASI,KAAKuC,MAAMC,SAAW,QACjC,CAEA,iBAAAC,GACExM,KAAKmM,WAAY,EACjBnM,KAAKkM,gBAAkB,KACvBvC,SAASI,KAAKuC,MAAMC,SAAW,EACjC,CAEA,gBAAAE,CAAiBC,GACXA,EAAMjB,SAAWiB,EAAMC,eACzB3M,KAAKwM,mBAET,CAEA,kBAAAI,CAAmBxL,GACjB,OAAOA,EAAKiB,MAAM,KAAKkF,IAAIC,GAAKA,EAAE,IAAIC,KAAK,IAAIC,aACjD,CAEA,MAAAE,GACE,OAAI5H,KAAKwI,QACAX,CAAI;;;;;;;QAUgB,IAAzB7H,KAAKG,SAASqC,OACTqF,CAAI;;;;;;;;QAWNA,CAAI;;;;;;;;;;;;;;;2CAe4B7H,KAAKG,SAASqC;;;;;;;;;;;;;;;;;;;;YAoB7CxC,KAAKG,SAASoH,IAAIqE,GAAW/D,CAAI;;;kBAG3B7H,KAAK4M,mBAAmBhB,EAAQxK;;;2CAGPwK,EAAQxK;;;oBAG/BwK,EAAQvK,eAAekG,IAAIsF,GAAQhF,CAAI;uDACJgF;;;;yCAIdjB,EAAQtK;;;;uCAIVsK,EAAQpK;;;;;mCAKZ,IAAMxB,KAAKqM,iBAAiBT;;;;;;;;;;;;;QAavD5L,KAAKmM,WAAanM,KAAKkM,gBAAkBrE,CAAI;4CACT7H,KAAKyM;;;wCAGTzM,KAAKkM,gBAAgB9K;iDACZpB,KAAKwM;;;;kBAIpCxM,KAAK4M,mBAAmB5M,KAAKkM,gBAAgB9K;;;;kBAI7CpB,KAAKkM,gBAAgB7K,eAAekG,IAAIsF,GAAQhF,CAAI;qDACjBgF;;;;;;qCAMhB7M,KAAKkM,gBAAgB1K;;;;kBAIxCxB,KAAKkM,gBAAgB5K;;;;;;;gEAOyBtB,KAAKwM;;;;;;;QAO3D;KAER,EAGF1E,eAAeC,OAAO,gBAAiBkE,GC7gBvC,MAAMa,UAAkBpG,EACtBC,cAAgBC,CAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA0VnBD,kBAAoB,CAClBpG,QAAS,CAAEY,KAAM2F,QACjB0B,QAAS,CAAErH,KAAM6F,UAGnB,WAAAjH,GACEkH,QACAjH,KAAKO,QAAU,KACfP,KAAKwI,SAAU,EAEfxI,KAAK+M,aACP,CAEA,iBAAMA,GACJ,IACE/M,KAAKwI,SAAU,EACfxI,KAAKO,cAAgBT,EAAgBgG,WAAW,QAClD,CAAE,MAAO/E,GACPC,QAAQD,MAAM,4BAA6BA,EAC7C,CAAC,QACCf,KAAKwI,SAAU,CACjB,CACF,CAEA,MAAAZ,GACE,OAAI5H,KAAKwI,QACAX,CAAI;;;;;;;QAUNA,CAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA8Lb,EAGFC,eAAeC,OAAO,aAAc+E,GClkBpC,MAAME,EAAS,CACbC,KAAU,MAACC,EAAUC,IAEZC,KAAKF,EAAWC,GAEzBF,QAAa,MAACC,EAAUG,IAEfD,KAAKF,EAAW,QAAUG,GA2S9B,MAAMC,EAAc,IAtS3B,MACE,WAAAvN,GACEC,KAAK6G,YAAc,KACnB7G,KAAKuN,qBACP,CAEA,mBAAAA,GACE,IACE,MAAMC,EAAYC,aAAaC,QAAQ,eACnCF,IACFxN,KAAK6G,YAAc8G,KAAKC,MAAMJ,GAElC,CAAE,MAAOzM,GACPC,QAAQD,MAAM,gDAAiDA,GAC/D0M,aAAaI,WAAW,cAC1B,CACF,CAEA,iBAAAC,CAAkB9J,GAChB,IACEyJ,aAAaM,QAAQ,cAAeJ,KAAKK,UAAUhK,IACnDhE,KAAK6G,YAAc7C,CACrB,CAAE,MAAOjD,GACPC,QAAQD,MAAM,iDAAkDA,EAClE,CACF,CAEA,qBAAAkN,GACER,aAAaI,WAAW,eACxB7N,KAAK6G,YAAc,IACrB,CAEA,cAAMqH,CAASnK,GACb,IAEE,MAAMoK,EAAanO,KAAKoO,yBAAyBrK,GACjD,IAAKoK,EAAWE,QACd,MAAM,IAAIxJ,MAAMsJ,EAAWG,OAAO7G,KAAK,OAKzC,SAD2B3H,EAAgBoE,eAAeH,EAASR,OAEjE,MAAM,IAAIsB,MAAM,6CAIlB,MAAMsI,EAAa,GACboB,QAAqBvB,EAAOK,KAAKtJ,EAASmJ,SAAUC,GAGpDqB,EAAU,CACdjL,MAAOQ,EAASR,MAChBnC,KAAM2C,EAAS3C,KACfoC,MAAOO,EAASP,MAChBC,cAAe8K,GAMXvK,SAHelE,EAAgBgE,WAAW0K,SAG7B1O,EAAgBoE,eAAeH,EAASR,QACrDkL,EAAsBzO,KAAK0O,uBAAuB1K,GAKxD,OAFAhE,KAAK8N,kBAAkBW,GAEhB,CACLE,SAAS,EACT3K,KAAMyK,EACNhF,QAAS,6BAGb,CAAE,MAAO1I,GAEP,OADAC,QAAQD,MAAM,sBAAuBA,GAC9B,CACL4N,SAAS,EACT5N,MAAOA,EAAM0I,SAAW,yBAE5B,CACF,CAEA,WAAMmF,CAAMrL,EAAO2J,GACjB,IAEE,IAAK3J,IAAU2J,EACb,MAAM,IAAIrI,MAAM,6CAGlB,IAAK7E,KAAK6O,aAAatL,GACrB,MAAM,IAAIsB,MAAM,6BAIlB,MAAMb,QAAalE,EAAgBoE,eAAeX,GAClD,IAAKS,EACH,MAAM,IAAIa,MAAM,wCAKlB,UAD8BmI,EAAO8B,QAAQ5B,EAAUlJ,EAAKP,eAE1D,MAAM,IAAIoB,MAAM,mBAIlB,MAAM4J,EAAsBzO,KAAK0O,uBAAuB1K,GAGxD,OAFAhE,KAAK8N,kBAAkBW,GAEhB,CACLE,SAAS,EACT3K,KAAMyK,EACNhF,QAAS,wBAGb,CAAE,MAAO1I,GAEP,OADAC,QAAQD,MAAM,gBAAiBA,GACxB,CACL4N,SAAS,EACT5N,MAAOA,EAAM0I,SAAW,6BAE5B,CACF,CAEA,MAAAsF,GAEE,OADA/O,KAAKiO,wBACE,CACLU,SAAS,EACTlF,QAAS,yBAEb,CAEA,cAAAuF,GACE,OAAOhP,KAAK6G,WACd,CAEA,eAAAoI,GACE,OAA4B,OAArBjP,KAAK6G,WACd,CAEA,OAAAqI,GACE,OAAOlP,KAAK6G,aAAyC,UAA1B7G,KAAK6G,YAAYnD,IAC9C,CAEA,wBAAA0K,CAAyBrK,GACvB,MAAMuK,EAAS,GA2Bf,QAxBKvK,EAAS3C,MAAQ2C,EAAS3C,KAAK+N,OAAO3M,OAAS,IAClD8L,EAAOhM,KAAK,0CAITyB,EAASR,OAAUvD,KAAK6O,aAAa9K,EAASR,QACjD+K,EAAOhM,KAAK,6BAITyB,EAASP,OAAUxD,KAAKoP,aAAarL,EAASP,QACjD8K,EAAOhM,KAAK,kCAITyB,EAASmJ,UAAYnJ,EAASmJ,SAAS1K,OAAS,IACnD8L,EAAOhM,KAAK,8CAIVyB,EAASmJ,WAAanJ,EAASsL,iBACjCf,EAAOhM,KAAK,uBAGP,CACL+L,QAA2B,IAAlBC,EAAO9L,OAChB8L,OAAQA,EAEZ,CAEA,YAAAO,CAAatL,GAEX,MADmB,6BACD+L,KAAK/L,EACzB,CAEA,YAAA6L,CAAa5L,GAGX,MADmB,oFACD8L,KAAK9L,EAAM+L,QAAQ,MAAO,IAC9C,CAEA,sBAAAb,CAAuB1K,GACrB,MAAMP,cAAEA,KAAkBgL,GAAwBzK,EAClD,OAAOyK,CACT,CAGA,mBAAMe,CAAczL,GAClB,IACE,IAAK/D,KAAKiP,kBACR,MAAM,IAAIpK,MAAM,0BAIlB,MAAMyJ,EAAS,GAUf,KARKvK,EAAS3C,MAAQ2C,EAAS3C,KAAK+N,OAAO3M,OAAS,IAClD8L,EAAOhM,KAAK,0CAGTyB,EAASP,OAAUxD,KAAKoP,aAAarL,EAASP,QACjD8K,EAAOhM,KAAK,gCAGVgM,EAAO9L,OAAS,EAClB,MAAM,IAAIqC,MAAMyJ,EAAO7G,KAAK,OAI9B,MAAMgI,EAAc,IACfzP,KAAK6G,YACRzF,KAAM2C,EAAS3C,KACfoC,MAAOO,EAASP,OAOlB,OAFAxD,KAAK8N,kBAAkB2B,GAEhB,CACLd,SAAS,EACT3K,KAAMyL,EACNhG,QAAS,2BAGb,CAAE,MAAO1I,GAEP,OADAC,QAAQD,MAAM,6BAA8BA,GACrC,CACL4N,SAAS,EACT5N,MAAOA,EAAM0I,SAAW,gCAE5B,CACF,CAGA,oBAAMiG,CAAeC,EAAiBC,EAAaP,GACjD,IACE,IAAKrP,KAAKiP,kBACR,MAAM,IAAIpK,MAAM,0BAIlB,IAAK8K,IAAoBC,IAAgBP,EACvC,MAAM,IAAIxK,MAAM,uCAGlB,GAAI+K,EAAYpN,OAAS,EACvB,MAAM,IAAIqC,MAAM,oDAGlB,GAAI+K,IAAgBP,EAClB,MAAM,IAAIxK,MAAM,6BAIlB,MAAMgL,QAAiB/P,EAAgBoE,eAAelE,KAAK6G,YAAYtD,OAIvE,UADqCyJ,EAAO8B,QAAQa,EAAiBE,EAASpM,eAE5E,MAAM,IAAIoB,MAAM,2BAIlB,MAAMsI,EAAa,SACWH,EAAOK,KAAKuC,EAAazC,GAKvD,MAAO,CACLwB,SAAS,EACTlF,QAAS,yBAGb,CAAE,MAAO1I,GAEP,OADAC,QAAQD,MAAM,uBAAwBA,GAC/B,CACL4N,SAAS,EACT5N,MAAOA,EAAM0I,SAAW,0BAE5B,CACF,GC5SF,MAAMqG,UAAkBpJ,EACtBC,cAAgBC,CAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAyLnBD,kBAAoB,CAClB6B,QAAS,CAAErH,KAAM6F,SACjBjG,MAAO,CAAEI,KAAMuH,QACfiG,QAAS,CAAExN,KAAMuH,QACjBqH,SAAU,CAAE5O,KAAM2F,SAGpB,WAAA/G,GACEkH,QACAjH,KAAKwI,SAAU,EACfxI,KAAKe,MAAQ,GACbf,KAAK2O,QAAU,GACf3O,KAAK+P,SAAW,CACdxM,MAAO,GACP2J,SAAU,GAEd,CAEA,iBAAA8C,CAAkBtD,GAChB,MAAMtL,KAAEA,EAAIsK,MAAEA,GAAUgB,EAAMjB,OAC9BzL,KAAK+P,SAAW,IACX/P,KAAK+P,SACR3O,CAACA,GAAOsK,GAIN1L,KAAKe,QACPf,KAAKe,MAAQ,GAEjB,CAEA,kBAAMkP,CAAavD,GAGjB,GAFAA,EAAMwD,kBAEFlQ,KAAKwI,QAAT,CAEAxI,KAAKwI,SAAU,EACfxI,KAAKe,MAAQ,GACbf,KAAK2O,QAAU,GAEf,IACE,MAAMwB,QAAe7C,EAAYsB,MAAM5O,KAAK+P,SAASxM,MAAOvD,KAAK+P,SAAS7C,UAEtEiD,EAAOxB,SACT3O,KAAK2O,QAAUwB,EAAO1G,QAGtBzJ,KAAKoH,cAAc,IAAIC,YAAY,aAAc,CAC/C+I,OAAQ,CAAEpM,KAAMmM,EAAOnM,SAIzBiG,WAAW,KACToG,OAAOC,QAAQC,UAAU,CAAA,EAAI,GAAI,KACjCF,OAAOjJ,cAAc,IAAIoJ,cAAc,cACtC,MAGHxQ,KAAKe,MAAQoP,EAAOpP,KAExB,CAAE,MAAOA,GACPf,KAAKe,MAAQ,uCACbC,QAAQD,MAAM,eAAgBA,EAChC,CAAC,QACCf,KAAKwI,SAAU,CACjB,CA/BkB,CAgCpB,CAEA,mBAAAiI,CAAoBtP,GAEhBnB,KAAK+P,SADM,UAAT5O,EACc,CACdoC,MAAO,qBACP2J,SAAU,YAGI,CACd3J,MAAO,mBACP2J,SAAU,YAGdlN,KAAKoI,eACP,CAEA,MAAAR,GACE,OAAOC,CAAI;;;;;;;;;;;4DAW6C,IAAM7H,KAAKyQ,oBAAoB;;;;YAI/EzQ,KAAKe,MAAQ8G,CAAI;;gBAEb7H,KAAKe;;YAEP;;YAEFf,KAAK2O,QAAU9G,CAAI;;gBAEf7H,KAAK2O;;YAEP;;0BAEY3O,KAAKiQ;;;;;;;;yBAQNjQ,KAAK+P,SAASxM;yBACdvD,KAAKgQ;;;;;;;;;;;;;;yBAcLhQ,KAAK+P,SAAS7C;yBACdlN,KAAKgQ;;;;;;;mEAOqChQ,KAAKwI;gBACxDxI,KAAKwI,QAAUX,CAAI;;;gBAGjB;;;;;;;;;KAUhB,EAGFC,eAAeC,OAAO,aAAc+H,GCzVpC,MAAMY,UAAqBhK,EACzBC,cAAgBC,CAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAwNnBD,kBAAoB,CAClB6B,QAAS,CAAErH,KAAM6F,SACjBjG,MAAO,CAAEI,KAAMuH,QACfiG,QAAS,CAAExN,KAAMuH,QACjBqH,SAAU,CAAE5O,KAAM2F,QAClB6J,YAAa,CAAExP,KAAM2F,SAGvB,WAAA/G,GACEkH,QACAjH,KAAKwI,SAAU,EACfxI,KAAKe,MAAQ,GACbf,KAAK2O,QAAU,GACf3O,KAAK+P,SAAW,CACd3O,KAAM,GACNmC,MAAO,GACPC,MAAO,GACP0J,SAAU,GACVmC,gBAAiB,IAEnBrP,KAAK2Q,YAAc,CAAA,CACrB,CAEA,iBAAAX,CAAkBtD,GAChB,MAAMtL,KAAEA,EAAIsK,MAAEA,GAAUgB,EAAMjB,OAC9BzL,KAAK+P,SAAW,IACX/P,KAAK+P,SACR3O,CAACA,GAAOsK,GAIN1L,KAAKe,QACPf,KAAKe,MAAQ,IAGXf,KAAK2Q,YAAYvP,KACnBpB,KAAK2Q,YAAc,IACd3Q,KAAK2Q,YACRvP,CAACA,GAAO,KAIZpB,KAAKoI,eACP,CAEA,aAAAwI,CAAcxP,EAAMsK,GAClB,OAAQtK,GACN,IAAK,OACH,OAAOsK,EAAMyD,OAAO3M,QAAU,EAAI,GAAK,yCAEzC,IAAK,QAEH,MADmB,6BACD8M,KAAK5D,GAAS,GAAK,4BAEvC,IAAK,QAEH,MADmB,oFACD4D,KAAK5D,EAAM6D,QAAQ,MAAO,KAAO,GAAK,+BAE1D,IAAK,WACH,OAAO7D,EAAMlJ,QAAU,EAAI,GAAK,6CAElC,IAAK,kBACH,OAAOkJ,IAAU1L,KAAK+P,SAAS7C,SAAW,GAAK,sBAEjD,QACE,MAAO,GAEb,CAEA,mBAAA2D,CAAoB3D,GAClB,IAAKA,EAAU,MAAO,CAAE4D,SAAU,EAAGC,KAAM,IAE3C,IAAIC,EAAQ,EAkBZ,OAfI9D,EAAS1K,QAAU,IAAGwO,GAAS,GAC/B9D,EAAS1K,QAAU,KAAIwO,GAAS,GAGhC,KAAK1B,KAAKpC,KAAW8D,GAAS,GAG9B,QAAQ1B,KAAKpC,KAAW8D,GAAS,GAGjC,QAAQ1B,KAAKpC,KAAW8D,GAAS,GAGjC,eAAe1B,KAAKpC,KAAW8D,GAAS,GAExCA,GAAS,EAAU,CAAEF,SAAU,EAAGC,KAAM,iBACxCC,GAAS,EAAU,CAAEF,SAAU,EAAGC,KAAM,kBACrC,CAAED,SAAU,EAAGC,KAAM,iBAC9B,CAEA,kBAAMd,CAAavD,GAGjB,GAFAA,EAAMwD,iBAEFlQ,KAAKwI,QAAS,OAGlB,MAAM8F,EAAS,CAAA,EAMf,GALAxH,OAAOmK,KAAKjR,KAAK+P,UAAUmB,QAAQC,IACjC,MAAMpQ,EAAQf,KAAK4Q,cAAcO,EAAOnR,KAAK+P,SAASoB,IAClDpQ,IAAOuN,EAAO6C,GAASpQ,KAGzB+F,OAAOmK,KAAK3C,GAAQ9L,OAAS,EAG/B,OAFAxC,KAAK2Q,YAAcrC,OACnBtO,KAAKe,MAAQ,wCAIff,KAAKwI,SAAU,EACfxI,KAAKe,MAAQ,GACbf,KAAK2O,QAAU,GACf3O,KAAK2Q,YAAc,CAAA,EAEnB,IACE,MAAMR,QAAe7C,EAAYY,SAASlO,KAAK+P,UAE3CI,EAAOxB,SACT3O,KAAK2O,QAAUwB,EAAO1G,QAGtBzJ,KAAKoH,cAAc,IAAIC,YAAY,aAAc,CAC/C+I,OAAQ,CAAEpM,KAAMmM,EAAOnM,SAIzBiG,WAAW,KACToG,OAAOC,QAAQC,UAAU,CAAA,EAAI,GAAI,KACjCF,OAAOjJ,cAAc,IAAIoJ,cAAc,cACtC,OAGHxQ,KAAKe,MAAQoP,EAAOpP,KAExB,CAAE,MAAOA,GACPf,KAAKe,MAAQ,mCACbC,QAAQD,MAAM,sBAAuBA,EACvC,CAAC,QACCf,KAAKwI,SAAU,CACjB,CACF,CAEA,MAAAZ,GACE,MAAMwJ,EAAmBpR,KAAK6Q,oBAAoB7Q,KAAK+P,SAAS7C,UAEhE,OAAOrF,CAAI;;;;;;;;YAQH7H,KAAKe,MAAQ8G,CAAI;;gBAEb7H,KAAKe;;YAEP;;YAEFf,KAAK2O,QAAU9G,CAAI;;gBAEf7H,KAAK2O;;YAEP;;0BAEY3O,KAAKiQ;;;;;;;oCAOKjQ,KAAK2Q,YAAYvP,KAAO,QAAU;yBAC7CpB,KAAK+P,SAAS3O;yBACdpB,KAAKgQ;;;;;gBAKdhQ,KAAK2Q,YAAYvP,KAAOyG,CAAI;0CACF7H,KAAK2Q,YAAYvP;gBACzC;;;;;;;;;;sCAUoBpB,KAAK2Q,YAAYpN,MAAQ,QAAU;2BAC9CvD,KAAK+P,SAASxM;2BACdvD,KAAKgQ;;;;;kBAKdhQ,KAAK2Q,YAAYpN,MAAQsE,CAAI;4CACH7H,KAAK2Q,YAAYpN;kBACzC;;;;;;;;;sCASkBvD,KAAK2Q,YAAYnN,MAAQ,QAAU;2BAC9CxD,KAAK+P,SAASvM;2BACdxD,KAAKgQ;;;;;kBAKdhQ,KAAK2Q,YAAYnN,MAAQqE,CAAI;4CACH7H,KAAK2Q,YAAYnN;kBACzC;;;;;;;;;;oCAUgBxD,KAAK2Q,YAAYzD,SAAW,QAAU;yBACjDlN,KAAK+P,SAAS7C;yBACdlN,KAAKgQ;;;;;gBAKdhQ,KAAK2Q,YAAYzD,SAAWrF,CAAI;0CACN7H,KAAK2Q,YAAYzD;gBACzC;;gBAEFlN,KAAK+P,SAAS7C,SAAWrF,CAAI;;;yDAG0C,IAA9BuJ,EAAiBN,SAAiB,OAAuC,IAA9BM,EAAiBN,SAAiB,SAAW;yCACvGM,EAAiBN,SAAW,EAAK;;+CAE5BM,EAAiBL;;gBAE9C;;;;;;;;;oCASkB/Q,KAAK2Q,YAAYtB,gBAAkB,QAAU;yBACxDrP,KAAK+P,SAASV;yBACdrP,KAAKgQ;;;;;gBAKdhQ,KAAK2Q,YAAYtB,gBAAkBxH,CAAI;0CACb7H,KAAK2Q,YAAYtB;gBACzC;;;;;;;;sEAQoDrP,KAAKwI;gBAC3DxI,KAAKwI,QAAUX,CAAI;;;gBAGjB;;;;;;;;;KAUhB,EAGFC,eAAeC,OAAO,gBAAiB2I,GC/fvC,MAAMW,UAAkB3K,EACtBC,cAAgBC,CAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAwWnBD,kBAAoB,CAClBE,YAAa,CAAE1F,KAAM2F,QACrBwK,UAAW,CAAEnQ,KAAMuH,QACnB6I,YAAa,CAAEpQ,KAAM2F,QACrBmB,OAAQ,CAAE9G,KAAM+G,OAChBM,QAAS,CAAErH,KAAM6F,SACjBwK,MAAO,CAAErQ,KAAM2F,SAGjB,WAAA/G,GACEkH,QACAjH,KAAK6G,YAAc,KACnB7G,KAAKsR,UAAY,YACjBtR,KAAKuR,YAAc,CAAA,EACnBvR,KAAKiI,OAAS,GACdjI,KAAKwI,SAAU,EACfxI,KAAKwR,MAAQ,CACXC,WAAY,EACZC,cAAe,EACfC,cAAe,EACfC,cAAe,GAGjB5R,KAAKmI,UACP,CAEA,cAAMA,GACJ,IACEnI,KAAKwI,SAAU,EAGf,MAAMjI,QAAgBT,EAAgBgG,WAAW,QAC7CvF,IACFP,KAAKuR,YAAc,CACjBvO,MAAOzC,EAAQyC,OAAS,GACxBC,SAAU1C,EAAQ0C,UAAY,GAC9BxB,YAAalB,EAAQkB,aAAe,KAKxCzB,KAAKiI,aAAenI,EAAgBiG,YAGpC/F,KAAKwR,MAAQ,CACXC,WAAY,IACZC,cAAe,IACfC,cAAe,EACfC,cAAe,GAGnB,CAAE,MAAO7Q,GACPC,QAAQD,MAAM,0BAA2BA,GACzCf,KAAK+I,iBAAiB,yBAA0B,QAClD,CAAC,QACC/I,KAAKwI,SAAU,CACjB,CACF,CAEA,SAAAqJ,CAAUC,GACR9R,KAAKsR,UAAYQ,CACnB,CAEA,qBAAMC,GACJ,IACE/R,KAAKwI,SAAU,QAET1I,EAAgB2F,cAAc,OAAQzF,KAAKuR,aACjDvR,KAAK+I,iBAAiB,oCAAqC,UAE7D,CAAE,MAAOhI,GACPC,QAAQD,MAAM,8BAA+BA,GAC7Cf,KAAK+I,iBAAiB,6BAA8B,QACtD,CAAC,QACC/I,KAAKwI,SAAU,CACjB,CACF,CAEA,eAAMwJ,CAAU/L,EAASC,GACvB,IACElG,KAAKwI,SAAU,QAET1I,EAAgBkG,YAAYC,EAASC,SACrClG,KAAKmI,WACXnI,KAAK+I,iBAAiB,iBAAkB,UAE1C,CAAE,MAAOhI,GACPC,QAAQD,MAAM,4BAA6BA,GAC3Cf,KAAK+I,iBAAiB,2BAA4B,QACpD,CAAC,QACC/I,KAAKwI,SAAU,CACjB,CACF,CAEA,kBAAAyJ,CAAmBd,EAAOzF,GACxB1L,KAAKuR,YAAc,IACdvR,KAAKuR,YACRJ,CAACA,GAAQzF,EAEb,CAEA,gBAAAwG,CAAiBjM,EAASkL,EAAOzF,GAC/B1L,KAAKiI,OAASjI,KAAKiI,OAAOV,IAAIc,GAC5BA,EAAMnH,MAAQ+E,EACV,IAAKoC,EAAO8I,CAACA,GAAQzF,GACrBrD,EAER,CAEA,gBAAAU,CAAiBU,EAAStI,EAAO,QAC/B,MAAMuI,EAAeC,SAASC,cAAc,OAC5CF,EAAaG,UAAY,gBAAgB1I,IACzCuI,EAAaI,YAAcL,EAC3BE,SAASI,KAAKC,YAAYN,GAE1BO,WAAW,KACTP,EAAaQ,UACZ,IACL,CAEA,eAAAiI,GACE,OAAOtK,CAAI;;;;;;;;qCAQsB7H,KAAKwR,MAAMC;;;;;qCAKXzR,KAAKwR,MAAME;;;;;qCAKX1R,KAAKwR,MAAMG;;;;;qCAKX3R,KAAKwR,MAAMI;;;;;;;;;;6CAUH,IAAM5R,KAAK6R,UAAU;;;6CAGrB,IAAM7R,KAAK6R,UAAU;;;6CAGrB,IAAMxB,OAAO+B,KAAK,YAAa;;;6CAG/B,IAAM/B,OAAO+B,KAAK,YAAa;;;;KAK1E,CAEA,mBAAAC,GACE,OAAOxK,CAAI;;;;;sBAKQ2D,IAAQA,EAAE0E,iBAAkBlQ,KAAK+R;;;;;;qBAMnC/R,KAAKuR,YAAYvO,OAAS;qBACzBwI,GAAMxL,KAAKiS,mBAAmB,QAASzG,EAAEC,OAAOC;;;;;;;;;;qBAUjD1L,KAAKuR,YAAYtO,UAAY;qBAC5BuI,GAAMxL,KAAKiS,mBAAmB,WAAYzG,EAAEC,OAAOC;;;;;;;;;qBASpD1L,KAAKuR,YAAY9P,aAAe;qBAC/B+J,GAAMxL,KAAKiS,mBAAmB,cAAezG,EAAEC,OAAOC;;;;;8DAKd1L,KAAKwI;YACvDxI,KAAKwI,QAAUX,CAAI,wCAA0C;;;;KAKvE,CAEA,kBAAAyK,GACE,OAAOzK,CAAI;;;;;;UAML7H,KAAKiI,OAAOV,IAAI,CAACc,EAAOkK,IAAU1K,CAAI;;;0CAGN0K,EAAQ;;;iCAGjB,IAAMvS,KAAKgS,UAAU3J,EAAMnH,IAAKmH;;;;;;;2CAOtBA,EAAMrF,OAAS;8CACZqF,EAAMpF,UAAY;;;;;;;;;2BASrCoF,EAAMrF,OAAS;2BACdwI,GAAMxL,KAAKkS,iBAAiB7J,EAAMnH,IAAK,QAASsK,EAAEC,OAAOC;;;;;;;;;;2BAU1DrD,EAAMpF,UAAY;2BACjBuI,GAAMxL,KAAKkS,iBAAiB7J,EAAMnH,IAAK,WAAYsK,EAAEC,OAAOC;;;;;;;;;;2BAU7DrD,EAAMjF,OAAS;2BACdoI,GAAMxL,KAAKkS,iBAAiB7J,EAAMnH,IAAK,QAASsR,SAAShH,EAAEC,OAAOC;;;;;;;;;2BASnErD,EAAMhF,OAAS,OAAS;4BACtBmI,GAAMxL,KAAKkS,iBAAiB7J,EAAMnH,IAAK,SAA6B,SAAnBsK,EAAEC,OAAOC;;;;;;;;;;KAWrF,CAEA,MAAA9D,GAEE,OAAK5H,KAAK6G,aAAyC,UAA1B7G,KAAK6G,YAAYnD,KAcnCmE,CAAI;;;;;;;;;gCASoC,cAAnB7H,KAAKsR,UAA4B,SAAW;qBACvD,IAAMtR,KAAK6R,UAAU;;;;;gCAKS,YAAnB7R,KAAKsR,UAA0B,SAAW;qBACrD,IAAMtR,KAAK6R,UAAU;;;;;gCAKS,WAAnB7R,KAAKsR,UAAyB,SAAW;qBACpD,IAAMtR,KAAK6R,UAAU;;;;;;;YAOX,cAAnB7R,KAAKsR,UAA4BtR,KAAKmS,kBAAoB;YACvC,YAAnBnS,KAAKsR,UAA0BtR,KAAKqS,sBAAwB;YACzC,WAAnBrS,KAAKsR,UAAyBtR,KAAKsS,qBAAuB;;;MA5CzDzK,CAAI;;;;;;;;;;OAgDf,EAGFC,eAAeC,OAAO,aAAcsJ,GCrsBpC,MAAMoB,UAAqB/L,EACzBC,cAAgBC,CAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA+EnBD,kBAAoB,CAClB8C,QAAS,CAAEtI,KAAMuH,SAGnB,WAAA3I,GACEkH,QACAjH,KAAKyJ,QAAU,EACjB,CAEA,MAAA7B,GACE,MAAM8K,EAAkC,oBAAjB1S,KAAKyJ,QAE5B,OAAO5B,CAAI;;;YAGH6K,EAAiB,KAAO;;;;YAIxBA,EAAiB,MAAQ;;;;YAIzBA,EAAiB,kBAAoB;;;;YAIrCA,EACE,iGACA;;;;;;;YAQDA,EAIC,GAJgB7K,CAAI;qDACmB,IAAMwI,OAAOC,QAAQqC;;;;;;KAOxE,EAGF7K,eAAeC,OAAO,iBAAkB0K,GCxHxC,MAAMG,UAAkBlM,EACtBC,cAAgBC,CAAG;;;;;;;;;;;;;;;;;;;;IAsBnBD,kBAAoB,CAClBkM,aAAc,CAAE1R,KAAMuH,QACtB7B,YAAa,CAAE1F,KAAM2F,SAGvB,WAAA/G,GACEkH,QACAjH,KAAK6S,aAAe,OACpB7S,KAAK6G,YAAc,IACrB,CAEA,eAAAiM,CAAgBpG,GACd1M,KAAKoH,cAAc,IAAIC,YAAY,aAAc,CAC/C+I,OAAQ1D,EAAM0D,SAElB,CAEA,gBAAA2C,CAAiBrG,GACf1M,KAAKoH,cAAc,IAAIC,YAAY,WAAY,CAC7C+I,OAAQ1D,EAAM0D,SAElB,CAEA,UAAA4C,GAEE,GAA0B,UAAtBhT,KAAK6S,gBAA8B7S,KAAK6G,aAAyC,UAA1B7G,KAAK6G,YAAYnD,MAC1E,OAAOmE,CAAI,8DAIb,IAA2B,UAAtB7H,KAAK6S,cAAkD,aAAtB7S,KAAK6S,eAAgC7S,KAAK6G,YAM9E,OALAoD,WAAW,KACTjK,KAAKoH,cAAc,IAAIC,YAAY,WAAY,CAC7C+I,OAAQ,CAAE6C,KAAM,SAEjB,GACIpL,CAAI,2BAA2B7H,KAAK6G,2BAG7C,OAAQ7G,KAAK6S,cACX,IAAK,OACH,OAAOhL,CAAI,2BAA2B7H,KAAK6G,2BAE7C,IAAK,WACH,OAAOgB,CAAI,+BAA+B7H,KAAK6G,+BAEjD,IAAK,WACH,OAAOgB,CAAI,kCAEb,IAAK,QACH,OAAOA,CAAI,4BAEb,IAAK,QACH,OAAOA,CAAI,2BAA2B7H,KAAK8S,gCAE7C,IAAK,WACH,OAAOjL,CAAI,8BAA8B7H,KAAK8S,mCAEhD,IAAK,QACH,OAAOjL,CAAI,4BAA4B7H,KAAK6G,4BAE9C,QACE,OAAOgB,CAAI,oCAEjB,CAEA,MAAAD,GACE,OAAOC,CAAI;;UAEL7H,KAAKgT;;KAGb,EAGFlL,eAAeC,OAAO,aAAc6K,GC1GpC,MAAMM,UAAkBxM,EACtBC,cAAgBC,CAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAwJnB,MAAAgB,GACE,OAAOC,CAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAmEb,EAGFC,eAAeC,OAAO,aAAcmL,GC5NpC,MAAMC,UAAmBzM,EACvBC,cAAgBC,CAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAyDnBD,kBAAoB,CAClB6B,QAAS,CAAErH,KAAM6F,SACjBH,YAAa,CAAE1F,KAAM2F,QACrB+L,aAAc,CAAE1R,KAAMuH,SAGxB,WAAA3I,GACEkH,QACAjH,KAAKwI,SAAU,EACfxI,KAAK6G,YAAc,KACnB7G,KAAK6S,aAAe,OAEpB7R,QAAQC,IAAI,2BACZjB,KAAKoT,eACP,CAEA,mBAAMA,GACJ,UAEQpT,KAAKqT,qBAGXrT,KAAKsT,mBAGLtT,KAAKuT,eAGLtJ,WAAW,KACTjK,KAAKwI,SAAU,EACf,MAAMgL,EAAiB7J,SAAS8J,eAAe,WAC3CD,GACFA,EAAeE,UAAUC,IAAI,WAE9B,IAEL,CAAE,MAAO5S,GACPC,QAAQD,MAAM,mCAAoCA,GAClDf,KAAKwI,SAAU,CACjB,CACF,CAEA,wBAAM6K,GAEJrS,QAAQC,IAAI,+BACd,CAEA,gBAAAqS,GAEE,MAAM9F,EAAYC,aAAaC,QAAQ,eACvC,GAAIF,EACF,IACExN,KAAK6G,YAAc8G,KAAKC,MAAMJ,EAChC,CAAE,MAAOzM,GACPC,QAAQD,MAAM,gCAAiCA,GAC/C0M,aAAaI,WAAW,cAC1B,CAEJ,CAEA,YAAA0F,GAEElD,OAAOuD,iBAAiB,WAAY,KAClC5T,KAAK6T,gBAIPlK,SAASiK,iBAAiB,QAAUpI,IAClC,GAAIA,EAAEC,OAAOqI,QAAQ,iBAAmBtI,EAAEC,OAAOsI,QAAQ,gBAAiB,CACxEvI,EAAE0E,iBACF,MAAM8D,EAAOxI,EAAEC,OAAOqI,QAAQ,KAAOtI,EAAEC,OAASD,EAAEC,OAAOsI,QAAQ,KACjE/T,KAAKiU,WAAWD,EAAKE,aAAa,QACpC,IAIFlU,KAAK6T,aACP,CAEA,WAAAA,GACE,MAAMZ,EAAO5C,OAAO8D,SAASC,SAC7BpU,KAAK6S,aAAwB,MAATI,EAAe,OAASA,EAAKtL,MAAM,GACvD3H,KAAKoI,eACP,CAEA,UAAA6L,CAAWhB,GACT5C,OAAOC,QAAQC,UAAU,CAAA,EAAI,GAAI0C,GACjCjT,KAAK6T,aACP,CAEA,eAAAf,CAAgBpG,GACd1M,KAAK6G,YAAc6F,EAAM0D,OAAOpM,KAChCyJ,aAAaM,QAAQ,cAAeJ,KAAKK,UAAUhO,KAAK6G,cACxD7G,KAAKoI,eACP,CAEA,gBAAAiM,GACErU,KAAK6G,YAAc,KACnB4G,aAAaI,WAAW,eACxB7N,KAAKiU,WAAW,KAChBjU,KAAKoI,eACP,CAEA,MAAAR,GACE,OAAOC,CAAI;;;yBAGU7H,KAAK6G;yBACL7G,KAAKqU;;;;;4BAKFrU,KAAK6S;2BACN7S,KAAK6G;0BACN7G,KAAK8S;wBACNtH,GAAMxL,KAAKiU,WAAWzI,EAAE4E,OAAO6C;;;;;;UAM9CjT,KAAKwI,QAAUX,CAAI;;;;;;;UAOjB;;KAGV,EAGFC,eAAeC,OAAO,cAAeoL"}