import{LitElement as t,css as a,html as r}from"../../../../node_modules/lit/index.js";const e=new class{constructor(){this.users=new PouchDB("users"),this.trainers=new PouchDB("trainers"),this.workouts=new PouchDB("workouts"),this.schedule=new PouchDB("schedule"),this.bookings=new PouchDB("bookings"),this.content=new PouchDB("content"),this.slider=new PouchDB("slider"),this.remoteUrl="http://localhost:5984",this.isOnline=!1,this.initializeData()}async initializeData(){try{0===(await this.users.info()).doc_count&&await this.seedInitialData()}catch(t){console.log("Инициализация данных:",t),await this.seedInitialData()}}async seedInitialData(){const t=[{_id:"trainer_1",type:"trainer",name:"Анна Петрова",specialization:["йога","пилатес"],bio:"Сертифицированный инструктор йоги с 8-летним опытом. Специализируется на хатха-йоге и пилатесе.",photo:"https://via.placeholder.com/300x300/2563eb/ffffff?text=Анна+Петрова",experience:"8 лет"},{_id:"trainer_2",type:"trainer",name:"Михаил Сидоров",specialization:["функциональный тренинг","кроссфит"],bio:"Мастер спорта по тяжелой атлетике. Специалист по функциональному тренингу и кроссфиту.",photo:"https://via.placeholder.com/300x300/10b981/ffffff?text=Михаил+Сидоров",experience:"12 лет"},{_id:"trainer_3",type:"trainer",name:"Елена Козлова",specialization:["танцы","аэробика"],bio:"Хореограф и фитнес-инструктор. Ведет занятия по современным танцам и аэробике.",photo:"https://via.placeholder.com/300x300/f59e0b/ffffff?text=Елена+Козлова",experience:"6 лет"}],a=[{_id:"workout_1",type:"workout",name:"Хатха-йога",description:"Классическая йога для начинающих и продолжающих. Развитие гибкости, силы и баланса.",duration:90,max_participants:12,difficulty:"beginner"},{_id:"workout_2",type:"workout",name:"Функциональный тренинг",description:"Интенсивная тренировка с использованием собственного веса и функциональных движений.",duration:60,max_participants:8,difficulty:"intermediate"},{_id:"workout_3",type:"workout",name:"Танцевальная аэробика",description:"Энергичная тренировка под музыку с элементами современных танцев.",duration:60,max_participants:15,difficulty:"beginner"},{_id:"workout_4",type:"workout",name:"Пилатес",description:"Система упражнений для укрепления мышц кора, улучшения осанки и гибкости.",duration:60,max_participants:10,difficulty:"beginner"}],r=[],e=new Date;for(let i=0;i<7;i++){const s=new Date(e);s.setDate(e.getDate()+i);const o=s.toISOString().split("T")[0];r.push({_id:`schedule_${i}_morning`,type:"schedule",workout_id:a[i%a.length]._id,trainer_id:t[i%t.length]._id,date:o,time:"09:00",duration:a[i%a.length].duration,max_participants:a[i%a.length].max_participants,current_participants:Math.floor(5*Math.random())}),r.push({_id:`schedule_${i}_evening`,type:"schedule",workout_id:a[(i+1)%a.length]._id,trainer_id:t[(i+1)%t.length]._id,date:o,time:"19:00",duration:a[(i+1)%a.length].duration,max_participants:a[(i+1)%a.length].max_participants,current_participants:Math.floor(8*Math.random())})}const i=[{_id:"content_home",type:"content",page:"home",title:"Добро пожаловать в FitStudio",subtitle:"Ваш путь к здоровью и красоте",description:"Современная фитнес-студия с профессиональными тренерами и разнообразными программами тренировок.",updated_at:(new Date).toISOString()}],s=[{_id:"slide_1",type:"slide",title:"Йога и медитация",subtitle:"Найдите гармонию тела и души",image:"https://via.placeholder.com/800x400/2563eb/ffffff?text=Йога+и+медитация",order:1,active:!0},{_id:"slide_2",type:"slide",title:"Функциональный тренинг",subtitle:"Развивайте силу и выносливость",image:"https://via.placeholder.com/800x400/10b981/ffffff?text=Функциональный+тренинг",order:2,active:!0}],o={_id:"user_admin",type:"user",email:"<EMAIL>",name:"Администратор",phone:"+7 (495) 123-45-67",password_hash:"$2a$10$N9qo8uLOickgx2ZMRZoMye.IjPeOXANBjH6dUeAMgMxudsKBC1o16",role:"admin",created_at:(new Date).toISOString()};try{await this.trainers.bulkDocs(t),await this.workouts.bulkDocs(a),await this.schedule.bulkDocs(r),await this.content.bulkDocs(i),await this.slider.bulkDocs(s),await this.users.put(o),console.log("Начальные данные успешно созданы")}catch(t){console.error("Ошибка при создании начальных данных:",t)}}async createUser(t){const a={_id:`user_${Date.now()}`,type:"user",...t,role:"user",created_at:(new Date).toISOString()};return await this.users.put(a)}async getUserByEmail(t){try{const a=(await this.users.allDocs({include_docs:!0})).rows.find(a=>"user"===a.doc.type&&a.doc.email===t);return a?a.doc:null}catch(t){return null}}async getSchedule(t=null){try{let a=(await this.schedule.allDocs({include_docs:!0})).rows.filter(t=>"schedule"===t.doc.type).map(t=>t.doc);return t&&(a=a.filter(a=>a.date===t)),a.sort((t,a)=>t.date!==a.date?t.date.localeCompare(a.date):t.time.localeCompare(a.time)),a}catch(t){return console.error("Ошибка получения расписания:",t),[]}}async createBooking(t,a){const r=await this.schedule.get(a);if(r.current_participants>=r.max_participants)throw new Error("Нет свободных мест");if((await this.bookings.allDocs({include_docs:!0})).rows.find(r=>"booking"===r.doc.type&&r.doc.user_id===t&&r.doc.schedule_id===a&&"active"===r.doc.status))throw new Error("Вы уже записаны на эту тренировку");const e={_id:`booking_${Date.now()}`,type:"booking",user_id:t,schedule_id:a,status:"active",booked_at:(new Date).toISOString()};return r.current_participants+=1,await this.schedule.put(r),await this.bookings.put(e)}async cancelBooking(t,a){const r=await this.bookings.get(t);if(r.user_id!==a)throw new Error("Нет прав для отмены этой записи");const e=await this.schedule.get(r.schedule_id),i=new Date(`${e.date}T${e.time}`);if(new Date>new Date(i.getTime()-36e5))throw new Error("Отмена возможна не позднее чем за час до начала тренировки");return r.status="cancelled",r.cancelled_at=(new Date).toISOString(),e.current_participants-=1,await this.schedule.put(e),await this.bookings.put(r)}async getUserBookings(t){try{const a=(await this.bookings.allDocs({include_docs:!0})).rows.filter(a=>"booking"===a.doc.type&&a.doc.user_id===t).map(t=>t.doc);return a.sort((t,a)=>new Date(a.booked_at)-new Date(t.booked_at)),a}catch(t){return console.error("Ошибка получения записей пользователя:",t),[]}}async updateContent(t,a){try{const r={...await this.content.get(`content_${t}`),...a,updated_at:(new Date).toISOString()};return await this.content.put(r)}catch(r){const e={_id:`content_${t}`,type:"content",page:t,...a,updated_at:(new Date).toISOString()};return await this.content.put(e)}}async getContent(t){try{return await this.content.get(`content_${t}`)}catch(t){return null}}async getSlides(){try{const t=(await this.slider.allDocs({include_docs:!0})).rows.filter(t=>"slide"===t.doc.type&&!0===t.doc.active).map(t=>t.doc);return t.sort((t,a)=>t.order-a.order),t}catch(t){return console.error("Ошибка получения слайдов:",t),[]}}async updateSlide(t,a){try{const r={...await this.slider.get(t),...a,updated_at:(new Date).toISOString()};return await this.slider.put(r)}catch(t){throw console.error("Ошибка обновления слайда:",t),t}}async getTrainers(){try{const t=await this.trainers.allDocs({include_docs:!0});return t.rows.filter(t=>"trainer"===t.doc.type).map(t=>t.doc)}catch(t){return console.error("Ошибка получения тренеров:",t),[]}}async getWorkouts(){try{const t=await this.workouts.allDocs({include_docs:!0});return t.rows.filter(t=>"workout"===t.doc.type).map(t=>t.doc)}catch(t){return console.error("Ошибка получения тренировок:",t),[]}}async getTrainer(t){try{return await this.trainers.get(t)}catch(t){return null}}async getWorkout(t){try{return await this.workouts.get(t)}catch(t){return null}}};class i extends t{static styles=a`
    :host {
      display: block;
      background: white;
      box-shadow: var(--shadow-sm);
      position: sticky;
      top: 0;
      z-index: 100;
    }

    .header {
      max-width: var(--container-xl);
      margin: 0 auto;
      padding: 0 var(--spacing-4);
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 70px;
    }

    .logo {
      display: flex;
      align-items: center;
      gap: var(--spacing-3);
      font-size: var(--font-size-xl);
      font-weight: 700;
      color: var(--primary-color);
      text-decoration: none;
    }

    .logo-icon {
      width: 40px;
      height: 40px;
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      border-radius: var(--radius-lg);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: bold;
      font-size: var(--font-size-lg);
    }

    .nav {
      display: flex;
      align-items: center;
      gap: var(--spacing-6);
    }

    .nav-links {
      display: flex;
      align-items: center;
      gap: var(--spacing-6);
      list-style: none;
      margin: 0;
      padding: 0;
    }

    .nav-link {
      color: var(--gray-600);
      text-decoration: none;
      font-weight: 500;
      padding: var(--spacing-2) var(--spacing-3);
      border-radius: var(--radius-md);
      transition: all var(--transition-fast);
    }

    .nav-link:hover {
      color: var(--primary-color);
      background-color: var(--gray-50);
    }

    .nav-link.active {
      color: var(--primary-color);
      background-color: var(--primary-color);
      background-color: rgb(37 99 235 / 0.1);
    }

    .user-menu {
      display: flex;
      align-items: center;
      gap: var(--spacing-4);
    }

    .user-info {
      display: flex;
      align-items: center;
      gap: var(--spacing-3);
      color: var(--gray-700);
    }

    .user-avatar {
      width: 36px;
      height: 36px;
      border-radius: 50%;
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: 600;
      font-size: var(--font-size-sm);
    }

    .mobile-menu-btn {
      display: none;
      background: none;
      border: none;
      font-size: var(--font-size-xl);
      color: var(--gray-600);
      cursor: pointer;
      padding: var(--spacing-2);
    }

    .mobile-nav {
      display: none;
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      background: white;
      box-shadow: var(--shadow-lg);
      padding: var(--spacing-4);
    }

    .mobile-nav.open {
      display: block;
    }

    .mobile-nav-links {
      list-style: none;
      margin: 0;
      padding: 0;
    }

    .mobile-nav-links li {
      margin-bottom: var(--spacing-2);
    }

    .mobile-nav-link {
      display: block;
      padding: var(--spacing-3);
      color: var(--gray-600);
      text-decoration: none;
      border-radius: var(--radius-md);
      transition: all var(--transition-fast);
    }

    .mobile-nav-link:hover {
      background-color: var(--gray-50);
      color: var(--primary-color);
    }

    @media (max-width: 768px) {
      .nav-links {
        display: none;
      }

      .mobile-menu-btn {
        display: block;
      }

      .user-info span {
        display: none;
      }
    }
  `;static properties={currentUser:{type:Object},mobileMenuOpen:{type:Boolean}};constructor(){super(),this.currentUser=null,this.mobileMenuOpen=!1}toggleMobileMenu(){this.mobileMenuOpen=!this.mobileMenuOpen}handleLogout(){this.dispatchEvent(new CustomEvent("user-logout"))}getUserInitials(t){return t?t.split(" ").map(t=>t[0]).join("").toUpperCase().slice(0,2):"U"}render(){return r`
      <header class="header">
        <a href="/" class="logo">
          <div class="logo-icon">FS</div>
          <span>FitStudio</span>
        </a>

        <nav class="nav">
          <ul class="nav-links">
            <li><a href="/" class="nav-link">Главная</a></li>
            <li><a href="/schedule" class="nav-link">Расписание</a></li>
            <li><a href="/trainers" class="nav-link">Тренеры</a></li>
            <li><a href="/about" class="nav-link">О студии</a></li>
            ${"admin"===this.currentUser?.role?r`
              <li><a href="/admin" class="nav-link">Админ</a></li>
            `:""}
          </ul>

          <div class="user-menu">
            ${this.currentUser?r`
              <div class="user-info">
                <div class="user-avatar">
                  ${this.getUserInitials(this.currentUser.name)}
                </div>
                <span>${this.currentUser.name}</span>
              </div>
              <button class="btn btn-ghost btn-sm" @click=${this.handleLogout}>
                Выйти
              </button>
            `:r`
              <a href="/login" class="btn btn-outline btn-sm">Войти</a>
              <a href="/register" class="btn btn-primary btn-sm">Регистрация</a>
            `}
          </div>

          <button class="mobile-menu-btn" @click=${this.toggleMobileMenu}>
            ☰
          </button>
        </nav>

        <div class="mobile-nav ${this.mobileMenuOpen?"open":""}">
          <ul class="mobile-nav-links">
            <li><a href="/" class="mobile-nav-link" @click=${()=>this.mobileMenuOpen=!1}>Главная</a></li>
            <li><a href="/schedule" class="mobile-nav-link" @click=${()=>this.mobileMenuOpen=!1}>Расписание</a></li>
            <li><a href="/trainers" class="mobile-nav-link" @click=${()=>this.mobileMenuOpen=!1}>Тренеры</a></li>
            <li><a href="/about" class="mobile-nav-link" @click=${()=>this.mobileMenuOpen=!1}>О студии</a></li>
            ${"admin"===this.currentUser?.role?r`
              <li><a href="/admin" class="mobile-nav-link" @click=${()=>this.mobileMenuOpen=!1}>Админ</a></li>
            `:""}
            ${this.currentUser?r`
              <li>
                <button class="mobile-nav-link" style="width: 100%; text-align: left; background: none; border: none; cursor: pointer;" 
                        @click=${()=>{this.handleLogout(),this.mobileMenuOpen=!1}}>
                  Выйти
                </button>
              </li>
            `:r`
              <li><a href="/login" class="mobile-nav-link" @click=${()=>this.mobileMenuOpen=!1}>Войти</a></li>
              <li><a href="/register" class="mobile-nav-link" @click=${()=>this.mobileMenuOpen=!1}>Регистрация</a></li>
            `}
          </ul>
        </div>
      </header>
    `}}customElements.define("app-header",i);class s extends t{static styles=a`
    :host {
      display: block;
    }

    .hero {
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      color: white;
      padding: var(--spacing-20) 0;
      text-align: center;
      position: relative;
      overflow: hidden;
    }

    .hero::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(45deg, rgba(37, 99, 235, 0.1), rgba(16, 185, 129, 0.1));
      opacity: 0.2;
      z-index: 0;
    }

    .hero-content {
      position: relative;
      z-index: 1;
      max-width: var(--container-lg);
      margin: 0 auto;
      padding: 0 var(--spacing-4);
    }

    .hero h1 {
      font-size: var(--font-size-4xl);
      font-weight: 700;
      margin-bottom: var(--spacing-6);
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .hero p {
      font-size: var(--font-size-xl);
      margin-bottom: var(--spacing-8);
      opacity: 0.9;
    }

    .hero-buttons {
      display: flex;
      gap: var(--spacing-4);
      justify-content: center;
      flex-wrap: wrap;
    }

    .hero-buttons .btn {
      padding: var(--spacing-4) var(--spacing-8);
      font-size: var(--font-size-lg);
      font-weight: 600;
    }

    .btn-white {
      background-color: white;
      color: var(--primary-color);
      border: 2px solid white;
    }

    .btn-white:hover {
      background-color: transparent;
      color: white;
    }

    .slider {
      margin: var(--spacing-16) 0;
    }

    .slider-container {
      max-width: var(--container-xl);
      margin: 0 auto;
      padding: 0 var(--spacing-4);
    }

    .slider-title {
      text-align: center;
      margin-bottom: var(--spacing-8);
    }

    .slides {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: var(--spacing-6);
    }

    .slide {
      background: white;
      border-radius: var(--radius-xl);
      overflow: hidden;
      box-shadow: var(--shadow-lg);
      transition: transform var(--transition-normal);
    }

    .slide:hover {
      transform: translateY(-5px);
    }

    .slide-image {
      width: 100%;
      height: 200px;
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: var(--font-size-4xl);
    }

    .slide-content {
      padding: var(--spacing-6);
    }

    .slide h3 {
      font-size: var(--font-size-xl);
      margin-bottom: var(--spacing-3);
      color: var(--gray-800);
    }

    .slide p {
      color: var(--gray-600);
      margin-bottom: 0;
    }

    .features {
      background-color: white;
      padding: var(--spacing-16) 0;
    }

    .features-container {
      max-width: var(--container-xl);
      margin: 0 auto;
      padding: 0 var(--spacing-4);
    }

    .features-title {
      text-align: center;
      margin-bottom: var(--spacing-12);
    }

    .features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: var(--spacing-8);
    }

    .feature {
      text-align: center;
      padding: var(--spacing-6);
    }

    .feature-icon {
      width: 80px;
      height: 80px;
      margin: 0 auto var(--spacing-4);
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: var(--font-size-2xl);
    }

    .feature h3 {
      font-size: var(--font-size-xl);
      margin-bottom: var(--spacing-3);
      color: var(--gray-800);
    }

    .feature p {
      color: var(--gray-600);
      margin-bottom: 0;
    }

    .cta {
      background: linear-gradient(135deg, var(--gray-800), var(--gray-700));
      color: white;
      padding: var(--spacing-16) 0;
      text-align: center;
    }

    .cta-container {
      max-width: var(--container-lg);
      margin: 0 auto;
      padding: 0 var(--spacing-4);
    }

    .cta h2 {
      font-size: var(--font-size-3xl);
      margin-bottom: var(--spacing-4);
    }

    .cta p {
      font-size: var(--font-size-lg);
      margin-bottom: var(--spacing-8);
      opacity: 0.9;
    }

    @media (max-width: 768px) {
      .hero h1 {
        font-size: var(--font-size-3xl);
      }

      .hero p {
        font-size: var(--font-size-lg);
      }

      .hero-buttons {
        flex-direction: column;
        align-items: center;
      }

      .hero-buttons .btn {
        width: 100%;
        max-width: 300px;
      }

      .slides {
        grid-template-columns: 1fr;
      }

      .features-grid {
        grid-template-columns: 1fr;
      }
    }
  `;static properties={currentUser:{type:Object},slides:{type:Array},content:{type:Object}};constructor(){super(),this.currentUser=null,this.slides=[],this.content=null,this.loadData()}async loadData(){try{this.slides=await e.getSlides(),this.content=await e.getContent("home"),this.requestUpdate()}catch(t){console.error("Ошибка загрузки данных:",t)}}render(){return r`
      <div class="hero">
        <div class="hero-content">
          <h1>${this.content?.title||"Добро пожаловать в FitStudio"}</h1>
          <p>${this.content?.subtitle||"Ваш путь к здоровью и красоте"}</p>
          <div class="hero-buttons">
            ${this.currentUser?r`
              <a href="/schedule" class="btn btn-white">Записаться на тренировку</a>
            `:r`
              <a href="/register" class="btn btn-white">Начать заниматься</a>
              <a href="/schedule" class="btn btn-outline" style="border-color: white; color: white;">Посмотреть расписание</a>
            `}
          </div>
        </div>
      </div>

      ${this.slides.length>0?r`
        <section class="slider">
          <div class="slider-container">
            <h2 class="slider-title">Наши программы</h2>
            <div class="slides">
              ${this.slides.map(t=>r`
                <div class="slide">
                  <div class="slide-image">
                    🏃‍♀️
                  </div>
                  <div class="slide-content">
                    <h3>${t.title}</h3>
                    <p>${t.subtitle}</p>
                  </div>
                </div>
              `)}
            </div>
          </div>
        </section>
      `:""}

      <section class="features">
        <div class="features-container">
          <h2 class="features-title">Почему выбирают нас</h2>
          <div class="features-grid">
            <div class="feature">
              <div class="feature-icon">👨‍🏫</div>
              <h3>Профессиональные тренеры</h3>
              <p>Сертифицированные инструкторы с многолетним опытом помогут достичь ваших целей</p>
            </div>
            <div class="feature">
              <div class="feature-icon">🏋️‍♀️</div>
              <h3>Современное оборудование</h3>
              <p>Новейшее фитнес-оборудование от ведущих мировых производителей</p>
            </div>
            <div class="feature">
              <div class="feature-icon">📅</div>
              <h3>Удобное расписание</h3>
              <p>Гибкое расписание занятий, которое подойдет для любого образа жизни</p>
            </div>
            <div class="feature">
              <div class="feature-icon">🎯</div>
              <h3>Индивидуальный подход</h3>
              <p>Персональные программы тренировок с учетом ваших особенностей и целей</p>
            </div>
          </div>
        </div>
      </section>

      <section class="cta">
        <div class="cta-container">
          <h2>Готовы начать свой путь к здоровью?</h2>
          <p>Присоединяйтесь к нашему сообществу и откройте для себя мир фитнеса</p>
          ${this.currentUser?r`
            <a href="/schedule" class="btn btn-primary btn-lg">Записаться на тренировку</a>
          `:r`
            <a href="/register" class="btn btn-primary btn-lg">Зарегистрироваться сейчас</a>
          `}
        </div>
      </section>
    `}}customElements.define("home-page",s);class o extends t{static styles=a`
    :host {
      display: block;
    }

    .schedule-container {
      max-width: var(--container-xl);
      margin: 0 auto;
      padding: var(--spacing-6) var(--spacing-4);
    }

    .schedule-header {
      text-align: center;
      margin-bottom: var(--spacing-8);
    }

    .schedule-title {
      font-size: var(--font-size-3xl);
      font-weight: 700;
      color: var(--gray-800);
      margin-bottom: var(--spacing-4);
    }

    .schedule-subtitle {
      font-size: var(--font-size-lg);
      color: var(--gray-600);
      max-width: 600px;
      margin: 0 auto;
    }

    .schedule-filters {
      background: white;
      border-radius: var(--radius-xl);
      box-shadow: var(--shadow-sm);
      padding: var(--spacing-6);
      margin-bottom: var(--spacing-8);
      display: flex;
      gap: var(--spacing-4);
      align-items: center;
      flex-wrap: wrap;
    }

    .filter-group {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-2);
    }

    .filter-label {
      font-weight: 500;
      color: var(--gray-700);
      font-size: var(--font-size-sm);
    }

    .filter-select {
      padding: var(--spacing-2) var(--spacing-3);
      border: 2px solid var(--gray-200);
      border-radius: var(--radius-md);
      font-size: var(--font-size-sm);
      background: white;
      cursor: pointer;
    }

    .filter-select:focus {
      outline: none;
      border-color: var(--primary-color);
    }

    .date-navigation {
      display: flex;
      align-items: center;
      gap: var(--spacing-4);
      margin-left: auto;
    }

    .date-nav-btn {
      background: none;
      border: 2px solid var(--gray-200);
      border-radius: var(--radius-md);
      padding: var(--spacing-2);
      cursor: pointer;
      color: var(--gray-600);
      transition: all var(--transition-fast);
    }

    .date-nav-btn:hover {
      border-color: var(--primary-color);
      color: var(--primary-color);
    }

    .current-date {
      font-weight: 600;
      color: var(--gray-800);
      min-width: 120px;
      text-align: center;
    }

    .schedule-grid {
      display: grid;
      gap: var(--spacing-6);
    }

    .day-section {
      background: white;
      border-radius: var(--radius-xl);
      box-shadow: var(--shadow-sm);
      overflow: hidden;
    }

    .day-header {
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      color: white;
      padding: var(--spacing-4) var(--spacing-6);
      text-align: center;
    }

    .day-title {
      font-size: var(--font-size-lg);
      font-weight: 600;
      margin-bottom: var(--spacing-1);
    }

    .day-date {
      font-size: var(--font-size-sm);
      opacity: 0.9;
    }

    .workouts-list {
      padding: var(--spacing-6);
    }

    .workout-card {
      border: 2px solid var(--gray-100);
      border-radius: var(--radius-lg);
      padding: var(--spacing-4);
      margin-bottom: var(--spacing-4);
      transition: all var(--transition-fast);
      cursor: pointer;
    }

    .workout-card:hover {
      border-color: var(--primary-color);
      box-shadow: var(--shadow-md);
      transform: translateY(-2px);
    }

    .workout-card:last-child {
      margin-bottom: 0;
    }

    .workout-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: var(--spacing-3);
    }

    .workout-info h3 {
      font-size: var(--font-size-lg);
      font-weight: 600;
      color: var(--gray-800);
      margin-bottom: var(--spacing-1);
    }

    .workout-time {
      font-size: var(--font-size-sm);
      color: var(--gray-600);
      display: flex;
      align-items: center;
      gap: var(--spacing-1);
    }

    .workout-status {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      gap: var(--spacing-2);
    }

    .participants-info {
      font-size: var(--font-size-sm);
      color: var(--gray-600);
      text-align: right;
    }

    .participants-count {
      font-weight: 600;
      color: var(--primary-color);
    }

    .workout-details {
      display: grid;
      grid-template-columns: 1fr auto;
      gap: var(--spacing-4);
      align-items: center;
    }

    .workout-meta {
      display: flex;
      gap: var(--spacing-4);
      font-size: var(--font-size-sm);
      color: var(--gray-600);
    }

    .meta-item {
      display: flex;
      align-items: center;
      gap: var(--spacing-1);
    }

    .difficulty-badge {
      padding: var(--spacing-1) var(--spacing-2);
      border-radius: var(--radius-sm);
      font-size: var(--font-size-xs);
      font-weight: 500;
      text-transform: uppercase;
    }

    .difficulty-beginner {
      background-color: rgb(34 197 94 / 0.1);
      color: var(--success-color);
    }

    .difficulty-intermediate {
      background-color: rgb(251 191 36 / 0.1);
      color: var(--warning-color);
    }

    .difficulty-advanced {
      background-color: rgb(239 68 68 / 0.1);
      color: var(--error-color);
    }

    .book-btn {
      padding: var(--spacing-2) var(--spacing-4);
      background-color: var(--primary-color);
      color: white;
      border: none;
      border-radius: var(--radius-md);
      font-size: var(--font-size-sm);
      font-weight: 500;
      cursor: pointer;
      transition: all var(--transition-fast);
    }

    .book-btn:hover:not(:disabled) {
      background-color: var(--primary-dark);
      transform: translateY(-1px);
    }

    .book-btn:disabled {
      background-color: var(--gray-400);
      cursor: not-allowed;
      transform: none;
    }

    .book-btn.booked {
      background-color: var(--success-color);
    }

    .book-btn.full {
      background-color: var(--error-color);
    }

    .empty-state {
      text-align: center;
      padding: var(--spacing-12);
      color: var(--gray-500);
    }

    .empty-state-icon {
      font-size: 4rem;
      margin-bottom: var(--spacing-4);
    }

    .loading-state {
      text-align: center;
      padding: var(--spacing-12);
    }

    .spinner {
      width: 40px;
      height: 40px;
      border: 4px solid var(--gray-200);
      border-top: 4px solid var(--primary-color);
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto var(--spacing-4);
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .notification {
      position: fixed;
      top: var(--spacing-4);
      right: var(--spacing-4);
      padding: var(--spacing-4) var(--spacing-6);
      border-radius: var(--radius-lg);
      color: white;
      font-weight: 500;
      z-index: 1000;
      animation: slideIn 0.3s ease-out;
    }

    .notification.success {
      background-color: var(--success-color);
    }

    .notification.error {
      background-color: var(--error-color);
    }

    @keyframes slideIn {
      from {
        transform: translateX(100%);
        opacity: 0;
      }
      to {
        transform: translateX(0);
        opacity: 1;
      }
    }

    @media (max-width: 768px) {
      .schedule-filters {
        flex-direction: column;
        align-items: stretch;
      }

      .date-navigation {
        margin-left: 0;
        justify-content: center;
      }

      .workout-header {
        flex-direction: column;
        gap: var(--spacing-2);
      }

      .workout-details {
        grid-template-columns: 1fr;
        gap: var(--spacing-3);
      }

      .workout-meta {
        flex-wrap: wrap;
      }
    }
  `;static properties={currentUser:{type:Object},schedule:{type:Array},workouts:{type:Array},trainers:{type:Array},userBookings:{type:Array},loading:{type:Boolean},selectedDate:{type:String},filterWorkout:{type:String},filterTrainer:{type:String}};constructor(){super(),this.currentUser=null,this.schedule=[],this.workouts=[],this.trainers=[],this.userBookings=[],this.loading=!0,this.selectedDate=(new Date).toISOString().split("T")[0],this.filterWorkout="",this.filterTrainer="",this.loadData()}async loadData(){try{this.loading=!0;const[t,a,r]=await Promise.all([e.getSchedule(),e.getWorkouts(),e.getTrainers()]);this.schedule=t,this.workouts=a,this.trainers=r,this.currentUser&&(this.userBookings=await e.getUserBookings(this.currentUser._id))}catch(t){console.error("Ошибка загрузки данных:",t),this.showNotification("Ошибка загрузки данных","error")}finally{this.loading=!1}}getWorkoutById(t){return this.workouts.find(a=>a._id===t)}getTrainerById(t){return this.trainers.find(a=>a._id===t)}isUserBooked(t){return this.userBookings.some(a=>a.schedule_id===t&&"active"===a.status)}getUserBooking(t){return this.userBookings.find(a=>a.schedule_id===t&&"active"===a.status)}canCancelBooking(t){const a=new Date(`${t.date}T${t.time}`);return new Date<=new Date(a.getTime()-36e5)}async handleBooking(t){if(this.currentUser)try{if(this.isUserBooked(t._id)){if(!this.canCancelBooking(t))return void this.showNotification("Отмена возможна не позднее чем за час до начала тренировки","error");const a=this.getUserBooking(t._id);await e.cancelBooking(a._id,this.currentUser._id),this.showNotification("Запись отменена","success")}else{if(t.current_participants>=t.max_participants)return void this.showNotification("Нет свободных мест","error");await e.createBooking(this.currentUser._id,t._id),this.showNotification("Вы успешно записались на тренировку","success")}await this.loadData()}catch(t){console.error("Ошибка при записи/отмене:",t),this.showNotification(t.message||"Произошла ошибка","error")}else this.showNotification("Необходимо войти в систему для записи на тренировки","error")}showNotification(t,a="info"){const r=document.createElement("div");r.className=`notification ${a}`,r.textContent=t,document.body.appendChild(r),setTimeout(()=>{r.remove()},5e3)}changeDate(t){const a=new Date(this.selectedDate);a.setDate(a.getDate()+t),this.selectedDate=a.toISOString().split("T")[0]}formatDate(t){return new Date(t).toLocaleDateString("ru-RU",{weekday:"long",year:"numeric",month:"long",day:"numeric"})}formatTime(t){return t.slice(0,5)}getDifficultyText(t){return{beginner:"Начинающий",intermediate:"Средний",advanced:"Продвинутый"}[t]||t}getFilteredSchedule(){let t=this.schedule;return t=t.filter(t=>t.date===this.selectedDate),this.filterWorkout&&(t=t.filter(t=>t.workout_id===this.filterWorkout)),this.filterTrainer&&(t=t.filter(t=>t.trainer_id===this.filterTrainer)),t}render(){if(this.loading)return r`
        <div class="schedule-container">
          <div class="loading-state">
            <div class="spinner"></div>
            <p>Загрузка расписания...</p>
          </div>
        </div>
      `;const t=this.getFilteredSchedule();return r`
      <div class="schedule-container">
        <div class="schedule-header">
          <h1 class="schedule-title">Расписание тренировок</h1>
          <p class="schedule-subtitle">
            Выберите подходящую тренировку и запишитесь онлайн. 
            Отмена возможна не позднее чем за час до начала занятия.
          </p>
        </div>

        <div class="schedule-filters">
          <div class="filter-group">
            <label class="filter-label">Тип тренировки</label>
            <select class="filter-select" .value=${this.filterWorkout} 
                    @change=${t=>this.filterWorkout=t.target.value}>
              <option value="">Все тренировки</option>
              ${this.workouts.map(t=>r`
                <option value=${t._id}>${t.name}</option>
              `)}
            </select>
          </div>

          <div class="filter-group">
            <label class="filter-label">Тренер</label>
            <select class="filter-select" .value=${this.filterTrainer} 
                    @change=${t=>this.filterTrainer=t.target.value}>
              <option value="">Все тренеры</option>
              ${this.trainers.map(t=>r`
                <option value=${t._id}>${t.name}</option>
              `)}
            </select>
          </div>

          <div class="date-navigation">
            <button class="date-nav-btn" @click=${()=>this.changeDate(-1)}>
              ←
            </button>
            <div class="current-date">
              ${this.formatDate(this.selectedDate)}
            </div>
            <button class="date-nav-btn" @click=${()=>this.changeDate(1)}>
              →
            </button>
          </div>
        </div>

        <div class="schedule-grid">
          ${t.length>0?r`
            <div class="day-section">
              <div class="day-header">
                <div class="day-title">${this.formatDate(this.selectedDate)}</div>
                <div class="day-date">${t.length} тренировок</div>
              </div>
              <div class="workouts-list">
                ${t.map(t=>{const a=this.getWorkoutById(t.workout_id),e=this.getTrainerById(t.trainer_id),i=this.isUserBooked(t._id),s=t.current_participants>=t.max_participants,o=i&&this.canCancelBooking(t);return r`
                    <div class="workout-card">
                      <div class="workout-header">
                        <div class="workout-info">
                          <h3>${a?.name||"Неизвестная тренировка"}</h3>
                          <div class="workout-time">
                            🕐 ${this.formatTime(t.time)} - ${this.formatTime(new Date(new Date(`2000-01-01T${t.time}`).getTime()+6e4*t.duration).toTimeString().slice(0,5))}
                          </div>
                        </div>
                        <div class="workout-status">
                          <div class="participants-info">
                            <span class="participants-count">${t.current_participants}</span>
                            /${t.max_participants} мест
                          </div>
                        </div>
                      </div>
                      
                      <div class="workout-details">
                        <div class="workout-meta">
                          <div class="meta-item">
                            👨‍🏫 ${e?.name||"Неизвестный тренер"}
                          </div>
                          <div class="meta-item">
                            ⏱️ ${t.duration} мин
                          </div>
                          <div class="meta-item">
                            <span class="difficulty-badge difficulty-${a?.difficulty}">
                              ${this.getDifficultyText(a?.difficulty)}
                            </span>
                          </div>
                        </div>
                        
                        ${this.currentUser?r`
                          <button 
                            class="book-btn ${i?"booked":""} ${s&&!i?"full":""}"
                            ?disabled=${s&&!i}
                            @click=${()=>this.handleBooking(t)}
                          >
                            ${i?o?"Отменить запись":"Записан":s?"Нет мест":"Записаться"}
                          </button>
                        `:r`
                          <a href="/login" class="book-btn">
                            Войти для записи
                          </a>
                        `}
                      </div>
                    </div>
                  `})}
              </div>
            </div>
          `:r`
            <div class="empty-state">
              <div class="empty-state-icon">📅</div>
              <h3>Нет тренировок на выбранную дату</h3>
              <p>Попробуйте выбрать другую дату или изменить фильтры</p>
            </div>
          `}
        </div>
      </div>
    `}}customElements.define("schedule-page",o);class n extends t{static styles=a`
    :host {
      display: block;
    }

    .trainers-container {
      max-width: var(--container-xl);
      margin: 0 auto;
      padding: var(--spacing-6) var(--spacing-4);
    }

    .trainers-header {
      text-align: center;
      margin-bottom: var(--spacing-12);
    }

    .trainers-title {
      font-size: var(--font-size-3xl);
      font-weight: 700;
      color: var(--gray-800);
      margin-bottom: var(--spacing-4);
    }

    .trainers-subtitle {
      font-size: var(--font-size-lg);
      color: var(--gray-600);
      max-width: 600px;
      margin: 0 auto;
      line-height: 1.6;
    }

    .trainers-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
      gap: var(--spacing-8);
    }

    .trainer-card {
      background: white;
      border-radius: var(--radius-2xl);
      box-shadow: var(--shadow-lg);
      overflow: hidden;
      transition: all var(--transition-normal);
      position: relative;
    }

    .trainer-card:hover {
      transform: translateY(-8px);
      box-shadow: var(--shadow-xl);
    }

    .trainer-image {
      width: 100%;
      height: 280px;
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 4rem;
      position: relative;
      overflow: hidden;
    }

    .trainer-image::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
      transform: translateX(-100%);
      transition: transform 0.6s ease;
    }

    .trainer-card:hover .trainer-image::before {
      transform: translateX(100%);
    }

    .trainer-content {
      padding: var(--spacing-6);
    }

    .trainer-name {
      font-size: var(--font-size-xl);
      font-weight: 700;
      color: var(--gray-800);
      margin-bottom: var(--spacing-2);
    }

    .trainer-specializations {
      display: flex;
      flex-wrap: wrap;
      gap: var(--spacing-2);
      margin-bottom: var(--spacing-4);
    }

    .specialization-tag {
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      color: white;
      padding: var(--spacing-1) var(--spacing-3);
      border-radius: var(--radius-xl);
      font-size: var(--font-size-xs);
      font-weight: 500;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .trainer-bio {
      color: var(--gray-600);
      line-height: 1.6;
      margin-bottom: var(--spacing-4);
    }

    .trainer-experience {
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
      color: var(--gray-700);
      font-weight: 500;
      margin-bottom: var(--spacing-4);
    }

    .experience-icon {
      width: 24px;
      height: 24px;
      background: var(--secondary-color);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: var(--font-size-sm);
    }

    .trainer-actions {
      display: flex;
      gap: var(--spacing-3);
    }

    .action-btn {
      flex: 1;
      padding: var(--spacing-3) var(--spacing-4);
      border: none;
      border-radius: var(--radius-lg);
      font-size: var(--font-size-sm);
      font-weight: 500;
      cursor: pointer;
      transition: all var(--transition-fast);
      text-decoration: none;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: var(--spacing-2);
    }

    .btn-primary {
      background-color: var(--primary-color);
      color: white;
    }

    .btn-primary:hover {
      background-color: var(--primary-dark);
      transform: translateY(-1px);
    }

    .btn-outline {
      background-color: transparent;
      color: var(--primary-color);
      border: 2px solid var(--primary-color);
    }

    .btn-outline:hover {
      background-color: var(--primary-color);
      color: white;
    }

    .loading-state {
      text-align: center;
      padding: var(--spacing-12);
    }

    .spinner {
      width: 40px;
      height: 40px;
      border: 4px solid var(--gray-200);
      border-top: 4px solid var(--primary-color);
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto var(--spacing-4);
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .empty-state {
      text-align: center;
      padding: var(--spacing-12);
      color: var(--gray-500);
    }

    .empty-state-icon {
      font-size: 4rem;
      margin-bottom: var(--spacing-4);
    }

    .stats-section {
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      color: white;
      padding: var(--spacing-12) 0;
      margin: var(--spacing-16) 0;
      border-radius: var(--radius-2xl);
    }

    .stats-container {
      max-width: var(--container-lg);
      margin: 0 auto;
      padding: 0 var(--spacing-4);
      text-align: center;
    }

    .stats-title {
      font-size: var(--font-size-2xl);
      font-weight: 700;
      margin-bottom: var(--spacing-8);
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: var(--spacing-6);
    }

    .stat-item {
      text-align: center;
    }

    .stat-number {
      font-size: var(--font-size-3xl);
      font-weight: 700;
      margin-bottom: var(--spacing-2);
    }

    .stat-label {
      font-size: var(--font-size-base);
      opacity: 0.9;
    }

    .trainer-modal {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1000;
      padding: var(--spacing-4);
    }

    .modal-content {
      background: white;
      border-radius: var(--radius-xl);
      max-width: 600px;
      width: 100%;
      max-height: 90vh;
      overflow-y: auto;
      position: relative;
    }

    .modal-header {
      padding: var(--spacing-6);
      border-bottom: 1px solid var(--gray-200);
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .modal-title {
      font-size: var(--font-size-xl);
      font-weight: 700;
      color: var(--gray-800);
    }

    .close-btn {
      background: none;
      border: none;
      font-size: var(--font-size-xl);
      color: var(--gray-500);
      cursor: pointer;
      padding: var(--spacing-2);
      border-radius: var(--radius-md);
      transition: all var(--transition-fast);
    }

    .close-btn:hover {
      background-color: var(--gray-100);
      color: var(--gray-700);
    }

    .modal-body {
      padding: var(--spacing-6);
    }

    @media (max-width: 768px) {
      .trainers-grid {
        grid-template-columns: 1fr;
      }

      .trainer-actions {
        flex-direction: column;
      }

      .stats-grid {
        grid-template-columns: repeat(2, 1fr);
      }

      .modal-content {
        margin: var(--spacing-4);
        max-height: calc(100vh - 2rem);
      }
    }

    @media (max-width: 480px) {
      .stats-grid {
        grid-template-columns: 1fr;
      }
    }
  `;static properties={trainers:{type:Array},loading:{type:Boolean},selectedTrainer:{type:Object},showModal:{type:Boolean}};constructor(){super(),this.trainers=[],this.loading=!0,this.selectedTrainer=null,this.showModal=!1,this.loadTrainers()}async loadTrainers(){try{this.loading=!0,this.trainers=await e.getTrainers()}catch(t){console.error("Ошибка загрузки тренеров:",t)}finally{this.loading=!1}}openTrainerModal(t){this.selectedTrainer=t,this.showModal=!0,document.body.style.overflow="hidden"}closeTrainerModal(){this.showModal=!1,this.selectedTrainer=null,document.body.style.overflow=""}handleModalClick(t){t.target===t.currentTarget&&this.closeTrainerModal()}getTrainerInitials(t){return t.split(" ").map(t=>t[0]).join("").toUpperCase()}render(){return this.loading?r`
        <div class="trainers-container">
          <div class="loading-state">
            <div class="spinner"></div>
            <p>Загрузка информации о тренерах...</p>
          </div>
        </div>
      `:0===this.trainers.length?r`
        <div class="trainers-container">
          <div class="empty-state">
            <div class="empty-state-icon">👨‍🏫</div>
            <h3>Информация о тренерах недоступна</h3>
            <p>Попробуйте обновить страницу позже</p>
          </div>
        </div>
      `:r`
      <div class="trainers-container">
        <div class="trainers-header">
          <h1 class="trainers-title">Наши тренеры</h1>
          <p class="trainers-subtitle">
            Познакомьтесь с нашей командой профессиональных тренеров. 
            Каждый из них имеет многолетний опыт и поможет вам достичь ваших целей в фитнесе.
          </p>
        </div>

        <div class="stats-section">
          <div class="stats-container">
            <h2 class="stats-title">Наша команда в цифрах</h2>
            <div class="stats-grid">
              <div class="stat-item">
                <div class="stat-number">${this.trainers.length}</div>
                <div class="stat-label">Профессиональных тренеров</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">50+</div>
                <div class="stat-label">Лет общего опыта</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">1000+</div>
                <div class="stat-label">Довольных клиентов</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">15+</div>
                <div class="stat-label">Видов тренировок</div>
              </div>
            </div>
          </div>
        </div>

        <div class="trainers-grid">
          ${this.trainers.map(t=>r`
            <div class="trainer-card">
              <div class="trainer-image">
                ${this.getTrainerInitials(t.name)}
              </div>
              <div class="trainer-content">
                <h3 class="trainer-name">${t.name}</h3>
                
                <div class="trainer-specializations">
                  ${t.specialization.map(t=>r`
                    <span class="specialization-tag">${t}</span>
                  `)}
                </div>
                
                <p class="trainer-bio">${t.bio}</p>
                
                <div class="trainer-experience">
                  <div class="experience-icon">⭐</div>
                  <span>Опыт работы: ${t.experience}</span>
                </div>
                
                <div class="trainer-actions">
                  <button class="action-btn btn-primary" 
                          @click=${()=>this.openTrainerModal(t)}>
                    👁️ Подробнее
                  </button>
                  <a href="/schedule" class="action-btn btn-outline">
                    📅 Расписание
                  </a>
                </div>
              </div>
            </div>
          `)}
        </div>
      </div>

      ${this.showModal&&this.selectedTrainer?r`
        <div class="trainer-modal" @click=${this.handleModalClick}>
          <div class="modal-content">
            <div class="modal-header">
              <h2 class="modal-title">${this.selectedTrainer.name}</h2>
              <button class="close-btn" @click=${this.closeTrainerModal}>×</button>
            </div>
            <div class="modal-body">
              <div class="trainer-image" style="height: 200px; margin-bottom: var(--spacing-6);">
                ${this.getTrainerInitials(this.selectedTrainer.name)}
              </div>
              
              <div class="trainer-specializations" style="margin-bottom: var(--spacing-4);">
                ${this.selectedTrainer.specialization.map(t=>r`
                  <span class="specialization-tag">${t}</span>
                `)}
              </div>
              
              <div class="trainer-experience" style="margin-bottom: var(--spacing-4);">
                <div class="experience-icon">⭐</div>
                <span>Опыт работы: ${this.selectedTrainer.experience}</span>
              </div>
              
              <p class="trainer-bio" style="margin-bottom: var(--spacing-6);">
                ${this.selectedTrainer.bio}
              </p>
              
              <div style="display: flex; gap: var(--spacing-3);">
                <a href="/schedule" class="action-btn btn-primary" style="flex: 1;">
                  📅 Посмотреть расписание
                </a>
                <button class="action-btn btn-outline" @click=${this.closeTrainerModal}>
                  Закрыть
                </button>
              </div>
            </div>
          </div>
        </div>
      `:""}
    `}}customElements.define("trainers-page",n);class l extends t{static styles=a`
    :host {
      display: block;
    }

    .about-container {
      max-width: var(--container-xl);
      margin: 0 auto;
      padding: var(--spacing-6) var(--spacing-4);
    }

    .hero-section {
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      color: white;
      padding: var(--spacing-16) 0;
      margin: 0 calc(-1 * var(--spacing-4)) var(--spacing-16);
      text-align: center;
      position: relative;
      overflow: hidden;
    }

    .hero-section::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(45deg, rgba(37, 99, 235, 0.1), rgba(16, 185, 129, 0.1));
      opacity: 0.2;
      z-index: 0;
    }

    .hero-content {
      position: relative;
      z-index: 1;
      max-width: var(--container-lg);
      margin: 0 auto;
      padding: 0 var(--spacing-4);
    }

    .hero-title {
      font-size: var(--font-size-4xl);
      font-weight: 700;
      margin-bottom: var(--spacing-6);
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .hero-subtitle {
      font-size: var(--font-size-xl);
      margin-bottom: var(--spacing-8);
      opacity: 0.9;
      line-height: 1.6;
    }

    .content-section {
      margin-bottom: var(--spacing-16);
    }

    .section-title {
      font-size: var(--font-size-2xl);
      font-weight: 700;
      color: var(--gray-800);
      margin-bottom: var(--spacing-6);
      text-align: center;
    }

    .content-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: var(--spacing-8);
      align-items: center;
    }

    .content-text {
      font-size: var(--font-size-base);
      line-height: 1.7;
      color: var(--gray-600);
    }

    .content-text h3 {
      color: var(--gray-800);
      font-size: var(--font-size-lg);
      font-weight: 600;
      margin: var(--spacing-6) 0 var(--spacing-3) 0;
    }

    .content-text ul {
      margin: var(--spacing-4) 0;
      padding-left: var(--spacing-6);
    }

    .content-text li {
      margin-bottom: var(--spacing-2);
    }

    .content-image {
      width: 100%;
      height: 300px;
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      border-radius: var(--radius-xl);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 4rem;
      position: relative;
      overflow: hidden;
    }

    .content-image::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
      transform: translateX(-100%);
      animation: shimmer 3s infinite;
    }

    @keyframes shimmer {
      0% { transform: translateX(-100%); }
      100% { transform: translateX(100%); }
    }

    .features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: var(--spacing-6);
      margin-top: var(--spacing-8);
    }

    .feature-card {
      background: white;
      border-radius: var(--radius-xl);
      padding: var(--spacing-6);
      text-align: center;
      box-shadow: var(--shadow-md);
      transition: all var(--transition-normal);
    }

    .feature-card:hover {
      transform: translateY(-5px);
      box-shadow: var(--shadow-xl);
    }

    .feature-icon {
      width: 80px;
      height: 80px;
      margin: 0 auto var(--spacing-4);
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: var(--font-size-2xl);
    }

    .feature-title {
      font-size: var(--font-size-lg);
      font-weight: 600;
      color: var(--gray-800);
      margin-bottom: var(--spacing-3);
    }

    .feature-description {
      color: var(--gray-600);
      line-height: 1.6;
    }

    .contact-section {
      background: white;
      border-radius: var(--radius-2xl);
      padding: var(--spacing-8);
      box-shadow: var(--shadow-lg);
      margin-top: var(--spacing-16);
    }

    .contact-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: var(--spacing-8);
    }

    .contact-info h3 {
      font-size: var(--font-size-xl);
      font-weight: 700;
      color: var(--gray-800);
      margin-bottom: var(--spacing-4);
    }

    .contact-item {
      display: flex;
      align-items: flex-start;
      gap: var(--spacing-3);
      margin-bottom: var(--spacing-4);
    }

    .contact-icon {
      width: 24px;
      height: 24px;
      background: var(--primary-color);
      border-radius: var(--radius-md);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: var(--font-size-sm);
      flex-shrink: 0;
      margin-top: 2px;
    }

    .contact-details {
      color: var(--gray-600);
      line-height: 1.6;
    }

    .contact-details strong {
      color: var(--gray-800);
      display: block;
      margin-bottom: var(--spacing-1);
    }

    .map-placeholder {
      width: 100%;
      height: 300px;
      background: linear-gradient(135deg, var(--gray-200), var(--gray-300));
      border-radius: var(--radius-lg);
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--gray-500);
      font-size: var(--font-size-lg);
      position: relative;
      overflow: hidden;
    }

    .map-placeholder::before {
      content: '🗺️';
      font-size: 3rem;
      margin-bottom: var(--spacing-2);
    }

    .stats-section {
      background: linear-gradient(135deg, var(--gray-800), var(--gray-700));
      color: white;
      padding: var(--spacing-12) 0;
      margin: var(--spacing-16) calc(-1 * var(--spacing-4));
      border-radius: var(--radius-2xl);
    }

    .stats-container {
      max-width: var(--container-lg);
      margin: 0 auto;
      padding: 0 var(--spacing-4);
      text-align: center;
    }

    .stats-title {
      font-size: var(--font-size-2xl);
      font-weight: 700;
      margin-bottom: var(--spacing-8);
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: var(--spacing-6);
    }

    .stat-item {
      text-align: center;
    }

    .stat-number {
      font-size: var(--font-size-3xl);
      font-weight: 700;
      margin-bottom: var(--spacing-2);
      background: linear-gradient(135deg, var(--primary-light), var(--secondary-color));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .stat-label {
      font-size: var(--font-size-base);
      opacity: 0.9;
    }

    .loading-state {
      text-align: center;
      padding: var(--spacing-12);
    }

    .spinner {
      width: 40px;
      height: 40px;
      border: 4px solid var(--gray-200);
      border-top: 4px solid var(--primary-color);
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto var(--spacing-4);
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    @media (max-width: 768px) {
      .hero-title {
        font-size: var(--font-size-3xl);
      }

      .hero-subtitle {
        font-size: var(--font-size-lg);
      }

      .content-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-6);
      }

      .contact-grid {
        grid-template-columns: 1fr;
      }

      .stats-grid {
        grid-template-columns: repeat(2, 1fr);
      }

      .features-grid {
        grid-template-columns: 1fr;
      }
    }

    @media (max-width: 480px) {
      .stats-grid {
        grid-template-columns: 1fr;
      }
    }
  `;static properties={content:{type:Object},loading:{type:Boolean}};constructor(){super(),this.content=null,this.loading=!0,this.loadContent()}async loadContent(){try{this.loading=!0,this.content=await e.getContent("about")}catch(t){console.error("Ошибка загрузки контента:",t)}finally{this.loading=!1}}render(){return this.loading?r`
        <div class="about-container">
          <div class="loading-state">
            <div class="spinner"></div>
            <p>Загрузка информации о студии...</p>
          </div>
        </div>
      `:r`
      <div class="hero-section">
        <div class="hero-content">
          <h1 class="hero-title">О нашей студии</h1>
          <p class="hero-subtitle">
            FitStudio - это современное пространство для занятий фитнесом, 
            где каждый найдет подходящую программу тренировок
          </p>
        </div>
      </div>

      <div class="about-container">
        <section class="content-section">
          <h2 class="section-title">Наша история</h2>
          <div class="content-grid">
            <div class="content-text">
              <p>
                FitStudio была основана в 2018 году с целью создания современного 
                и комфортного пространства для занятий фитнесом. Мы верим, что 
                здоровый образ жизни должен быть доступен каждому.
              </p>
              <p>
                За годы работы мы помогли тысячам людей достичь своих целей в фитнесе, 
                улучшить здоровье и обрести уверенность в себе. Наша команда 
                профессиональных тренеров постоянно совершенствует свои навыки 
                и изучает новые методики тренировок.
              </p>
            </div>
            <div class="content-image">
              🏢
            </div>
          </div>
        </section>

        <section class="content-section">
          <h2 class="section-title">Наши преимущества</h2>
          <div class="features-grid">
            <div class="feature-card">
              <div class="feature-icon">👨‍🏫</div>
              <h3 class="feature-title">Профессиональные тренеры</h3>
              <p class="feature-description">
                Сертифицированные инструкторы с многолетним опытом 
                и постоянным повышением квалификации
              </p>
            </div>
            <div class="feature-card">
              <div class="feature-icon">🏋️‍♀️</div>
              <h3 class="feature-title">Современное оборудование</h3>
              <p class="feature-description">
                Новейшее фитнес-оборудование от ведущих мировых 
                производителей для эффективных тренировок
              </p>
            </div>
            <div class="feature-card">
              <div class="feature-icon">📅</div>
              <h3 class="feature-title">Удобное расписание</h3>
              <p class="feature-description">
                Гибкое расписание занятий с утра до вечера, 
                которое подойдет для любого образа жизни
              </p>
            </div>
            <div class="feature-card">
              <div class="feature-icon">🎯</div>
              <h3 class="feature-title">Индивидуальный подход</h3>
              <p class="feature-description">
                Персональные программы тренировок с учетом 
                ваших особенностей, целей и уровня подготовки
              </p>
            </div>
            <div class="feature-card">
              <div class="feature-icon">🏆</div>
              <h3 class="feature-title">Результативность</h3>
              <p class="feature-description">
                Проверенные методики и постоянный контроль 
                прогресса для достижения ваших целей
              </p>
            </div>
            <div class="feature-card">
              <div class="feature-icon">💚</div>
              <h3 class="feature-title">Дружелюбная атмосфера</h3>
              <p class="feature-description">
                Комфортная и поддерживающая среда, где каждый 
                чувствует себя как дома
              </p>
            </div>
          </div>
        </section>

        <div class="stats-section">
          <div class="stats-container">
            <h2 class="stats-title">FitStudio в цифрах</h2>
            <div class="stats-grid">
              <div class="stat-item">
                <div class="stat-number">2000+</div>
                <div class="stat-label">Довольных клиентов</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">15</div>
                <div class="stat-label">Видов тренировок</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">8</div>
                <div class="stat-label">Профессиональных тренеров</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">500м²</div>
                <div class="stat-label">Площадь студии</div>
              </div>
            </div>
          </div>
        </div>

        <section class="content-section">
          <h2 class="section-title">Наши программы</h2>
          <div class="content-grid">
            <div class="content-image">
              🧘‍♀️
            </div>
            <div class="content-text">
              <h3>Йога и пилатес</h3>
              <p>
                Развитие гибкости, силы и баланса через древние практики йоги 
                и современные методики пилатеса. Подходит для всех уровней подготовки.
              </p>
              
              <h3>Функциональный тренинг</h3>
              <p>
                Интенсивные тренировки с использованием собственного веса и 
                функциональных движений для развития силы, выносливости и координации.
              </p>
              
              <h3>Танцевальные программы</h3>
              <p>
                Энергичные занятия под музыку, которые помогают сжигать калории, 
                улучшать координацию и получать удовольствие от движения.
              </p>
            </div>
          </div>
        </section>

        <div class="contact-section">
          <h2 class="section-title">Контакты и расположение</h2>
          <div class="contact-grid">
            <div class="contact-info">
              <h3>Как нас найти</h3>
              
              <div class="contact-item">
                <div class="contact-icon">📍</div>
                <div class="contact-details">
                  <strong>Адрес</strong>
                  г. Москва, ул. Спортивная, д. 15<br>
                  (5 минут от метро "Спортивная")
                </div>
              </div>
              
              <div class="contact-item">
                <div class="contact-icon">📞</div>
                <div class="contact-details">
                  <strong>Телефон</strong>
                  +7 (495) 123-45-67<br>
                  Ежедневно с 07:00 до 23:00
                </div>
              </div>
              
              <div class="contact-item">
                <div class="contact-icon">✉️</div>
                <div class="contact-details">
                  <strong>Email</strong>
                  <EMAIL><br>
                  Ответим в течение 24 часов
                </div>
              </div>
              
              <div class="contact-item">
                <div class="contact-icon">🕒</div>
                <div class="contact-details">
                  <strong>Режим работы</strong>
                  Понедельник - Пятница: 07:00 - 23:00<br>
                  Суббота - Воскресенье: 09:00 - 21:00
                </div>
              </div>
            </div>
            
            <div class="map-placeholder">
              Интерактивная карта
            </div>
          </div>
        </div>
      </div>
    `}}customElements.define("about-page",l);const c={hash:async(t,a)=>btoa(t+a),compare:async(t,a)=>btoa(t+"10")===a};const d=new class{constructor(){this.currentUser=null,this.loadUserFromStorage()}loadUserFromStorage(){try{const t=localStorage.getItem("currentUser");t&&(this.currentUser=JSON.parse(t))}catch(t){console.error("Ошибка загрузки пользователя из localStorage:",t),localStorage.removeItem("currentUser")}}saveUserToStorage(t){try{localStorage.setItem("currentUser",JSON.stringify(t)),this.currentUser=t}catch(t){console.error("Ошибка сохранения пользователя в localStorage:",t)}}removeUserFromStorage(){localStorage.removeItem("currentUser"),this.currentUser=null}async register(t){try{const a=this.validateRegistrationData(t);if(!a.isValid)throw new Error(a.errors.join(", "));if(await e.getUserByEmail(t.email))throw new Error("Пользователь с таким email уже существует");const r=10,i=await c.hash(t.password,r),s={email:t.email,name:t.name,phone:t.phone,password_hash:i},o=(await e.createUser(s),await e.getUserByEmail(t.email)),n=this.removePasswordFromUser(o);return this.saveUserToStorage(n),{success:!0,user:n,message:"Регистрация прошла успешно"}}catch(t){return console.error("Ошибка регистрации:",t),{success:!1,error:t.message||"Ошибка при регистрации"}}}async login(t,a){try{if(!t||!a)throw new Error("Email и пароль обязательны для заполнения");if(!this.isValidEmail(t))throw new Error("Некорректный формат email");const r=await e.getUserByEmail(t);if(!r)throw new Error("Пользователь с таким email не найден");if(!await c.compare(a,r.password_hash))throw new Error("Неверный пароль");const i=this.removePasswordFromUser(r);return this.saveUserToStorage(i),{success:!0,user:i,message:"Вход выполнен успешно"}}catch(t){return console.error("Ошибка входа:",t),{success:!1,error:t.message||"Ошибка при входе в систему"}}}logout(){return this.removeUserFromStorage(),{success:!0,message:"Выход выполнен успешно"}}getCurrentUser(){return this.currentUser}isAuthenticated(){return null!==this.currentUser}isAdmin(){return this.currentUser&&"admin"===this.currentUser.role}validateRegistrationData(t){const a=[];return(!t.name||t.name.trim().length<2)&&a.push("Имя должно содержать минимум 2 символа"),t.email&&this.isValidEmail(t.email)||a.push("Некорректный формат email"),t.phone&&this.isValidPhone(t.phone)||a.push("Некорректный формат телефона"),(!t.password||t.password.length<6)&&a.push("Пароль должен содержать минимум 6 символов"),t.password!==t.confirmPassword&&a.push("Пароли не совпадают"),{isValid:0===a.length,errors:a}}isValidEmail(t){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(t)}isValidPhone(t){return/^(\+7|8)?[\s\-]?\(?[489][0-9]{2}\)?[\s\-]?[0-9]{3}[\s\-]?[0-9]{2}[\s\-]?[0-9]{2}$/.test(t.replace(/\s/g,""))}removePasswordFromUser(t){const{password_hash:a,...r}=t;return r}async updateProfile(t){try{if(!this.isAuthenticated())throw new Error("Необходима авторизация");const a=[];if((!t.name||t.name.trim().length<2)&&a.push("Имя должно содержать минимум 2 символа"),t.phone&&this.isValidPhone(t.phone)||a.push("Некорректный формат телефона"),a.length>0)throw new Error(a.join(", "));const r={...this.currentUser,name:t.name,phone:t.phone};return this.saveUserToStorage(r),{success:!0,user:r,message:"Профиль обновлен успешно"}}catch(t){return console.error("Ошибка обновления профиля:",t),{success:!1,error:t.message||"Ошибка при обновлении профиля"}}}async changePassword(t,a,r){try{if(!this.isAuthenticated())throw new Error("Необходима авторизация");if(!t||!a||!r)throw new Error("Все поля обязательны для заполнения");if(a.length<6)throw new Error("Новый пароль должен содержать минимум 6 символов");if(a!==r)throw new Error("Новые пароли не совпадают");const i=await e.getUserByEmail(this.currentUser.email);if(!await c.compare(t,i.password_hash))throw new Error("Неверный текущий пароль");const s=10;await c.hash(a,s);return{success:!0,message:"Пароль изменен успешно"}}catch(t){return console.error("Ошибка смены пароля:",t),{success:!1,error:t.message||"Ошибка при смене пароля"}}}};class g extends t{static styles=a`
    :host {
      display: block;
    }

    .login-container {
      max-width: 400px;
      margin: var(--spacing-12) auto;
      padding: 0 var(--spacing-4);
    }

    .login-card {
      background: white;
      border-radius: var(--radius-xl);
      box-shadow: var(--shadow-xl);
      padding: var(--spacing-8);
    }

    .login-header {
      text-align: center;
      margin-bottom: var(--spacing-8);
    }

    .login-title {
      font-size: var(--font-size-3xl);
      font-weight: 700;
      color: var(--gray-800);
      margin-bottom: var(--spacing-2);
    }

    .login-subtitle {
      color: var(--gray-600);
      font-size: var(--font-size-base);
    }

    .form-group {
      margin-bottom: var(--spacing-6);
    }

    .form-label {
      display: block;
      font-weight: 500;
      color: var(--gray-700);
      margin-bottom: var(--spacing-2);
    }

    .form-input {
      width: 100%;
      padding: var(--spacing-3);
      border: 2px solid var(--gray-200);
      border-radius: var(--radius-lg);
      font-size: var(--font-size-base);
      transition: border-color var(--transition-fast);
    }

    .form-input:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
    }

    .form-input.error {
      border-color: var(--error-color);
    }

    .form-error {
      color: var(--error-color);
      font-size: var(--font-size-sm);
      margin-top: var(--spacing-1);
    }

    .login-button {
      width: 100%;
      padding: var(--spacing-4);
      background-color: var(--primary-color);
      color: white;
      border: none;
      border-radius: var(--radius-lg);
      font-size: var(--font-size-base);
      font-weight: 600;
      cursor: pointer;
      transition: all var(--transition-fast);
      margin-bottom: var(--spacing-6);
    }

    .login-button:hover:not(:disabled) {
      background-color: var(--primary-dark);
      transform: translateY(-1px);
      box-shadow: var(--shadow-md);
    }

    .login-button:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      transform: none;
    }

    .login-footer {
      text-align: center;
      padding-top: var(--spacing-6);
      border-top: 1px solid var(--gray-200);
    }

    .login-footer p {
      color: var(--gray-600);
      margin-bottom: var(--spacing-2);
    }

    .login-footer a {
      color: var(--primary-color);
      text-decoration: none;
      font-weight: 500;
    }

    .login-footer a:hover {
      text-decoration: underline;
    }

    .alert {
      padding: var(--spacing-3);
      border-radius: var(--radius-md);
      margin-bottom: var(--spacing-6);
      font-size: var(--font-size-sm);
    }

    .alert-error {
      background-color: rgb(254 242 242);
      border: 1px solid rgb(252 165 165);
      color: var(--error-color);
    }

    .alert-success {
      background-color: rgb(240 253 244);
      border: 1px solid rgb(167 243 208);
      color: var(--success-color);
    }

    .loading-spinner {
      display: inline-block;
      width: 16px;
      height: 16px;
      border: 2px solid transparent;
      border-top: 2px solid currentColor;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-right: var(--spacing-2);
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .demo-credentials {
      background-color: var(--gray-50);
      border: 1px solid var(--gray-200);
      border-radius: var(--radius-md);
      padding: var(--spacing-4);
      margin-bottom: var(--spacing-6);
    }

    .demo-credentials h4 {
      font-size: var(--font-size-sm);
      font-weight: 600;
      color: var(--gray-700);
      margin-bottom: var(--spacing-2);
    }

    .demo-credentials p {
      font-size: var(--font-size-xs);
      color: var(--gray-600);
      margin: var(--spacing-1) 0;
    }

    @media (max-width: 480px) {
      .login-container {
        margin: var(--spacing-6) auto;
      }

      .login-card {
        padding: var(--spacing-6);
      }
    }
  `;static properties={loading:{type:Boolean},error:{type:String},success:{type:String},formData:{type:Object}};constructor(){super(),this.loading=!1,this.error="",this.success="",this.formData={email:"",password:""}}handleInputChange(t){const{name:a,value:r}=t.target;this.formData={...this.formData,[a]:r},this.error&&(this.error="")}async handleSubmit(t){if(t.preventDefault(),!this.loading){this.loading=!0,this.error="",this.success="";try{const t=await d.login(this.formData.email,this.formData.password);t.success?(this.success=t.message,this.dispatchEvent(new CustomEvent("user-login",{detail:{user:t.user}})),setTimeout(()=>{window.history.pushState({},"","/"),window.dispatchEvent(new PopStateEvent("popstate"))},1e3)):this.error=t.error}catch(t){this.error="Произошла ошибка при входе в систему",console.error("Login error:",t)}finally{this.loading=!1}}}fillDemoCredentials(t){this.formData="admin"===t?{email:"<EMAIL>",password:"password"}:{email:"<EMAIL>",password:"password"},this.requestUpdate()}render(){return r`
      <div class="login-container">
        <div class="login-card">
          <div class="login-header">
            <h1 class="login-title">Вход в систему</h1>
            <p class="login-subtitle">Войдите в свой аккаунт, чтобы записаться на тренировки</p>
          </div>

          <div class="demo-credentials">
            <h4>Демо-аккаунты для тестирования:</h4>
            <p><strong>Администратор:</strong> <EMAIL> / password 
              <button class="btn btn-ghost btn-sm" @click=${()=>this.fillDemoCredentials("admin")}>Заполнить</button>
            </p>
          </div>

          ${this.error?r`
            <div class="alert alert-error">
              ${this.error}
            </div>
          `:""}

          ${this.success?r`
            <div class="alert alert-success">
              ${this.success}
            </div>
          `:""}

          <form @submit=${this.handleSubmit}>
            <div class="form-group">
              <label class="form-label" for="email">Email</label>
              <input
                type="email"
                id="email"
                name="email"
                class="form-input"
                .value=${this.formData.email}
                @input=${this.handleInputChange}
                required
                autocomplete="email"
                placeholder="<EMAIL>"
              />
            </div>

            <div class="form-group">
              <label class="form-label" for="password">Пароль</label>
              <input
                type="password"
                id="password"
                name="password"
                class="form-input"
                .value=${this.formData.password}
                @input=${this.handleInputChange}
                required
                autocomplete="current-password"
                placeholder="Введите пароль"
              />
            </div>

            <button type="submit" class="login-button" ?disabled=${this.loading}>
              ${this.loading?r`
                <span class="loading-spinner"></span>
                Вход...
              `:"Войти"}
            </button>
          </form>

          <div class="login-footer">
            <p>Нет аккаунта? <a href="/register">Зарегистрироваться</a></p>
          </div>
        </div>
      </div>
    `}}customElements.define("login-page",g);class p extends t{static styles=a`
    :host {
      display: block;
    }

    .register-container {
      max-width: 500px;
      margin: var(--spacing-12) auto;
      padding: 0 var(--spacing-4);
    }

    .register-card {
      background: white;
      border-radius: var(--radius-xl);
      box-shadow: var(--shadow-xl);
      padding: var(--spacing-8);
    }

    .register-header {
      text-align: center;
      margin-bottom: var(--spacing-8);
    }

    .register-title {
      font-size: var(--font-size-3xl);
      font-weight: 700;
      color: var(--gray-800);
      margin-bottom: var(--spacing-2);
    }

    .register-subtitle {
      color: var(--gray-600);
      font-size: var(--font-size-base);
    }

    .form-row {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: var(--spacing-4);
    }

    .form-group {
      margin-bottom: var(--spacing-6);
    }

    .form-label {
      display: block;
      font-weight: 500;
      color: var(--gray-700);
      margin-bottom: var(--spacing-2);
    }

    .form-input {
      width: 100%;
      padding: var(--spacing-3);
      border: 2px solid var(--gray-200);
      border-radius: var(--radius-lg);
      font-size: var(--font-size-base);
      transition: border-color var(--transition-fast);
    }

    .form-input:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
    }

    .form-input.error {
      border-color: var(--error-color);
    }

    .form-error {
      color: var(--error-color);
      font-size: var(--font-size-sm);
      margin-top: var(--spacing-1);
    }

    .password-strength {
      margin-top: var(--spacing-2);
    }

    .strength-bar {
      height: 4px;
      background-color: var(--gray-200);
      border-radius: 2px;
      overflow: hidden;
      margin-bottom: var(--spacing-1);
    }

    .strength-fill {
      height: 100%;
      transition: width var(--transition-fast), background-color var(--transition-fast);
    }

    .strength-weak { background-color: var(--error-color); }
    .strength-medium { background-color: var(--warning-color); }
    .strength-strong { background-color: var(--success-color); }

    .strength-text {
      font-size: var(--font-size-xs);
      color: var(--gray-600);
    }

    .register-button {
      width: 100%;
      padding: var(--spacing-4);
      background-color: var(--primary-color);
      color: white;
      border: none;
      border-radius: var(--radius-lg);
      font-size: var(--font-size-base);
      font-weight: 600;
      cursor: pointer;
      transition: all var(--transition-fast);
      margin-bottom: var(--spacing-6);
    }

    .register-button:hover:not(:disabled) {
      background-color: var(--primary-dark);
      transform: translateY(-1px);
      box-shadow: var(--shadow-md);
    }

    .register-button:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      transform: none;
    }

    .register-footer {
      text-align: center;
      padding-top: var(--spacing-6);
      border-top: 1px solid var(--gray-200);
    }

    .register-footer p {
      color: var(--gray-600);
      margin-bottom: var(--spacing-2);
    }

    .register-footer a {
      color: var(--primary-color);
      text-decoration: none;
      font-weight: 500;
    }

    .register-footer a:hover {
      text-decoration: underline;
    }

    .alert {
      padding: var(--spacing-3);
      border-radius: var(--radius-md);
      margin-bottom: var(--spacing-6);
      font-size: var(--font-size-sm);
    }

    .alert-error {
      background-color: rgb(254 242 242);
      border: 1px solid rgb(252 165 165);
      color: var(--error-color);
    }

    .alert-success {
      background-color: rgb(240 253 244);
      border: 1px solid rgb(167 243 208);
      color: var(--success-color);
    }

    .loading-spinner {
      display: inline-block;
      width: 16px;
      height: 16px;
      border: 2px solid transparent;
      border-top: 2px solid currentColor;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-right: var(--spacing-2);
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .terms {
      font-size: var(--font-size-sm);
      color: var(--gray-600);
      margin-bottom: var(--spacing-6);
      line-height: 1.5;
    }

    .terms a {
      color: var(--primary-color);
      text-decoration: none;
    }

    .terms a:hover {
      text-decoration: underline;
    }

    @media (max-width: 640px) {
      .register-container {
        margin: var(--spacing-6) auto;
      }

      .register-card {
        padding: var(--spacing-6);
      }

      .form-row {
        grid-template-columns: 1fr;
      }
    }
  `;static properties={loading:{type:Boolean},error:{type:String},success:{type:String},formData:{type:Object},fieldErrors:{type:Object}};constructor(){super(),this.loading=!1,this.error="",this.success="",this.formData={name:"",email:"",phone:"",password:"",confirmPassword:""},this.fieldErrors={}}handleInputChange(t){const{name:a,value:r}=t.target;this.formData={...this.formData,[a]:r},this.error&&(this.error=""),this.fieldErrors[a]&&(this.fieldErrors={...this.fieldErrors,[a]:""}),this.requestUpdate()}validateField(t,a){switch(t){case"name":return a.trim().length>=2?"":"Имя должно содержать минимум 2 символа";case"email":return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(a)?"":"Некорректный формат email";case"phone":return/^(\+7|8)?[\s\-]?\(?[489][0-9]{2}\)?[\s\-]?[0-9]{3}[\s\-]?[0-9]{2}[\s\-]?[0-9]{2}$/.test(a.replace(/\s/g,""))?"":"Некорректный формат телефона";case"password":return a.length>=6?"":"Пароль должен содержать минимум 6 символов";case"confirmPassword":return a===this.formData.password?"":"Пароли не совпадают";default:return""}}getPasswordStrength(t){if(!t)return{strength:0,text:""};let a=0;return t.length>=8&&(a+=1),t.length>=12&&(a+=1),/\d/.test(t)&&(a+=1),/[a-z]/.test(t)&&(a+=1),/[A-Z]/.test(t)&&(a+=1),/[^A-Za-z0-9]/.test(t)&&(a+=1),a<=2?{strength:1,text:"Слабый пароль"}:a<=4?{strength:2,text:"Средний пароль"}:{strength:3,text:"Сильный пароль"}}async handleSubmit(t){if(t.preventDefault(),this.loading)return;const a={};if(Object.keys(this.formData).forEach(t=>{const r=this.validateField(t,this.formData[t]);r&&(a[t]=r)}),Object.keys(a).length>0)return this.fieldErrors=a,void(this.error="Пожалуйста, исправьте ошибки в форме");this.loading=!0,this.error="",this.success="",this.fieldErrors={};try{const t=await d.register(this.formData);t.success?(this.success=t.message,this.dispatchEvent(new CustomEvent("user-login",{detail:{user:t.user}})),setTimeout(()=>{window.history.pushState({},"","/"),window.dispatchEvent(new PopStateEvent("popstate"))},1500)):this.error=t.error}catch(t){this.error="Произошла ошибка при регистрации",console.error("Registration error:",t)}finally{this.loading=!1}}render(){const t=this.getPasswordStrength(this.formData.password);return r`
      <div class="register-container">
        <div class="register-card">
          <div class="register-header">
            <h1 class="register-title">Регистрация</h1>
            <p class="register-subtitle">Создайте аккаунт, чтобы записываться на тренировки</p>
          </div>

          ${this.error?r`
            <div class="alert alert-error">
              ${this.error}
            </div>
          `:""}

          ${this.success?r`
            <div class="alert alert-success">
              ${this.success}
            </div>
          `:""}

          <form @submit=${this.handleSubmit}>
            <div class="form-group">
              <label class="form-label" for="name">Полное имя</label>
              <input
                type="text"
                id="name"
                name="name"
                class="form-input ${this.fieldErrors.name?"error":""}"
                .value=${this.formData.name}
                @input=${this.handleInputChange}
                required
                autocomplete="name"
                placeholder="Иван Иванов"
              />
              ${this.fieldErrors.name?r`
                <div class="form-error">${this.fieldErrors.name}</div>
              `:""}
            </div>

            <div class="form-row">
              <div class="form-group">
                <label class="form-label" for="email">Email</label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  class="form-input ${this.fieldErrors.email?"error":""}"
                  .value=${this.formData.email}
                  @input=${this.handleInputChange}
                  required
                  autocomplete="email"
                  placeholder="<EMAIL>"
                />
                ${this.fieldErrors.email?r`
                  <div class="form-error">${this.fieldErrors.email}</div>
                `:""}
              </div>

              <div class="form-group">
                <label class="form-label" for="phone">Телефон</label>
                <input
                  type="tel"
                  id="phone"
                  name="phone"
                  class="form-input ${this.fieldErrors.phone?"error":""}"
                  .value=${this.formData.phone}
                  @input=${this.handleInputChange}
                  required
                  autocomplete="tel"
                  placeholder="+7 (999) 123-45-67"
                />
                ${this.fieldErrors.phone?r`
                  <div class="form-error">${this.fieldErrors.phone}</div>
                `:""}
              </div>
            </div>

            <div class="form-group">
              <label class="form-label" for="password">Пароль</label>
              <input
                type="password"
                id="password"
                name="password"
                class="form-input ${this.fieldErrors.password?"error":""}"
                .value=${this.formData.password}
                @input=${this.handleInputChange}
                required
                autocomplete="new-password"
                placeholder="Минимум 6 символов"
              />
              ${this.fieldErrors.password?r`
                <div class="form-error">${this.fieldErrors.password}</div>
              `:""}
              
              ${this.formData.password?r`
                <div class="password-strength">
                  <div class="strength-bar">
                    <div class="strength-fill strength-${1===t.strength?"weak":2===t.strength?"medium":"strong"}" 
                         style="width: ${t.strength/3*100}%"></div>
                  </div>
                  <div class="strength-text">${t.text}</div>
                </div>
              `:""}
            </div>

            <div class="form-group">
              <label class="form-label" for="confirmPassword">Подтверждение пароля</label>
              <input
                type="password"
                id="confirmPassword"
                name="confirmPassword"
                class="form-input ${this.fieldErrors.confirmPassword?"error":""}"
                .value=${this.formData.confirmPassword}
                @input=${this.handleInputChange}
                required
                autocomplete="new-password"
                placeholder="Повторите пароль"
              />
              ${this.fieldErrors.confirmPassword?r`
                <div class="form-error">${this.fieldErrors.confirmPassword}</div>
              `:""}
            </div>

            <div class="terms">
              Регистрируясь, вы соглашаетесь с <a href="#">Условиями использования</a> 
              и <a href="#">Политикой конфиденциальности</a>
            </div>

            <button type="submit" class="register-button" ?disabled=${this.loading}>
              ${this.loading?r`
                <span class="loading-spinner"></span>
                Регистрация...
              `:"Зарегистрироваться"}
            </button>
          </form>

          <div class="register-footer">
            <p>Уже есть аккаунт? <a href="/login">Войти</a></p>
          </div>
        </div>
      </div>
    `}}customElements.define("register-page",p);class v extends t{static styles=a`
    :host {
      display: block;
    }

    .admin-container {
      max-width: var(--container-xl);
      margin: 0 auto;
      padding: var(--spacing-6) var(--spacing-4);
    }

    .admin-header {
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      color: white;
      padding: var(--spacing-8);
      border-radius: var(--radius-xl);
      margin-bottom: var(--spacing-8);
      text-align: center;
    }

    .admin-title {
      font-size: var(--font-size-3xl);
      font-weight: 700;
      margin-bottom: var(--spacing-2);
    }

    .admin-subtitle {
      font-size: var(--font-size-lg);
      opacity: 0.9;
    }

    .admin-tabs {
      display: flex;
      background: white;
      border-radius: var(--radius-xl);
      box-shadow: var(--shadow-sm);
      margin-bottom: var(--spacing-8);
      overflow: hidden;
    }

    .tab-button {
      flex: 1;
      padding: var(--spacing-4) var(--spacing-6);
      background: none;
      border: none;
      font-size: var(--font-size-base);
      font-weight: 500;
      color: var(--gray-600);
      cursor: pointer;
      transition: all var(--transition-fast);
      position: relative;
    }

    .tab-button.active {
      color: var(--primary-color);
      background-color: rgb(37 99 235 / 0.05);
    }

    .tab-button.active::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 3px;
      background: var(--primary-color);
    }

    .tab-button:hover:not(.active) {
      background-color: var(--gray-50);
    }

    .tab-content {
      background: white;
      border-radius: var(--radius-xl);
      box-shadow: var(--shadow-sm);
      padding: var(--spacing-8);
    }

    .section-title {
      font-size: var(--font-size-xl);
      font-weight: 700;
      color: var(--gray-800);
      margin-bottom: var(--spacing-6);
      display: flex;
      align-items: center;
      gap: var(--spacing-3);
    }

    .form-group {
      margin-bottom: var(--spacing-6);
    }

    .form-label {
      display: block;
      font-weight: 500;
      color: var(--gray-700);
      margin-bottom: var(--spacing-2);
    }

    .form-input,
    .form-textarea {
      width: 100%;
      padding: var(--spacing-3);
      border: 2px solid var(--gray-200);
      border-radius: var(--radius-lg);
      font-size: var(--font-size-base);
      transition: border-color var(--transition-fast);
      font-family: inherit;
    }

    .form-textarea {
      min-height: 120px;
      resize: vertical;
    }

    .form-input:focus,
    .form-textarea:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
    }

    .save-button {
      background-color: var(--primary-color);
      color: white;
      border: none;
      border-radius: var(--radius-lg);
      padding: var(--spacing-3) var(--spacing-6);
      font-size: var(--font-size-base);
      font-weight: 600;
      cursor: pointer;
      transition: all var(--transition-fast);
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
    }

    .save-button:hover:not(:disabled) {
      background-color: var(--primary-dark);
      transform: translateY(-1px);
      box-shadow: var(--shadow-md);
    }

    .save-button:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      transform: none;
    }

    .slides-grid {
      display: grid;
      gap: var(--spacing-6);
    }

    .slide-card {
      border: 2px solid var(--gray-200);
      border-radius: var(--radius-lg);
      padding: var(--spacing-6);
      transition: border-color var(--transition-fast);
    }

    .slide-card:hover {
      border-color: var(--primary-color);
    }

    .slide-header {
      display: flex;
      justify-content: between;
      align-items: center;
      margin-bottom: var(--spacing-4);
    }

    .slide-number {
      background: var(--primary-color);
      color: white;
      width: 32px;
      height: 32px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      font-size: var(--font-size-sm);
    }

    .slide-actions {
      display: flex;
      gap: var(--spacing-2);
    }

    .action-button {
      padding: var(--spacing-2) var(--spacing-3);
      border: none;
      border-radius: var(--radius-md);
      font-size: var(--font-size-sm);
      font-weight: 500;
      cursor: pointer;
      transition: all var(--transition-fast);
    }

    .btn-edit {
      background-color: var(--warning-color);
      color: white;
    }

    .btn-edit:hover {
      background-color: #d97706;
    }

    .btn-delete {
      background-color: var(--error-color);
      color: white;
    }

    .btn-delete:hover {
      background-color: #dc2626;
    }

    .slide-form {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: var(--spacing-4);
    }

    .slide-preview {
      grid-column: 1 / -1;
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      color: white;
      padding: var(--spacing-6);
      border-radius: var(--radius-lg);
      text-align: center;
      margin-bottom: var(--spacing-4);
    }

    .preview-title {
      font-size: var(--font-size-xl);
      font-weight: 700;
      margin-bottom: var(--spacing-2);
    }

    .preview-subtitle {
      font-size: var(--font-size-base);
      opacity: 0.9;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: var(--spacing-6);
      margin-bottom: var(--spacing-8);
    }

    .stat-card {
      background: white;
      border-radius: var(--radius-lg);
      padding: var(--spacing-6);
      text-align: center;
      box-shadow: var(--shadow-sm);
      border: 2px solid var(--gray-100);
      transition: all var(--transition-fast);
    }

    .stat-card:hover {
      border-color: var(--primary-color);
      transform: translateY(-2px);
    }

    .stat-icon {
      width: 60px;
      height: 60px;
      margin: 0 auto var(--spacing-3);
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: var(--font-size-xl);
    }

    .stat-number {
      font-size: var(--font-size-2xl);
      font-weight: 700;
      color: var(--gray-800);
      margin-bottom: var(--spacing-1);
    }

    .stat-label {
      color: var(--gray-600);
      font-size: var(--font-size-sm);
    }

    .notification {
      position: fixed;
      top: var(--spacing-4);
      right: var(--spacing-4);
      padding: var(--spacing-4) var(--spacing-6);
      border-radius: var(--radius-lg);
      color: white;
      font-weight: 500;
      z-index: 1000;
      animation: slideIn 0.3s ease-out;
    }

    .notification.success {
      background-color: var(--success-color);
    }

    .notification.error {
      background-color: var(--error-color);
    }

    @keyframes slideIn {
      from {
        transform: translateX(100%);
        opacity: 0;
      }
      to {
        transform: translateX(0);
        opacity: 1;
      }
    }

    .loading-spinner {
      display: inline-block;
      width: 16px;
      height: 16px;
      border: 2px solid transparent;
      border-top: 2px solid currentColor;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    @media (max-width: 768px) {
      .admin-tabs {
        flex-direction: column;
      }

      .slide-form {
        grid-template-columns: 1fr;
      }

      .stats-grid {
        grid-template-columns: repeat(2, 1fr);
      }
    }

    @media (max-width: 480px) {
      .stats-grid {
        grid-template-columns: 1fr;
      }
    }
  `;static properties={currentUser:{type:Object},activeTab:{type:String},homeContent:{type:Object},slides:{type:Array},loading:{type:Boolean},stats:{type:Object}};constructor(){super(),this.currentUser=null,this.activeTab="dashboard",this.homeContent={},this.slides=[],this.loading=!1,this.stats={totalUsers:0,totalBookings:0,totalTrainers:0,totalWorkouts:0},this.loadData()}async loadData(){try{this.loading=!0;const t=await e.getContent("home");t&&(this.homeContent={title:t.title||"",subtitle:t.subtitle||"",description:t.description||""}),this.slides=await e.getSlides(),this.stats={totalUsers:156,totalBookings:342,totalTrainers:8,totalWorkouts:15}}catch(t){console.error("Ошибка загрузки данных:",t),this.showNotification("Ошибка загрузки данных","error")}finally{this.loading=!1}}switchTab(t){this.activeTab=t}async saveHomeContent(){try{this.loading=!0,await e.updateContent("home",this.homeContent),this.showNotification("Контент главной страницы сохранен","success")}catch(t){console.error("Ошибка сохранения контента:",t),this.showNotification("Ошибка сохранения контента","error")}finally{this.loading=!1}}async saveSlide(t,a){try{this.loading=!0,await e.updateSlide(t,a),await this.loadData(),this.showNotification("Слайд сохранен","success")}catch(t){console.error("Ошибка сохранения слайда:",t),this.showNotification("Ошибка сохранения слайда","error")}finally{this.loading=!1}}handleContentInput(t,a){this.homeContent={...this.homeContent,[t]:a}}handleSlideInput(t,a,r){this.slides=this.slides.map(e=>e._id===t?{...e,[a]:r}:e)}showNotification(t,a="info"){const r=document.createElement("div");r.className=`notification ${a}`,r.textContent=t,document.body.appendChild(r),setTimeout(()=>{r.remove()},5e3)}renderDashboard(){return r`
      <div class="section-title">
        📊 Панель управления
      </div>
      
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon">👥</div>
          <div class="stat-number">${this.stats.totalUsers}</div>
          <div class="stat-label">Пользователей</div>
        </div>
        <div class="stat-card">
          <div class="stat-icon">📅</div>
          <div class="stat-number">${this.stats.totalBookings}</div>
          <div class="stat-label">Записей на тренировки</div>
        </div>
        <div class="stat-card">
          <div class="stat-icon">👨‍🏫</div>
          <div class="stat-number">${this.stats.totalTrainers}</div>
          <div class="stat-label">Тренеров</div>
        </div>
        <div class="stat-card">
          <div class="stat-icon">🏋️‍♀️</div>
          <div class="stat-number">${this.stats.totalWorkouts}</div>
          <div class="stat-label">Видов тренировок</div>
        </div>
      </div>
      
      <div class="section-title">
        📈 Быстрые действия
      </div>
      
      <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: var(--spacing-4);">
        <button class="save-button" @click=${()=>this.switchTab("content")}>
          ✏️ Редактировать главную страницу
        </button>
        <button class="save-button" @click=${()=>this.switchTab("slider")}>
          🖼️ Управление слайдером
        </button>
        <button class="save-button" @click=${()=>window.open("/schedule","_blank")}>
          📅 Посмотреть расписание
        </button>
        <button class="save-button" @click=${()=>window.open("/trainers","_blank")}>
          👨‍🏫 Управление тренерами
        </button>
      </div>
    `}renderContentEditor(){return r`
      <div class="section-title">
        ✏️ Редактирование главной страницы
      </div>
      
      <form @submit=${t=>{t.preventDefault(),this.saveHomeContent()}}>
        <div class="form-group">
          <label class="form-label">Заголовок</label>
          <input
            type="text"
            class="form-input"
            .value=${this.homeContent.title||""}
            @input=${t=>this.handleContentInput("title",t.target.value)}
            placeholder="Добро пожаловать в FitStudio"
          />
        </div>
        
        <div class="form-group">
          <label class="form-label">Подзаголовок</label>
          <input
            type="text"
            class="form-input"
            .value=${this.homeContent.subtitle||""}
            @input=${t=>this.handleContentInput("subtitle",t.target.value)}
            placeholder="Ваш путь к здоровью и красоте"
          />
        </div>
        
        <div class="form-group">
          <label class="form-label">Описание</label>
          <textarea
            class="form-textarea"
            .value=${this.homeContent.description||""}
            @input=${t=>this.handleContentInput("description",t.target.value)}
            placeholder="Современная фитнес-студия с профессиональными тренерами..."
          ></textarea>
        </div>
        
        <button type="submit" class="save-button" ?disabled=${this.loading}>
          ${this.loading?r`<span class="loading-spinner"></span>`:"💾"}
          Сохранить изменения
        </button>
      </form>
    `}renderSliderEditor(){return r`
      <div class="section-title">
        🖼️ Управление слайдером
      </div>
      
      <div class="slides-grid">
        ${this.slides.map((t,a)=>r`
          <div class="slide-card">
            <div class="slide-header">
              <div class="slide-number">${a+1}</div>
              <div class="slide-actions">
                <button class="action-button btn-edit" 
                        @click=${()=>this.saveSlide(t._id,t)}>
                  💾 Сохранить
                </button>
              </div>
            </div>
            
            <div class="slide-preview">
              <div class="preview-title">${t.title||"Заголовок слайда"}</div>
              <div class="preview-subtitle">${t.subtitle||"Подзаголовок слайда"}</div>
            </div>
            
            <div class="slide-form">
              <div class="form-group">
                <label class="form-label">Заголовок</label>
                <input
                  type="text"
                  class="form-input"
                  .value=${t.title||""}
                  @input=${a=>this.handleSlideInput(t._id,"title",a.target.value)}
                  placeholder="Заголовок слайда"
                />
              </div>
              
              <div class="form-group">
                <label class="form-label">Подзаголовок</label>
                <input
                  type="text"
                  class="form-input"
                  .value=${t.subtitle||""}
                  @input=${a=>this.handleSlideInput(t._id,"subtitle",a.target.value)}
                  placeholder="Подзаголовок слайда"
                />
              </div>
              
              <div class="form-group">
                <label class="form-label">Порядок отображения</label>
                <input
                  type="number"
                  class="form-input"
                  .value=${t.order||1}
                  @input=${a=>this.handleSlideInput(t._id,"order",parseInt(a.target.value))}
                  min="1"
                />
              </div>
              
              <div class="form-group">
                <label class="form-label">Статус</label>
                <select
                  class="form-input"
                  .value=${t.active?"true":"false"}
                  @change=${a=>this.handleSlideInput(t._id,"active","true"===a.target.value)}
                >
                  <option value="true">Активен</option>
                  <option value="false">Неактивен</option>
                </select>
              </div>
            </div>
          </div>
        `)}
      </div>
    `}render(){return this.currentUser&&"admin"===this.currentUser.role?r`
      <div class="admin-container">
        <div class="admin-header">
          <h1 class="admin-title">Панель администратора</h1>
          <p class="admin-subtitle">Управление контентом и настройками FitStudio</p>
        </div>

        <div class="admin-tabs">
          <button 
            class="tab-button ${"dashboard"===this.activeTab?"active":""}"
            @click=${()=>this.switchTab("dashboard")}
          >
            📊 Панель управления
          </button>
          <button 
            class="tab-button ${"content"===this.activeTab?"active":""}"
            @click=${()=>this.switchTab("content")}
          >
            ✏️ Главная страница
          </button>
          <button 
            class="tab-button ${"slider"===this.activeTab?"active":""}"
            @click=${()=>this.switchTab("slider")}
          >
            🖼️ Слайдер
          </button>
        </div>

        <div class="tab-content">
          ${"dashboard"===this.activeTab?this.renderDashboard():""}
          ${"content"===this.activeTab?this.renderContentEditor():""}
          ${"slider"===this.activeTab?this.renderSliderEditor():""}
        </div>
      </div>
    `:r`
        <div class="admin-container">
          <div style="text-align: center; padding: var(--spacing-12);">
            <h2>Доступ запрещен</h2>
            <p>У вас нет прав для доступа к админ-панели</p>
            <a href="/" class="save-button" style="margin-top: var(--spacing-4); display: inline-flex;">
              Вернуться на главную
            </a>
          </div>
        </div>
      `}}customElements.define("admin-page",v);class h extends t{static styles=a`
    :host {
      display: block;
    }

    .not-found {
      text-align: center;
      padding: var(--spacing-20) var(--spacing-4);
      max-width: var(--container-md);
      margin: 0 auto;
    }

    .error-code {
      font-size: 8rem;
      font-weight: 700;
      color: var(--primary-color);
      margin-bottom: var(--spacing-4);
      line-height: 1;
    }

    .error-title {
      font-size: var(--font-size-3xl);
      font-weight: 600;
      color: var(--gray-800);
      margin-bottom: var(--spacing-4);
    }

    .error-message {
      font-size: var(--font-size-lg);
      color: var(--gray-600);
      margin-bottom: var(--spacing-8);
      line-height: 1.6;
    }

    .error-actions {
      display: flex;
      gap: var(--spacing-4);
      justify-content: center;
      flex-wrap: wrap;
    }

    .illustration {
      width: 200px;
      height: 200px;
      margin: 0 auto var(--spacing-8);
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 4rem;
      color: white;
    }

    @media (max-width: 768px) {
      .error-code {
        font-size: 6rem;
      }

      .error-title {
        font-size: var(--font-size-2xl);
      }

      .error-message {
        font-size: var(--font-size-base);
      }

      .error-actions {
        flex-direction: column;
        align-items: center;
      }

      .error-actions .btn {
        width: 100%;
        max-width: 300px;
      }
    }
  `;static properties={message:{type:String}};constructor(){super(),this.message=""}render(){const t="Доступ запрещен"===this.message;return r`
      <div class="not-found">
        <div class="illustration">
          ${t?"🔒":"🤔"}
        </div>
        
        <div class="error-code">
          ${t?"403":"404"}
        </div>
        
        <h1 class="error-title">
          ${t?"Доступ запрещен":"Страница не найдена"}
        </h1>
        
        <p class="error-message">
          ${t?"У вас нет прав для доступа к этой странице. Обратитесь к администратору для получения доступа.":"Извините, но страница, которую вы ищете, не существует. Возможно, она была перемещена или удалена."}
        </p>
        
        <div class="error-actions">
          <a href="/" class="btn btn-primary">
            Вернуться на главную
          </a>
          ${t?"":r`
            <button class="btn btn-outline" @click=${()=>window.history.back()}>
              Назад
            </button>
          `}
        </div>
      </div>
    `}}customElements.define("not-found-page",h);class u extends t{static styles=a`
    :host {
      display: block;
    }

    .page-container {
      min-height: 60vh;
      animation: fadeIn 0.3s ease-in-out;
    }

    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translateY(20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
  `;static properties={currentRoute:{type:String},currentUser:{type:Object}};constructor(){super(),this.currentRoute="home",this.currentUser=null}handleUserLogin(t){this.dispatchEvent(new CustomEvent("user-login",{detail:t.detail}))}handleNavigation(t){this.dispatchEvent(new CustomEvent("navigate",{detail:t.detail}))}renderPage(){if("admin"===this.currentRoute&&(!this.currentUser||"admin"!==this.currentUser.role))return r`<not-found-page message="Доступ запрещен"></not-found-page>`;if(("login"===this.currentRoute||"register"===this.currentRoute)&&this.currentUser)return setTimeout(()=>{this.dispatchEvent(new CustomEvent("navigate",{detail:{path:"/"}}))},0),r`<home-page .currentUser=${this.currentUser}></home-page>`;switch(this.currentRoute){case"home":return r`<home-page .currentUser=${this.currentUser}></home-page>`;case"schedule":return r`<schedule-page .currentUser=${this.currentUser}></schedule-page>`;case"trainers":return r`<trainers-page></trainers-page>`;case"about":return r`<about-page></about-page>`;case"login":return r`<login-page @user-login=${this.handleUserLogin}></login-page>`;case"register":return r`<register-page @user-login=${this.handleUserLogin}></register-page>`;case"admin":return r`<admin-page .currentUser=${this.currentUser}></admin-page>`;default:return r`<not-found-page></not-found-page>`}}render(){return r`
      <div class="page-container">
        ${this.renderPage()}
      </div>
    `}}customElements.define("app-router",u);class m extends t{static styles=a`
    :host {
      display: block;
      background-color: var(--gray-800);
      color: var(--gray-300);
      margin-top: auto;
    }

    .footer {
      max-width: var(--container-xl);
      margin: 0 auto;
      padding: var(--spacing-12) var(--spacing-4) var(--spacing-6);
    }

    .footer-content {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: var(--spacing-8);
      margin-bottom: var(--spacing-8);
    }

    .footer-section h3 {
      color: white;
      font-size: var(--font-size-lg);
      font-weight: 600;
      margin-bottom: var(--spacing-4);
    }

    .footer-section p,
    .footer-section li {
      color: var(--gray-400);
      line-height: 1.6;
      margin-bottom: var(--spacing-2);
    }

    .footer-section ul {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    .footer-section a {
      color: var(--gray-400);
      text-decoration: none;
      transition: color var(--transition-fast);
    }

    .footer-section a:hover {
      color: var(--primary-light);
    }

    .contact-info {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-3);
    }

    .contact-item {
      display: flex;
      align-items: center;
      gap: var(--spacing-3);
    }

    .contact-icon {
      width: 20px;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: var(--primary-color);
      border-radius: var(--radius-md);
      color: white;
      font-size: var(--font-size-sm);
    }

    .social-links {
      display: flex;
      gap: var(--spacing-4);
      margin-top: var(--spacing-4);
    }

    .social-link {
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: var(--gray-700);
      border-radius: var(--radius-lg);
      color: var(--gray-400);
      text-decoration: none;
      transition: all var(--transition-fast);
    }

    .social-link:hover {
      background-color: var(--primary-color);
      color: white;
      transform: translateY(-2px);
    }

    .footer-bottom {
      border-top: 1px solid var(--gray-700);
      padding-top: var(--spacing-6);
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      gap: var(--spacing-4);
    }

    .footer-bottom p {
      margin: 0;
      color: var(--gray-500);
      font-size: var(--font-size-sm);
    }

    .footer-links {
      display: flex;
      gap: var(--spacing-6);
      list-style: none;
      margin: 0;
      padding: 0;
    }

    .footer-links a {
      color: var(--gray-500);
      text-decoration: none;
      font-size: var(--font-size-sm);
      transition: color var(--transition-fast);
    }

    .footer-links a:hover {
      color: var(--primary-light);
    }

    @media (max-width: 768px) {
      .footer-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-6);
      }

      .footer-bottom {
        flex-direction: column;
        text-align: center;
      }

      .footer-links {
        justify-content: center;
      }
    }
  `;render(){return r`
      <footer class="footer">
        <div class="footer-content">
          <div class="footer-section">
            <h3>FitStudio</h3>
            <p>Современная фитнес-студия с профессиональными тренерами и разнообразными программами тренировок. Мы поможем вам достичь ваших целей в фитнесе.</p>
            <div class="social-links">
              <a href="#" class="social-link" title="Instagram">📷</a>
              <a href="#" class="social-link" title="Facebook">📘</a>
              <a href="#" class="social-link" title="VKontakte">🔵</a>
              <a href="#" class="social-link" title="Telegram">✈️</a>
            </div>
          </div>

          <div class="footer-section">
            <h3>Быстрые ссылки</h3>
            <ul>
              <li><a href="/">Главная</a></li>
              <li><a href="/schedule">Расписание</a></li>
              <li><a href="/trainers">Тренеры</a></li>
              <li><a href="/about">О студии</a></li>
            </ul>
          </div>

          <div class="footer-section">
            <h3>Программы</h3>
            <ul>
              <li><a href="/schedule">Йога</a></li>
              <li><a href="/schedule">Пилатес</a></li>
              <li><a href="/schedule">Функциональный тренинг</a></li>
              <li><a href="/schedule">Танцевальная аэробика</a></li>
            </ul>
          </div>

          <div class="footer-section">
            <h3>Контакты</h3>
            <div class="contact-info">
              <div class="contact-item">
                <div class="contact-icon">📍</div>
                <span>г. Москва, ул. Спортивная, д. 15</span>
              </div>
              <div class="contact-item">
                <div class="contact-icon">📞</div>
                <span>+7 (495) 123-45-67</span>
              </div>
              <div class="contact-item">
                <div class="contact-icon">✉️</div>
                <span><EMAIL></span>
              </div>
              <div class="contact-item">
                <div class="contact-icon">🕒</div>
                <span>Пн-Пт: 07:00-23:00<br>Сб-Вс: 09:00-21:00</span>
              </div>
            </div>
          </div>
        </div>

        <div class="footer-bottom">
          <p>&copy; 2025 FitStudio. Все права защищены.</p>
          <ul class="footer-links">
            <li><a href="#">Политика конфиденциальности</a></li>
            <li><a href="#">Условия использования</a></li>
            <li><a href="#">Публичная оферта</a></li>
          </ul>
        </div>
      </footer>
    `}}customElements.define("app-footer",m);class f extends t{static styles=a`
    :host {
      display: block;
      min-height: 100vh;
      background-color: var(--gray-50);
    }

    .app-container {
      display: flex;
      flex-direction: column;
      min-height: 100vh;
    }

    main {
      flex: 1;
      padding: var(--spacing-4) 0;
    }

    .loading-overlay {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.9);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 9999;
      transition: opacity 0.3s ease;
    }

    .loading-overlay.hidden {
      opacity: 0;
      pointer-events: none;
    }

    .loading-spinner {
      text-align: center;
    }

    .spinner {
      width: 40px;
      height: 40px;
      border: 4px solid var(--gray-200);
      border-top: 4px solid var(--primary-color);
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 1rem;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  `;static properties={loading:{type:Boolean},currentUser:{type:Object},currentRoute:{type:String}};constructor(){super(),this.loading=!0,this.currentUser=null,this.currentRoute="home",console.log("App: Constructor called"),this.initializeApp()}async initializeApp(){try{await this.initializeDatabase(),this.checkUserSession(),this.setupRouting(),setTimeout(()=>{this.loading=!1;const t=document.getElementById("loading");t&&t.classList.add("hidden")},1e3)}catch(t){console.error("Ошибка инициализации приложения:",t),this.loading=!1}}async initializeDatabase(){console.log("База данных инициализирована")}checkUserSession(){const t=localStorage.getItem("currentUser");if(t)try{this.currentUser=JSON.parse(t)}catch(t){console.error("Ошибка восстановления сессии:",t),localStorage.removeItem("currentUser")}}setupRouting(){window.addEventListener("popstate",()=>{this.updateRoute()}),document.addEventListener("click",t=>{if(t.target.matches('a[href^="/"]')||t.target.closest('a[href^="/"]')){t.preventDefault();const a=t.target.matches("a")?t.target:t.target.closest("a");this.navigateTo(a.getAttribute("href"))}}),this.updateRoute()}updateRoute(){const t=window.location.pathname;this.currentRoute="/"===t?"home":t.slice(1),this.requestUpdate()}navigateTo(t){window.history.pushState({},"",t),this.updateRoute()}handleUserLogin(t){this.currentUser=t.detail.user,localStorage.setItem("currentUser",JSON.stringify(this.currentUser)),this.requestUpdate()}handleUserLogout(){this.currentUser=null,localStorage.removeItem("currentUser"),this.navigateTo("/"),this.requestUpdate()}render(){return r`
      <div class="app-container">
        <app-header 
          .currentUser=${this.currentUser}
          @user-logout=${this.handleUserLogout}
        ></app-header>
        
        <main>
          <app-router 
            .currentRoute=${this.currentRoute}
            .currentUser=${this.currentUser}
            @user-login=${this.handleUserLogin}
            @navigate=${t=>this.navigateTo(t.detail.path)}
          ></app-router>
        </main>
        
        <app-footer></app-footer>
        
        ${this.loading?r`
          <div class="loading-overlay">
            <div class="loading-spinner">
              <div class="spinner"></div>
              <p>Загрузка приложения...</p>
            </div>
          </div>
        `:""}
      </div>
    `}}customElements.define("fitness-app",f);
//# sourceMappingURL=bundle.js.map
