import { LitElement, html, css } from '/node_modules/lit/index.js';
import './home-page.js';
import './schedule-page.js';
import './trainers-page.js';
import './about-page.js';
import './login-page.js';
import './register-page.js';
import './admin-page.js';
import './not-found-page.js';

class AppRouter extends LitElement {
  static styles = css`
    :host {
      display: block;
    }

    .page-container {
      min-height: 60vh;
      animation: fadeIn 0.3s ease-in-out;
    }

    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translateY(20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
  `;

  static properties = {
    currentRoute: { type: String },
    currentUser: { type: Object }
  };

  constructor() {
    super();
    this.currentRoute = 'home';
    this.currentUser = null;
  }

  handleUserLogin(event) {
    this.dispatchEvent(new CustomEvent('user-login', {
      detail: event.detail
    }));
  }

  handleNavigation(event) {
    this.dispatchEvent(new CustomEvent('navigate', {
      detail: event.detail
    }));
  }

  renderPage() {
    // Проверка доступа к админ-страницам
    if (this.currentRoute === 'admin' && (!this.currentUser || this.currentUser.role !== 'admin')) {
      return html`<not-found-page message="Доступ запрещен"></not-found-page>`;
    }

    // Перенаправление авторизованных пользователей с страниц входа/регистрации
    if ((this.currentRoute === 'login' || this.currentRoute === 'register') && this.currentUser) {
      setTimeout(() => {
        this.dispatchEvent(new CustomEvent('navigate', {
          detail: { path: '/' }
        }));
      }, 0);
      return html`<home-page .currentUser=${this.currentUser}></home-page>`;
    }

    switch (this.currentRoute) {
      case 'home':
        return html`<home-page .currentUser=${this.currentUser}></home-page>`;
      
      case 'schedule':
        return html`<schedule-page .currentUser=${this.currentUser}></schedule-page>`;
      
      case 'trainers':
        return html`<trainers-page></trainers-page>`;
      
      case 'about':
        return html`<about-page></about-page>`;
      
      case 'login':
        return html`<login-page @user-login=${this.handleUserLogin}></login-page>`;
      
      case 'register':
        return html`<register-page @user-login=${this.handleUserLogin}></register-page>`;
      
      case 'admin':
        return html`<admin-page .currentUser=${this.currentUser}></admin-page>`;
      
      default:
        return html`<not-found-page></not-found-page>`;
    }
  }

  render() {
    return html`
      <div class="page-container">
        ${this.renderPage()}
      </div>
    `;
  }
}

customElements.define('app-router', AppRouter);
