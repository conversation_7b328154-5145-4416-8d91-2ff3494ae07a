export default {
  port: 8081,
  nodeResolve: true,
  open: true,
  watch: true,
  appIndex: 'index.html',
  rootDir: '.',
  middleware: [
    // Middleware для обработки SPA маршрутизации
    function rewriteIndex(context, next) {
      if (context.url !== '/' && !context.url.includes('.') && !context.url.startsWith('/node_modules')) {
        context.url = '/';
      }
      return next();
    }
  ]
};
