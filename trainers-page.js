import { LitElement, html, css } from '/node_modules/lit/index.js';
import { databaseService } from './database.js';

class TrainersPage extends LitElement {
  static styles = css`
    :host {
      display: block;
    }

    .trainers-container {
      max-width: var(--container-xl);
      margin: 0 auto;
      padding: var(--spacing-6) var(--spacing-4);
    }

    .trainers-header {
      text-align: center;
      margin-bottom: var(--spacing-12);
    }

    .trainers-title {
      font-size: var(--font-size-3xl);
      font-weight: 700;
      color: var(--gray-800);
      margin-bottom: var(--spacing-4);
    }

    .trainers-subtitle {
      font-size: var(--font-size-lg);
      color: var(--gray-600);
      max-width: 600px;
      margin: 0 auto;
      line-height: 1.6;
    }

    .trainers-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
      gap: var(--spacing-8);
    }

    .trainer-card {
      background: white;
      border-radius: var(--radius-2xl);
      box-shadow: var(--shadow-lg);
      overflow: hidden;
      transition: all var(--transition-normal);
      position: relative;
    }

    .trainer-card:hover {
      transform: translateY(-8px);
      box-shadow: var(--shadow-xl);
    }

    .trainer-image {
      width: 100%;
      height: 280px;
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 4rem;
      position: relative;
      overflow: hidden;
    }

    .trainer-image::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
      transform: translateX(-100%);
      transition: transform 0.6s ease;
    }

    .trainer-card:hover .trainer-image::before {
      transform: translateX(100%);
    }

    .trainer-content {
      padding: var(--spacing-6);
    }

    .trainer-name {
      font-size: var(--font-size-xl);
      font-weight: 700;
      color: var(--gray-800);
      margin-bottom: var(--spacing-2);
    }

    .trainer-specializations {
      display: flex;
      flex-wrap: wrap;
      gap: var(--spacing-2);
      margin-bottom: var(--spacing-4);
    }

    .specialization-tag {
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      color: white;
      padding: var(--spacing-1) var(--spacing-3);
      border-radius: var(--radius-xl);
      font-size: var(--font-size-xs);
      font-weight: 500;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .trainer-bio {
      color: var(--gray-600);
      line-height: 1.6;
      margin-bottom: var(--spacing-4);
    }

    .trainer-experience {
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
      color: var(--gray-700);
      font-weight: 500;
      margin-bottom: var(--spacing-4);
    }

    .experience-icon {
      width: 24px;
      height: 24px;
      background: var(--secondary-color);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: var(--font-size-sm);
    }

    .trainer-actions {
      display: flex;
      gap: var(--spacing-3);
    }

    .action-btn {
      flex: 1;
      padding: var(--spacing-3) var(--spacing-4);
      border: none;
      border-radius: var(--radius-lg);
      font-size: var(--font-size-sm);
      font-weight: 500;
      cursor: pointer;
      transition: all var(--transition-fast);
      text-decoration: none;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: var(--spacing-2);
    }

    .btn-primary {
      background-color: var(--primary-color);
      color: white;
    }

    .btn-primary:hover {
      background-color: var(--primary-dark);
      transform: translateY(-1px);
    }

    .btn-outline {
      background-color: transparent;
      color: var(--primary-color);
      border: 2px solid var(--primary-color);
    }

    .btn-outline:hover {
      background-color: var(--primary-color);
      color: white;
    }

    .loading-state {
      text-align: center;
      padding: var(--spacing-12);
    }

    .spinner {
      width: 40px;
      height: 40px;
      border: 4px solid var(--gray-200);
      border-top: 4px solid var(--primary-color);
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto var(--spacing-4);
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .empty-state {
      text-align: center;
      padding: var(--spacing-12);
      color: var(--gray-500);
    }

    .empty-state-icon {
      font-size: 4rem;
      margin-bottom: var(--spacing-4);
    }

    .stats-section {
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      color: white;
      padding: var(--spacing-12) 0;
      margin: var(--spacing-16) 0;
      border-radius: var(--radius-2xl);
    }

    .stats-container {
      max-width: var(--container-lg);
      margin: 0 auto;
      padding: 0 var(--spacing-4);
      text-align: center;
    }

    .stats-title {
      font-size: var(--font-size-2xl);
      font-weight: 700;
      margin-bottom: var(--spacing-8);
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: var(--spacing-6);
    }

    .stat-item {
      text-align: center;
    }

    .stat-number {
      font-size: var(--font-size-3xl);
      font-weight: 700;
      margin-bottom: var(--spacing-2);
    }

    .stat-label {
      font-size: var(--font-size-base);
      opacity: 0.9;
    }

    .trainer-modal {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1000;
      padding: var(--spacing-4);
    }

    .modal-content {
      background: white;
      border-radius: var(--radius-xl);
      max-width: 600px;
      width: 100%;
      max-height: 90vh;
      overflow-y: auto;
      position: relative;
    }

    .modal-header {
      padding: var(--spacing-6);
      border-bottom: 1px solid var(--gray-200);
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .modal-title {
      font-size: var(--font-size-xl);
      font-weight: 700;
      color: var(--gray-800);
    }

    .close-btn {
      background: none;
      border: none;
      font-size: var(--font-size-xl);
      color: var(--gray-500);
      cursor: pointer;
      padding: var(--spacing-2);
      border-radius: var(--radius-md);
      transition: all var(--transition-fast);
    }

    .close-btn:hover {
      background-color: var(--gray-100);
      color: var(--gray-700);
    }

    .modal-body {
      padding: var(--spacing-6);
    }

    @media (max-width: 768px) {
      .trainers-grid {
        grid-template-columns: 1fr;
      }

      .trainer-actions {
        flex-direction: column;
      }

      .stats-grid {
        grid-template-columns: repeat(2, 1fr);
      }

      .modal-content {
        margin: var(--spacing-4);
        max-height: calc(100vh - 2rem);
      }
    }

    @media (max-width: 480px) {
      .stats-grid {
        grid-template-columns: 1fr;
      }
    }
  `;

  static properties = {
    trainers: { type: Array },
    loading: { type: Boolean },
    selectedTrainer: { type: Object },
    showModal: { type: Boolean }
  };

  constructor() {
    super();
    this.trainers = [];
    this.loading = true;
    this.selectedTrainer = null;
    this.showModal = false;
    
    this.loadTrainers();
  }

  async loadTrainers() {
    try {
      this.loading = true;
      this.trainers = await databaseService.getTrainers();
    } catch (error) {
      console.error('Ошибка загрузки тренеров:', error);
    } finally {
      this.loading = false;
    }
  }

  openTrainerModal(trainer) {
    this.selectedTrainer = trainer;
    this.showModal = true;
    document.body.style.overflow = 'hidden';
  }

  closeTrainerModal() {
    this.showModal = false;
    this.selectedTrainer = null;
    document.body.style.overflow = '';
  }

  handleModalClick(event) {
    if (event.target === event.currentTarget) {
      this.closeTrainerModal();
    }
  }

  getTrainerInitials(name) {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  }

  render() {
    if (this.loading) {
      return html`
        <div class="trainers-container">
          <div class="loading-state">
            <div class="spinner"></div>
            <p>Загрузка информации о тренерах...</p>
          </div>
        </div>
      `;
    }

    if (this.trainers.length === 0) {
      return html`
        <div class="trainers-container">
          <div class="empty-state">
            <div class="empty-state-icon">👨‍🏫</div>
            <h3>Информация о тренерах недоступна</h3>
            <p>Попробуйте обновить страницу позже</p>
          </div>
        </div>
      `;
    }

    return html`
      <div class="trainers-container">
        <div class="trainers-header">
          <h1 class="trainers-title">Наши тренеры</h1>
          <p class="trainers-subtitle">
            Познакомьтесь с нашей командой профессиональных тренеров. 
            Каждый из них имеет многолетний опыт и поможет вам достичь ваших целей в фитнесе.
          </p>
        </div>

        <div class="stats-section">
          <div class="stats-container">
            <h2 class="stats-title">Наша команда в цифрах</h2>
            <div class="stats-grid">
              <div class="stat-item">
                <div class="stat-number">${this.trainers.length}</div>
                <div class="stat-label">Профессиональных тренеров</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">50+</div>
                <div class="stat-label">Лет общего опыта</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">1000+</div>
                <div class="stat-label">Довольных клиентов</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">15+</div>
                <div class="stat-label">Видов тренировок</div>
              </div>
            </div>
          </div>
        </div>

        <div class="trainers-grid">
          ${this.trainers.map(trainer => html`
            <div class="trainer-card">
              <div class="trainer-image">
                ${this.getTrainerInitials(trainer.name)}
              </div>
              <div class="trainer-content">
                <h3 class="trainer-name">${trainer.name}</h3>
                
                <div class="trainer-specializations">
                  ${trainer.specialization.map(spec => html`
                    <span class="specialization-tag">${spec}</span>
                  `)}
                </div>
                
                <p class="trainer-bio">${trainer.bio}</p>
                
                <div class="trainer-experience">
                  <div class="experience-icon">⭐</div>
                  <span>Опыт работы: ${trainer.experience}</span>
                </div>
                
                <div class="trainer-actions">
                  <button class="action-btn btn-primary" 
                          @click=${() => this.openTrainerModal(trainer)}>
                    👁️ Подробнее
                  </button>
                  <a href="/schedule" class="action-btn btn-outline">
                    📅 Расписание
                  </a>
                </div>
              </div>
            </div>
          `)}
        </div>
      </div>

      ${this.showModal && this.selectedTrainer ? html`
        <div class="trainer-modal" @click=${this.handleModalClick}>
          <div class="modal-content">
            <div class="modal-header">
              <h2 class="modal-title">${this.selectedTrainer.name}</h2>
              <button class="close-btn" @click=${this.closeTrainerModal}>×</button>
            </div>
            <div class="modal-body">
              <div class="trainer-image" style="height: 200px; margin-bottom: var(--spacing-6);">
                ${this.getTrainerInitials(this.selectedTrainer.name)}
              </div>
              
              <div class="trainer-specializations" style="margin-bottom: var(--spacing-4);">
                ${this.selectedTrainer.specialization.map(spec => html`
                  <span class="specialization-tag">${spec}</span>
                `)}
              </div>
              
              <div class="trainer-experience" style="margin-bottom: var(--spacing-4);">
                <div class="experience-icon">⭐</div>
                <span>Опыт работы: ${this.selectedTrainer.experience}</span>
              </div>
              
              <p class="trainer-bio" style="margin-bottom: var(--spacing-6);">
                ${this.selectedTrainer.bio}
              </p>
              
              <div style="display: flex; gap: var(--spacing-3);">
                <a href="/schedule" class="action-btn btn-primary" style="flex: 1;">
                  📅 Посмотреть расписание
                </a>
                <button class="action-btn btn-outline" @click=${this.closeTrainerModal}>
                  Закрыть
                </button>
              </div>
            </div>
          </div>
        </div>
      ` : ''}
    `;
  }
}

customElements.define('trainers-page', TrainersPage);
