!function(e){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{var n;n="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this,n.immediate=e()}}(function(){return function(){function e(n,t,o){function i(r,f){if(!t[r]){if(!n[r]){var a="function"==typeof require&&require;if(!f&&a)return a(r,!0);if(u)return u(r,!0);var s=new Error("Cannot find module '"+r+"'");throw s.code="MODULE_NOT_FOUND",s}var l=t[r]={exports:{}};n[r][0].call(l.exports,function(e){return i(n[r][1][e]||e)},l,l.exports,e,n,t,o)}return t[r].exports}for(var u="function"==typeof require&&require,r=0;r<o.length;r++)i(o[r]);return i}return e}()({1:[function(e,n,t){"use strict";function o(){f&&a&&(f=!1,a.length?d=a.concat(d):c=-1,d.length&&i())}function i(){if(!f){p=!1,f=!0;for(var e=d.length,n=setTimeout(o);e;){for(a=d,d=[];a&&++c<e;)a[c].run();c=-1,e=d.length}a=null,c=-1,f=!1,clearTimeout(n)}}function u(e,n){this.fun=e,this.array=n}function r(e){var n=new Array(arguments.length-1);if(arguments.length>1)for(var t=1;t<arguments.length;t++)n[t-1]=arguments[t];d.push(new u(e,n)),p||f||(p=!0,s())}for(var f,a,s,l=[e("./nextTick"),e("./queueMicrotask"),e("./mutation.js"),e("./messageChannel"),e("./stateChange"),e("./timeout")],c=-1,d=[],p=!1,h=-1,g=l.length;++h<g;)if(l[h]&&l[h].test&&l[h].test()){s=l[h].install(i);break}u.prototype.run=function(){var e=this.fun,n=this.array;switch(n.length){case 0:return e();case 1:return e(n[0]);case 2:return e(n[0],n[1]);case 3:return e(n[0],n[1],n[2]);default:return e.apply(null,n)}},n.exports=r},{"./messageChannel":2,"./mutation.js":3,"./nextTick":7,"./queueMicrotask":4,"./stateChange":5,"./timeout":6}],2:[function(e,n,t){(function(e){"use strict";t.test=function(){return!e.setImmediate&&void 0!==e.MessageChannel},t.install=function(n){var t=new e.MessageChannel;return t.port1.onmessage=n,function(){t.port2.postMessage(0)}}}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],3:[function(e,n,t){(function(e){"use strict";var n=e.MutationObserver||e.WebKitMutationObserver;t.test=function(){return n},t.install=function(t){var o=0,i=new n(t),u=e.document.createTextNode("");return i.observe(u,{characterData:!0}),function(){u.data=o=++o%2}}}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],4:[function(e,n,t){(function(e){"use strict";t.test=function(){return"function"==typeof e.queueMicrotask},t.install=function(n){return function(){e.queueMicrotask(n)}}}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],5:[function(e,n,t){(function(e){"use strict";t.test=function(){return"document"in e&&"onreadystatechange"in e.document.createElement("script")},t.install=function(n){return function(){var t=e.document.createElement("script");return t.onreadystatechange=function(){n(),t.onreadystatechange=null,t.parentNode.removeChild(t),t=null},e.document.documentElement.appendChild(t),n}}}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],6:[function(e,n,t){"use strict";t.test=function(){return!0},t.install=function(e){return function(){setTimeout(e,0)}}},{}],7:[function(e,n,t){},{}]},{},[1])(1)});
