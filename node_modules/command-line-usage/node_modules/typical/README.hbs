[![view on npm](https://badgen.net/npm/v/typical)](https://www.npmjs.org/package/typical)
[![npm module downloads](https://badgen.net/npm/dt/typical)](https://www.npmjs.org/package/typical)
[![Gihub repo dependents](https://badgen.net/github/dependents-repo/75lb/typical)](https://github.com/75lb/typical/network/dependents?dependent_type=REPOSITORY)
[![Gihub package dependents](https://badgen.net/github/dependents-pkg/75lb/typical)](https://github.com/75lb/typical/network/dependents?dependent_type=PACKAGE)
[![Node.js CI](https://github.com/75lb/typical/actions/workflows/node.js.yml/badge.svg)](https://github.com/75lb/typical/actions/workflows/node.js.yml)
[![js-standard-style](https://img.shields.io/badge/code%20style-standard-brightgreen.svg)](https://github.com/feross/standard)

{{>main}}

## Load anywhere

This library is compatible with Node.js, the Web and any style of module loader. It can be loaded anywhere, natively without transpilation.

Within a Node.js ECMAScript Module:

```js
import t from 'typical'
import { isNumber } from 'typical'
```

CommonJS:

```js
const t = require('typical')
const { isNumber } = require('typical')
```

Within a modern browser ECMAScript Module:

```js
import t from './node_modules/typical/index.js'
```

* * *

&copy; 2014-25 Lloyd Brookes \<<EMAIL>\>.

Test suite by [test-runner](https://github.com/test-runner-js/test-runner). Documented by [jsdoc-to-markdown](https://github.com/jsdoc2md/jsdoc-to-markdown).
