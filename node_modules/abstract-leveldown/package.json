{"name": "abstract-leveldown", "version": "6.2.3", "description": "An abstract prototype matching the LevelDOWN API", "license": "MIT", "main": "index.js", "browser": {"./next-tick.js": "./next-tick-browser.js"}, "scripts": {"test": "standard && hallmark && nyc node test/self.js", "test-browsers": "airtap --coverage --loopback airtap.local test/self.js", "test-browser-local": "airtap --coverage --local test/self.js", "coverage": "nyc report --reporter=text-lcov | coveralls", "hallmark": "hallmark --fix", "dependency-check": "dependency-check . test/*.js", "prepublishOnly": "npm run dependency-check"}, "dependencies": {"buffer": "^5.5.0", "immediate": "^3.2.3", "level-concat-iterator": "~2.0.0", "level-supports": "~1.0.0", "xtend": "~4.0.0"}, "devDependencies": {"airtap": "^3.0.0", "coveralls": "^3.0.2", "dependency-check": "^3.3.0", "hallmark": "^2.0.0", "level-community": "^3.0.0", "nyc": "^14.0.0", "sinon": "^7.2.4", "standard": "^14.0.0", "tape": "^4.10.0"}, "hallmark": {"community": "level-community"}, "repository": {"type": "git", "url": "https://github.com/Level/abstract-leveldown.git"}, "homepage": "https://github.com/Level/abstract-leveldown", "keywords": ["level", "leveldb", "leveldown", "levelup"], "engines": {"node": ">=6"}}