sudo: false
language: node_js

matrix:
  fast_finish: true
  include:
  - node_js: 6
    env: CMD=test
  - node_js: 8
    env: CMD=test
  - node_js: 10
    env: CMD=test
  - node_js: stable
    env: CMD=test-browsers
    addons:
      sauce_connect: true
      hosts:
        - airtap.local

before_script: git fetch --tags

script:
  - DEBUG=airtap:* npm run $CMD

after_success: npm run coverage

notifications:
  email: false
