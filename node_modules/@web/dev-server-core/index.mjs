// this file is autogenerated with the generate-mjs-dts-entrypoints script
import cjsEntrypoint from './dist/index.js';

const {
  FSWatcher,
  Koa,
  Server,
  WebSocket,
  DevServer,
  WebSocketsManager,
  getRequestBrowserPath,
  getRequestFilePath,
  getResponseBody,
  getHtmlPath,
  isInlineScriptRequest,
  PluginSyntaxError,
  PluginError,
} = cjsEntrypoint;

export {
  FSWatcher,
  Koa,
  Server,
  WebSocket,
  DevServer,
  WebSocketsManager,
  getRequestBrowserPath,
  getRequestFilePath,
  getResponseBody,
  getHtmlPath,
  isInlineScriptRequest,
  PluginSyntaxError,
  PluginError,
};
