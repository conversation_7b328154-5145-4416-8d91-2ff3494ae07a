"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PluginTransformCache = void 0;
const lru_cache_1 = __importDefault(require("lru-cache"));
const fs_1 = __importDefault(require("fs"));
const util_1 = require("util");
const utils_js_1 = require("../utils.js");
const fsStat = (0, util_1.promisify)(fs_1.default.stat);
async function fileExists(filePath) {
    try {
        const stat = await fsStat(filePath);
        return stat.isFile();
    }
    catch (_a) {
        return false;
    }
}
/**
 * Cache for file transformations.
 */
class PluginTransformCache {
    constructor(fileWatcher, rootDir) {
        this.fileWatcher = fileWatcher;
        this.rootDir = rootDir;
        this.cacheKeysPerFilePath = new Map();
        this.lruCache = new lru_cache_1.default({
            sizeCalculation: (e, key) => e.body.length + (key ? key.length : 0),
            maxSize: 52428800,
            noDisposeOnSet: true,
            dispose: (_value, cacheKey) => {
                // remove file path -> url mapping when we are no longer caching it
                for (const [filePath, cacheKeysForFilePath] of this.cacheKeysPerFilePath.entries()) {
                    if (cacheKeysForFilePath.has(cacheKey)) {
                        this.cacheKeysPerFilePath.delete(filePath);
                        return;
                    }
                }
            },
        });
        // remove file from cache on change
        const removeCacheListener = (filePath) => {
            const cacheKeys = this.cacheKeysPerFilePath.get(filePath);
            if (cacheKeys) {
                for (const cacheKey of cacheKeys) {
                    this.lruCache.delete(cacheKey);
                }
            }
        };
        fileWatcher.addListener('change', removeCacheListener);
        fileWatcher.addListener('unlink', removeCacheListener);
    }
    async get(cacheKey) {
        return this.lruCache.get(cacheKey);
    }
    async set(filePath, body, headers, cacheKey) {
        try {
            if (!(await fileExists(filePath))) {
                // only cache files on the file system
                return;
            }
            if (typeof body === 'string') {
                let cacheKeysForFilePath = this.cacheKeysPerFilePath.get(filePath);
                if (!cacheKeysForFilePath) {
                    cacheKeysForFilePath = new Set();
                    this.cacheKeysPerFilePath.set(filePath, cacheKeysForFilePath);
                }
                cacheKeysForFilePath.add(cacheKey);
                this.lruCache.set(cacheKey, { body, headers, filePath });
            }
        }
        catch (error) {
            if (error instanceof utils_js_1.RequestCancelledError) {
                // no need to do anything if the request was cancelled
                return;
            }
            throw error;
        }
    }
}
exports.PluginTransformCache = PluginTransformCache;
//# sourceMappingURL=PluginTransformCache.js.map