"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.watchServedFilesMiddleware = void 0;
const fs_1 = __importDefault(require("fs"));
const utils_js_1 = require("../utils.js");
/**
 * Sets up a middleware which tracks served files and sends a reload message to any
 * active browsers when any of the files change.
 */
function watchServedFilesMiddleware(fileWatcher, rootDir) {
    return async (ctx, next) => {
        await next();
        if (ctx.response.status !== 404) {
            let filePath = (0, utils_js_1.getRequestFilePath)(ctx.url, rootDir);
            // if the request ends with a / it might be an index.html, check if it exists
            // and watch it
            if (filePath.endsWith('/')) {
                filePath += 'index.html';
            }
            // watch file if it exists
            fs_1.default.stat(filePath, (err, stats) => {
                if (!err && !stats.isDirectory()) {
                    fileWatcher.add(filePath);
                }
            });
        }
    };
}
exports.watchServedFilesMiddleware = watchServedFilesMiddleware;
//# sourceMappingURL=watchServedFilesMiddleware.js.map