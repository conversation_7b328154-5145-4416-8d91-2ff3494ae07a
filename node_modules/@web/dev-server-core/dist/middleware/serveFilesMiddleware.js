"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.serveFilesMiddleware = void 0;
const koa_send_1 = __importDefault(require("koa-send"));
const koa_static_1 = __importDefault(require("koa-static"));
const utils_js_1 = require("../utils.js");
/**
 * Creates multiple middleware used for serving files.
 */
function serveFilesMiddleware(rootDir) {
    const koaStaticOptions = {
        hidden: true,
        defer: true,
        brotli: false,
        gzip: false,
        setHeaders(res) {
            res.setHeader('cache-control', 'no-cache');
        },
    };
    // the wds-root-dir parameter indicates using a different root directory as a path relative
    // from the regular root dir or as an absolute path
    const serveCustomRootDirMiddleware = async (ctx, next) => {
        if ((0, utils_js_1.isOutsideRootDir)(ctx.path)) {
            const { normalizedPath, newRootDir } = (0, utils_js_1.resolvePathOutsideRootDir)(ctx.path, rootDir);
            await (0, koa_send_1.default)(ctx, normalizedPath, Object.assign(Object.assign({}, koaStaticOptions), { root: newRootDir }));
            return;
        }
        return next();
    };
    // serve static files from the regular root dir
    return [serveCustomRootDirMiddleware, (0, koa_static_1.default)(rootDir, koaStaticOptions)];
}
exports.serveFilesMiddleware = serveFilesMiddleware;
//# sourceMappingURL=serveFilesMiddleware.js.map