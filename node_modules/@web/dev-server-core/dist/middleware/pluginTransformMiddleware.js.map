{"version": 3, "file": "pluginTransformMiddleware.js", "sourceRoot": "", "sources": ["../../src/middleware/pluginTransformMiddleware.ts"], "names": [], "mappings": ";;;AAIA,uEAAiE;AACjE,0CAAyF;AAIzF;;GAEG;AACH,SAAgB,yBAAyB,CACvC,MAAc,EACd,MAA2B,EAC3B,WAAsB;;IAEtB,MAAM,KAAK,GAAG,IAAI,8CAAoB,CAAC,WAAW,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;IACpE,MAAM,gBAAgB,GAAG,CAAC,MAAA,MAAM,CAAC,OAAO,mCAAI,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,WAAW,IAAI,CAAC,CAAC,CAAC;IAC9E,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE;QACjC,uBAAuB;QACvB,OAAO,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,EAAE,CAAC;KAC9B;IAED,OAAO,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE;;QAC7B,qFAAqF;QACrF,8EAA8E;QAC9E,MAAM,QAAQ,GACZ,OAAO,CAAC,GAAG;YACX,CAAC,MAAM,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,WAAC,OAAA,MAAA,CAAC,CAAC,iBAAiB,kDAAG,OAAO,CAAC,CAAA,EAAA,CAAC,CAAC,CAAC;iBAC3E,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;iBACd,IAAI,CAAC,GAAG,CAAC,CAAC;QACf,MAAM,MAAM,GAAG,MAAM,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACzC,IAAI,MAAM,EAAE;YACV,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC;YAC3B,KAAK,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE;gBACnD,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;aAC5B;YACD,MAAM,CAAC,KAAK,CAAC,sBAAsB,QAAQ,+BAA+B,CAAC,CAAC;YAC5E,OAAO;SACR;QAED,MAAM,IAAI,EAAE,CAAC;QAEb,IAAI,OAAO,CAAC,MAAM,GAAG,GAAG,IAAI,OAAO,CAAC,MAAM,IAAI,GAAG,EAAE;YACjD,OAAO;SACR;QAED,IAAI;YACF,yDAAyD;YACzD,MAAM,IAAA,0BAAe,EAAC,OAAO,CAAC,CAAC;YAE/B,IAAI,YAAY,GAAG,KAAK,CAAC;YACzB,IAAI,eAAe,GAAG,KAAK,CAAC;YAC5B,KAAK,MAAM,MAAM,IAAI,gBAAgB,EAAE;gBACrC,MAAM,MAAM,GAAG,MAAM,CAAA,MAAA,MAAM,CAAC,SAAS,uDAAG,OAAO,CAAC,CAAA,CAAC;gBAEjD,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;oBAC9B,YAAY,GAAG,MAAM,CAAC,cAAc,KAAK,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC;oBACrE,IAAI,MAAM,CAAC,IAAI,IAAI,IAAI,EAAE;wBACvB,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC;wBAC3B,eAAe,GAAG,IAAI,CAAC;wBACvB,MAAM,CAAC,KAAK,CAAC,UAAU,MAAM,CAAC,IAAI,gBAAgB,OAAO,CAAC,IAAI,GAAG,CAAC,CAAC;qBACpE;oBAED,IAAI,MAAM,CAAC,OAAO,EAAE;wBAClB,KAAK,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE;4BACnD,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;yBAC5B;qBACF;iBACF;qBAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;oBACrC,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC;oBACtB,eAAe,GAAG,IAAI,CAAC;oBACvB,MAAM,CAAC,KAAK,CAAC,UAAU,MAAM,CAAC,IAAI,gBAAgB,OAAO,CAAC,IAAI,GAAG,CAAC,CAAC;iBACpE;aACF;YAED,IAAI,eAAe,IAAI,CAAC,YAAY,EAAE;gBACpC,MAAM,CAAC,KAAK,CAAC,oBAAoB,QAAQ,6BAA6B,CAAC,CAAC;gBACxE,MAAM,QAAQ,GAAG,IAAA,6BAAkB,EAAC,OAAO,CAAC,GAAG,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;gBACjE,KAAK,CAAC,GAAG,CACP,QAAQ,EACR,OAAO,CAAC,IAAI,EACZ,OAAO,CAAC,QAAQ,CAAC,OAAiC,EAClD,QAAQ,CACT,CAAC;aACH;SACF;QAAC,OAAO,CAAC,EAAE;YACV,IAAI,CAAC,YAAY,gCAAqB,EAAE;gBACtC,OAAO,SAAS,CAAC;aAClB;YACD,OAAO,CAAC,IAAI,GAAG,uEAAuE,CAAC;YACvF,OAAO,CAAC,MAAM,GAAG,GAAG,CAAC;YAErB,MAAM,KAAK,GAAG,CAA0B,CAAC;YAEzC,IAAI,KAAK,CAAC,IAAI,KAAK,mBAAmB,EAAE;gBACtC,MAAM,CAAC,cAAc,CAAC,KAA0B,CAAC,CAAC;gBAClD,OAAO;aACR;YAED,IAAI,KAAK,CAAC,IAAI,KAAK,aAAa,EAAE;gBAChC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;gBAC5B,OAAO;aACR;YAED,MAAM,KAAK,CAAC;SACb;IACH,CAAC,CAAC;AACJ,CAAC;AAjGD,8DAiGC"}