{"version": 3, "file": "pluginServeMiddleware.js", "sourceRoot": "", "sources": ["../../src/middleware/pluginServeMiddleware.ts"], "names": [], "mappings": ";;;;;;AACA,gDAAwB;AAIxB;;GAEG;AACH,SAAgB,qBAAqB,CAAC,MAAc,EAAE,OAAiB;IACrE,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,IAAI,CAAC,CAAC,CAAC;IACvD,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE;QAC7B,mBAAmB;QACnB,OAAO,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,EAAE,CAAC;KAC9B;IAED,OAAO,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE;;QAC7B,KAAK,MAAM,MAAM,IAAI,YAAY,EAAE;YACjC,MAAM,QAAQ,GAAG,MAAM,CAAA,MAAA,MAAM,CAAC,KAAK,uDAAG,OAAO,CAAC,CAAA,CAAC;YAE/C,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;gBAChC,IAAI,QAAQ,CAAC,IAAI,IAAI,IAAI,EAAE;oBACzB,MAAM,IAAI,KAAK,CACb,0FAA0F,CAC3F,CAAC;iBACH;gBAED,OAAO,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;gBAC7B,IAAI,QAAQ,CAAC,IAAI,IAAI,IAAI,EAAE;oBACzB,OAAO,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;iBAC9B;qBAAM;oBACL,OAAO,CAAC,IAAI,GAAG,cAAI,CAAC,OAAO,CAAC,cAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;iBAC1D;gBAED,IAAI,QAAQ,CAAC,OAAO,EAAE;oBACpB,KAAK,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;wBACrD,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;qBAC5B;iBACF;gBAED,MAAM,CAAC,KAAK,CAAC,UAAU,MAAM,CAAC,IAAI,WAAW,OAAO,CAAC,IAAI,GAAG,CAAC,CAAC;gBAC9D,OAAO,CAAC,MAAM,GAAG,GAAG,CAAC;gBACrB,OAAO;aACR;iBAAM,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;gBACvC,OAAO,CAAC,IAAI,GAAG,QAAQ,CAAC;gBACxB,OAAO,CAAC,IAAI,GAAG,cAAI,CAAC,OAAO,CAAC,cAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;gBACzD,MAAM,CAAC,KAAK,CAAC,UAAU,MAAM,CAAC,IAAI,WAAW,OAAO,CAAC,IAAI,GAAG,CAAC,CAAC;gBAC9D,OAAO,CAAC,MAAM,GAAG,GAAG,CAAC;gBACrB,OAAO;aACR;SACF;QACD,OAAO,IAAI,EAAE,CAAC;IAChB,CAAC,CAAC;AACJ,CAAC;AA5CD,sDA4CC"}