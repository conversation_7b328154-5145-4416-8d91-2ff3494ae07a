import { FSWatcher } from 'chokidar';
import { Middleware } from 'koa';
import { DevServerCoreConfig } from '../server/DevServerCoreConfig.js';
import { Logger } from '../logger/Logger.js';
/**
 * Sets up a middleware which allows plugins to transform files before they are served to the browser.
 */
export declare function pluginTransformMiddleware(logger: Logger, config: DevServerCoreConfig, fileWatcher: FSWatcher): Middleware;
//# sourceMappingURL=pluginTransformMiddleware.d.ts.map