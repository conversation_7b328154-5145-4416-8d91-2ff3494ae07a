{"version": 3, "file": "addPlugins.js", "sourceRoot": "", "sources": ["../../src/server/addPlugins.ts"], "names": [], "mappings": ";;;AACA,gGAA0F;AAC1F,4EAAsE;AACtE,sEAAgE;AAGhE,SAAgB,UAAU,CAAC,MAAc,EAAE,MAA2B;;IACpE,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;QACnB,MAAM,CAAC,OAAO,GAAG,EAAE,CAAC;KACrB;IAED,IAAI,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;QAChE,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,IAAA,oCAAe,EAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC;KAC3D;IAED,IAAI,MAAM,CAAC,eAAe,KAAI,MAAA,MAAM,CAAC,OAAO,0CAAE,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,eAAe,CAAC,CAAA,EAAE;QAC5E,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,IAAA,sCAAgB,GAAE,CAAC,CAAC;KAC5C;IAED,IAAI,MAAA,MAAM,CAAC,OAAO,0CAAE,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,eAAe,IAAI,EAAE,IAAI,iBAAiB,IAAI,EAAE,CAAC,EAAE;QAChF,4FAA4F;QAC5F,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,IAAA,8DAA4B,EAAC,MAAM,EAAE,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;KAC3F;AACH,CAAC;AAjBD,gCAiBC"}