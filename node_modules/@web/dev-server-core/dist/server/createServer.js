"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createServer = void 0;
const koa_1 = __importDefault(require("koa"));
const path_1 = __importDefault(require("path"));
const http_1 = __importDefault(require("http"));
const http2_1 = __importDefault(require("http2"));
const fs_1 = __importDefault(require("fs"));
const net_1 = __importDefault(require("net"));
const createMiddleware_js_1 = require("./createMiddleware.js");
const addPlugins_js_1 = require("./addPlugins.js");
/**
 * A request handler that returns a 301 HTTP Redirect to the same location as the original
 * request but using the https protocol
 */
function httpsRedirect(req, res) {
    const { host } = req.headers;
    res.writeHead(301, { Location: `https://${host}${req.url}` });
    res.end();
}
/**
 * Creates a koa server with middlewares, but does not start it. Returns the koa app and
 * http server instances.
 */
function createServer(logger, cfg, fileWatcher, middlewareMode = false) {
    const app = new koa_1.default();
    app.silent = true;
    app.on('error', error => {
        if (['EPIPE', 'ECONNRESET', 'ERR_STREAM_PREMATURE_CLOSE'].includes(error.code)) {
            return;
        }
        console.error('Error while handling server request.');
        console.error(error);
    });
    (0, addPlugins_js_1.addPlugins)(logger, cfg);
    // special case the legacy plugin, if it is given make sure the resolve module imports plugin
    // runs before the legacy plugin because it compiles away module syntax. ideally we have a
    // generic API for this, but we need to design that a bit more first
    const indexOfLegacy = cfg.plugins.findIndex(p => p.name === 'legacy');
    let indexOfResolve = cfg.plugins.findIndex(p => p.name === 'resolve-module-imports');
    if (indexOfLegacy !== -1 && indexOfResolve !== -1) {
        const legacy = cfg.plugins.splice(indexOfLegacy, 1)[0];
        // recompute after splicing
        indexOfResolve = cfg.plugins.findIndex(p => p.name === 'resolve-module-imports');
        cfg.plugins.splice(indexOfResolve, 1, cfg.plugins[indexOfResolve], legacy);
    }
    const middleware = (0, createMiddleware_js_1.createMiddleware)(cfg, logger, fileWatcher);
    for (const m of middleware) {
        app.use(m);
    }
    if (middlewareMode) {
        return { app };
    }
    let server;
    if (cfg.http2) {
        const dir = path_1.default.join(__dirname, '..');
        const options = {
            key: fs_1.default.readFileSync(cfg.sslKey
                ? path_1.default.resolve(cfg.sslKey)
                : path_1.default.join(dir, '..', '.self-signed-dev-server-ssl.key')),
            cert: fs_1.default.readFileSync(cfg.sslCert
                ? path_1.default.resolve(cfg.sslCert)
                : path_1.default.join(dir, '..', '.self-signed-dev-server-ssl.cert')),
            allowHTTP1: true,
            maxSessionMemory: 20,
        };
        const httpsRedirectServer = http_1.default.createServer(httpsRedirect);
        server = http2_1.default.createSecureServer(options, app.callback());
        let appServerPort;
        let httpsRedirectServerPort;
        /**
         * A connection handler that checks if the connection is using TLS
         */
        const httpRedirectProxy = (socket) => {
            socket.once('data', buffer => {
                // A TLS handshake record starts with byte 22.
                const address = buffer[0] === 22 ? appServerPort : httpsRedirectServerPort;
                const proxy = net_1.default.createConnection(address, () => {
                    proxy.write(buffer);
                    socket.pipe(proxy).pipe(socket);
                });
            });
        };
        const wrapperServer = net_1.default.createServer(httpRedirectProxy);
        wrapperServer.addListener('close', () => {
            httpsRedirectServer.close();
            server.close();
        });
        wrapperServer.addListener('listening', () => {
            const info = server.address();
            if (!info || typeof info === 'string') {
                return;
            }
            const { address, port } = info;
            appServerPort = port + 1;
            httpsRedirectServerPort = port + 2;
            server.listen({ address, port: appServerPort });
            httpsRedirectServer.listen({ address, port: httpsRedirectServerPort });
        });
        const serverListen = wrapperServer.listen.bind(wrapperServer);
        wrapperServer.listen = (config, callback) => {
            server.addListener('listening', callback);
            serverListen(config);
            return server;
        };
    }
    else {
        server = http_1.default.createServer(app.callback());
    }
    return {
        server,
        app,
    };
}
exports.createServer = createServer;
//# sourceMappingURL=createServer.js.map