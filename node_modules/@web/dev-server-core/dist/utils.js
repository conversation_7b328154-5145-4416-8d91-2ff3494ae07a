"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.resolvePathOutsideRootDir = exports.isOutsideRootDir = exports.getRequestFilePath = exports.getRequestBrowserPath = exports.isInlineScriptRequest = exports.getResponseBody = exports.RequestCancelledError = exports.getHtmlPath = exports.toFilePath = exports.toBrowserPath = void 0;
const is_stream_1 = __importDefault(require("is-stream"));
const get_stream_1 = __importDefault(require("get-stream"));
const isbinaryfile_1 = require("isbinaryfile");
const path_1 = __importDefault(require("path"));
const OUTSIDE_ROOT_KEY = '/__wds-outside-root__/';
/**
 * Turns a file path into a path suitable for browsers, with a / as seperator.
 * @param {string} filePath
 * @returns {string}
 */
function toBrowserPath(filePath) {
    return filePath.split(path_1.default.sep).join('/');
}
exports.toBrowserPath = toBrowserPath;
/**
 * Transforms a file system path to a browser URL. For example windows uses `\` on the file system,
 * but it should use `/` in the browser.
 */
function toFilePath(browserPath) {
    return browserPath.split('/').join(path_1.default.sep);
}
exports.toFilePath = toFilePath;
function getHtmlPath(path) {
    return path.endsWith('/') ? `${path}index.html` : path;
}
exports.getHtmlPath = getHtmlPath;
class RequestCancelledError extends Error {
}
exports.RequestCancelledError = RequestCancelledError;
/**
 * Returns the context body as string or buffer, depending on the content type.
 * Response streams can only be read once, the response body is replaced with
 * the stream result.
 */
async function getResponseBody(ctx) {
    let requestCanceled = false;
    ctx.req.on('close', () => {
        requestCanceled = true;
    });
    if (Buffer.isBuffer(ctx.body)) {
        const contentLength = Number(ctx.response.get('content-length'));
        const canStringify = !(await (0, isbinaryfile_1.isBinaryFile)(ctx.body, contentLength));
        if (requestCanceled) {
            throw new RequestCancelledError();
        }
        if (canStringify) {
            ctx.body = ctx.body.toString();
        }
        return ctx.body;
    }
    if (typeof ctx.body === 'string') {
        return ctx.body;
    }
    if ((0, is_stream_1.default)(ctx.body)) {
        // a stream can only be read once, so after reading it assign
        // the string response to the body so that it can be accessed
        // again later
        try {
            const bodyBuffer = await get_stream_1.default.buffer(ctx.body);
            ctx.body = bodyBuffer;
            // recursive call will stringify the buffer
            return getResponseBody(ctx);
        }
        catch (error) {
            if (error instanceof RequestCancelledError) {
                throw error;
            }
            if (requestCanceled) {
                throw new RequestCancelledError();
            }
            throw error;
        }
    }
    return ctx.body;
}
exports.getResponseBody = getResponseBody;
function isInlineScriptRequest(contextOrUrl) {
    const url = typeof contextOrUrl === 'string' ? contextOrUrl : contextOrUrl.url;
    return url.includes(`inline-script-`) && url.includes('source');
}
exports.isInlineScriptRequest = isInlineScriptRequest;
function getRequestBrowserPath(url) {
    const urlObj = new URL(url, 'http://localhost:8000/');
    let requestPath;
    // inline module requests have the source in a query string
    if (isInlineScriptRequest(url)) {
        requestPath = urlObj.searchParams.get('source');
    }
    else {
        requestPath = urlObj.pathname;
    }
    if (requestPath.endsWith('/')) {
        return `${requestPath}index.html`;
    }
    return requestPath;
}
exports.getRequestBrowserPath = getRequestBrowserPath;
function getRequestFilePath(contextOrString, rootDir) {
    const url = typeof contextOrString === 'string' ? contextOrString : contextOrString.url;
    const requestPath = getRequestBrowserPath(url);
    if (isOutsideRootDir(requestPath)) {
        const { normalizedPath, newRootDir } = resolvePathOutsideRootDir(requestPath, rootDir);
        const filePath = toFilePath(normalizedPath);
        return path_1.default.join(newRootDir, filePath);
    }
    else {
        const filePath = toFilePath(requestPath);
        return path_1.default.join(rootDir, filePath);
    }
}
exports.getRequestFilePath = getRequestFilePath;
function isOutsideRootDir(browserPath) {
    return browserPath.startsWith(OUTSIDE_ROOT_KEY);
}
exports.isOutsideRootDir = isOutsideRootDir;
function resolvePathOutsideRootDir(browserPath, rootDir) {
    const [, , depthString] = browserPath.split('/');
    const depth = Number(depthString);
    if (depth == null || Number.isNaN(depth)) {
        throw new Error(`Invalid wds-root-dir path: ${path_1.default}`);
    }
    const normalizedPath = browserPath.replace(`${OUTSIDE_ROOT_KEY}${depth}`, '');
    const newRootDir = path_1.default.resolve(rootDir, `..${path_1.default.sep}`.repeat(depth));
    return { normalizedPath, newRootDir };
}
exports.resolvePathOutsideRootDir = resolvePathOutsideRootDir;
//# sourceMappingURL=utils.js.map