{"version": 3, "file": "util.js", "sourceRoot": "", "sources": ["../../src/dom5/util.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;GAYG;;;AAEH,uDAAiD;AACjD,mDAMyB;AACzB,6CAA2C;AAE3C;;;;GAIG;AACH,SAAgB,cAAc,CAAC,IAAS;IACtC,IAAI,IAAA,6BAAa,EAAC,IAAI,CAAC,EAAE;QACvB,OAAO,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC;KACxB;IACD,IAAI,IAAA,0BAAU,EAAC,IAAI,CAAC,EAAE;QACpB,OAAO,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;KACzB;IACD,MAAM,OAAO,GAAG,IAAA,wBAAW,EAAC,IAAI,EAAE,0BAAU,CAAC,CAAC;IAC9C,OAAO,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAC9C,CAAC;AATD,wCASC;AAED;;GAEG;AACH,SAAgB,YAAY,CAAC,OAAY,EAAE,IAAY;IACrD,MAAM,CAAC,GAAG,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IAC3C,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE;QACV,OAAO,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;KAC/B;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAND,oCAMC;AAED,SAAgB,iBAAiB,CAAC,OAAY,EAAE,IAAY;IAC1D,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;QAClB,OAAO,CAAC,CAAC,CAAC;KACX;IACD,MAAM,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;IAC7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QAC7C,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,CAAC,EAAE;YAC7C,OAAO,CAAC,CAAC;SACV;KACF;IACD,OAAO,CAAC,CAAC,CAAC;AACZ,CAAC;AAXD,8CAWC;AAED;;GAEG;AACH,SAAgB,YAAY,CAAC,OAAY,EAAE,IAAY;IACrD,OAAO,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AACjD,CAAC;AAFD,oCAEC;AAED,SAAgB,YAAY,CAAC,OAAY,EAAE,IAAY,EAAE,KAAa;IACpE,MAAM,CAAC,GAAG,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IAC3C,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE;QACV,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC;KAChC;SAAM;QACL,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;KAClD;AACH,CAAC;AAPD,oCAOC;AAED,SAAgB,eAAe,CAAC,OAAY,EAAE,IAAY;IACxD,MAAM,CAAC,GAAG,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IAC3C,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE;QACV,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;KAC5B;AACH,CAAC;AALD,0CAKC;AAED,SAAS,iBAAiB,CAAC,MAAW,EAAE,KAAa,EAAE,GAAW;IAChE,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE;QACtB,OAAO;KACR;IACD,IAAI,IAAI,GAAG,EAAE,CAAC;IACd,KAAK,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,EAAE,EAAE;QACjC,IAAI,IAAI,cAAc,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;KAC9C;IACD,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,EAAE,GAAG,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC;IACjD,IAAI,IAAI,EAAE;QACR,MAAM,EAAE,GAAG,8BAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnC,EAAE,CAAC,UAAU,GAAG,MAAM,CAAC;QACvB,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;KACxC;AACH,CAAC;AAED;;;;;GAKG;AACH,SAAgB,SAAS,CAAC,IAAS;IACjC,IAAI,CAAC,CAAC,IAAA,yBAAS,EAAC,IAAI,CAAC,IAAI,IAAA,0BAAU,EAAC,IAAI,CAAC,IAAI,IAAA,kCAAkB,EAAC,IAAI,CAAC,CAAC,EAAE;QACtE,OAAO;KACR;IACD,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;QACpB,OAAO;KACR;IACD,IAAI,cAAc,GAAG,CAAC,CAAC,CAAC;IACxB,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;QAC5D,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QACvB,IAAI,IAAA,0BAAU,EAAC,CAAC,CAAC,EAAE;YACjB,IAAI,cAAc,KAAK,CAAC,CAAC,EAAE;gBACzB,cAAc,GAAG,CAAC,CAAC;aACpB;YACD,IAAI,CAAC,KAAK,CAAC,EAAE;gBACX,8BAA8B;gBAC9B,iBAAiB,CAAC,IAAI,EAAE,CAAC,EAAE,cAAc,CAAC,CAAC;aAC5C;SACF;aAAM;YACL,UAAU;YACV,SAAS,CAAC,CAAC,CAAC,CAAC;YACb,qCAAqC;YACrC,IAAI,cAAc,GAAG,CAAC,CAAC,EAAE;gBACvB,iBAAiB,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,cAAc,CAAC,CAAC;gBAC/C,cAAc,GAAG,CAAC,CAAC,CAAC;aACrB;SACF;KACF;AACH,CAAC;AA5BD,8BA4BC;AAED;;;;GAIG;AACH,SAAgB,cAAc,CAAC,IAAS,EAAE,KAAa;IACrD,IAAI,IAAA,6BAAa,EAAC,IAAI,CAAC,EAAE;QACvB,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;KACnB;SAAM,IAAI,IAAA,0BAAU,EAAC,IAAI,CAAC,EAAE;QAC3B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;KACpB;SAAM;QACL,MAAM,EAAE,GAAG,8BAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACpC,EAAE,CAAC,UAAU,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC,CAAC;KACxB;AACH,CAAC;AAVD,wCAUC;AAIM,MAAM,iBAAiB,GAAG,SAAS,iBAAiB,CAAC,IAAS;IACnE,OAAO,IAAI,CAAC,UAAU,CAAC;AACzB,CAAC,CAAC;AAFW,QAAA,iBAAiB,qBAE5B"}