{"version": 3, "file": "iteration.js", "sourceRoot": "", "sources": ["../../src/dom5/iteration.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;GAYG;;;AAEH,mDAAwE;AACxE,uCAA6D;AAE7D;;;GAGG;AACH,QAAe,CAAC,CAAC,OAAO,CACtB,IAAS,EACT,KAAiC,EACjC,aAA6B;IAE7B,KAAK,MAAM,KAAK,IAAI,UAAU,CAAC,IAAI,EAAE,aAAa,CAAC,EAAE;QACnD,KAAK,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;KACrB;AACH,CAAC;AARD,0BAQC;AAED;;;;GAIG;AACH,QAAe,CAAC,CAAC,UAAU,CACzB,IAAS,EACT,gBAA+B,2BAAiB;IAEhD,MAAM,IAAI,CAAC;IACX,MAAM,UAAU,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC;IACvC,IAAI,UAAU,KAAK,SAAS,EAAE;QAC5B,OAAO;KACR;IACD,KAAK,MAAM,KAAK,IAAI,UAAU,EAAE;QAC9B,KAAK,CAAC,CAAC,UAAU,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC;KACzC;AACH,CAAC;AAZD,gCAYC;AAED;;;;;GAKG;AACH,QAAe,CAAC,CAAC,kBAAkB,CACjC,IAAS,EACT,gBAA+B,2BAAiB;IAEhD,MAAM,UAAU,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC;IACvC,IAAI,UAAU,KAAK,SAAS,EAAE;QAC5B,KAAK,MAAM,KAAK,IAAI,YAAY,CAAC,UAAU,CAAC,EAAE;YAC5C,KAAK,CAAC,CAAC,kBAAkB,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC;SACjD;KACF;IACD,MAAM,IAAI,CAAC;AACb,CAAC;AAXD,gDAWC;AAED;;GAEG;AACH,QAAe,CAAC,CAAC,SAAS,CAAC,IAAS;IAClC,IAAI,QAAQ,GAAoB,IAAI,CAAC;IACrC,OAAO,QAAQ,KAAK,SAAS,EAAE;QAC7B,MAAM,QAAQ,CAAC;QACf,QAAQ,GAAG,QAAQ,CAAC,UAAU,CAAC;KAChC;AACH,CAAC;AAND,8BAMC;AAED;;;;;;GAMG;AACH,QAAe,CAAC,CAAC,gBAAgB,CAAC,IAAS;IACzC,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC;IAC/B,IAAI,MAAM,KAAK,SAAS,EAAE;QACxB,OAAO;KACR;IACD,MAAM,QAAQ,GAAG,MAAM,CAAC,UAAU,CAAC;IACnC,IAAI,QAAQ,KAAK,SAAS,EAAE;QAC1B,MAAM,IAAI,KAAK,CAAC,yDAAyD,CAAC,CAAC;KAC5E;IACD,MAAM,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IACrC,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;QAChB,MAAM,IAAI,KAAK,CAAC,4DAA4D,CAAC,CAAC;KAC/E;IACD,KAAK,CAAC,CAAC,YAAY,CAAC,QAAQ,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;AAC3C,CAAC;AAdD,4CAcC;AAED,oEAAoE;AACpE,QAAQ,CAAC,CAAC,YAAY,CAAI,GAAQ,EAAE,YAAY,GAAG,GAAG,CAAC,MAAM,GAAG,CAAC;IAC/D,KAAK,IAAI,KAAK,GAAG,YAAY,EAAE,KAAK,IAAI,CAAC,EAAE,KAAK,EAAE,EAAE;QAClD,MAAM,GAAG,CAAC,KAAK,CAAC,CAAC;KAClB;AACH,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;GAsBG;AACH,QAAe,CAAC,CAAC,KAAK,CAAC,IAAS;IAC9B,KAAK,MAAM,eAAe,IAAI,gBAAgB,CAAC,IAAI,CAAC,EAAE;QACpD,KAAK,CAAC,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;KAC5C;IACD,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC;IAC/B,IAAI,MAAM,EAAE;QACV,MAAM,MAAM,CAAC;QACb,KAAK,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;KACtB;AACH,CAAC;AATD,sBASC;AAED;;GAEG;AACH,SAAgB,KAAK,CACnB,IAAS,EACT,SAAoB,EACpB,gBAA+B,2BAAiB;IAEhD,KAAK,MAAM,MAAM,IAAI,QAAQ,CAAC,IAAI,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE;QAC7D,OAAO,MAAM,CAAC;KACf;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AATD,sBASC;AAED;;;GAGG;AACH,QAAe,CAAC,CAAC,QAAQ,CACvB,IAAS,EACT,SAAoB,EACpB,gBAA+B,2BAAiB;IAEhD,MAAM,gBAAgB,GAAG,0BAAC,CAAC,GAAG,CAAC,yBAAS,EAAE,SAAS,CAAC,CAAC;IACrD,KAAK,MAAM,IAAI,IAAI,UAAU,CAAC,IAAI,EAAE,aAAa,CAAC,EAAE;QAClD,IAAI,gBAAgB,CAAC,IAAI,CAAC,EAAE;YAC1B,MAAM,IAAI,CAAC;SACZ;KACF;AACH,CAAC;AAXD,4BAWC"}