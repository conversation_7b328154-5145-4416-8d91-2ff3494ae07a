{"version": 3, "file": "predicates.js", "sourceRoot": "", "sources": ["../../src/dom5/predicates.ts"], "names": [], "mappings": ";AAAA,oBAAoB;AACpB;;;;;;;;;;;;GAYG;;;AAEH,uCAA4E;AAE5E;;;;;GAKG;AACH,SAAS,YAAY,CAAC,KAAa;IACjC,OAAO,UAAU,IAAI;QACnB,OAAO,IAAA,wBAAc,EAAC,IAAI,CAAC,KAAK,KAAK,CAAC;IACxC,CAAC,CAAC;AACJ,CAAC;AAQD,SAAS,EAAE,EAAC,cAAc;IACxB,MAAM,KAAK,GAAG,IAAI,KAAK,CAAY,SAAS,CAAC,MAAM,CAAC,CAAC;IACrD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACzC,KAAK,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;KACzB;IACD,OAAO,UAAU,IAAI;QACnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACrC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;gBAClB,OAAO,IAAI,CAAC;aACb;SACF;QACD,OAAO,KAAK,CAAC;IACf,CAAC,CAAC;AACJ,CAAC;AAMD,SAAS,GAAG,EAAC,cAAc;IACzB,MAAM,KAAK,GAAG,IAAI,KAAK,CAAY,SAAS,CAAC,MAAM,CAAC,CAAC;IACrD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACzC,KAAK,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;KACzB;IACD,OAAO,UAAU,IAAI;QACnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACrC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;gBACnB,OAAO,KAAK,CAAC;aACd;SACF;QACD,OAAO,IAAI,CAAC;IACd,CAAC,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAS,GAAG,CAAC,WAAsB;IACjC,OAAO,UAAU,IAAI;QACnB,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IAC5B,CAAC,CAAC;AACJ,CAAC;AAED;;;GAGG;AACH,SAAS,aAAa,CAAC,WAAsB;IAC3C,OAAO,UAAU,IAAI;QACnB,IAAI,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC;QAC7B,OAAO,MAAM,KAAK,SAAS,EAAE;YAC3B,IAAI,WAAW,CAAC,MAAM,CAAC,EAAE;gBACvB,OAAO,IAAI,CAAC;aACb;YACD,MAAM,GAAG,MAAM,CAAC,UAAU,CAAC;SAC5B;QACD,OAAO,KAAK,CAAC;IACf,CAAC,CAAC;AACJ,CAAC;AAED,SAAS,OAAO,CAAC,IAAY;IAC3B,OAAO,UAAU,IAAI;QACnB,OAAO,IAAA,2BAAiB,EAAC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;IAC5C,CAAC,CAAC;AACJ,CAAC;AAED,SAAS,YAAY,CAAC,IAAY,EAAE,KAAa;IAC/C,OAAO,UAAU,IAAI;QACnB,OAAO,IAAA,sBAAY,EAAC,IAAI,EAAE,IAAI,CAAC,KAAK,KAAK,CAAC;IAC5C,CAAC,CAAC;AACJ,CAAC;AAED,SAAS,QAAQ,CAAC,IAAY;IAC5B,OAAO,0BAA0B,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;AACnD,CAAC;AAED,SAAS,UAAU,CAAC,IAAY;IAC9B,MAAM,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;IAC7B,OAAO,UAAU,IAAI;QACnB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjB,OAAO,KAAK,CAAC;SACd;QACD,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;IAC1C,CAAC,CAAC;AACJ,CAAC;AAED;;;;GAIG;AACH,SAAS,kBAAkB,CAAC,KAAa;IACvC,OAAO,UAAU,IAAI;QACnB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjB,OAAO,KAAK,CAAC;SACd;QACD,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC;IAChD,CAAC,CAAC;AACJ,CAAC;AAED,SAAgB,0BAA0B,CAAC,IAAY,EAAE,KAAa;IACpE,OAAO,UAAU,OAAY;QAC3B,MAAM,cAAc,GAAG,IAAA,sBAAY,EAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QACnD,IAAI,OAAO,cAAc,KAAK,QAAQ,EAAE;YACtC,OAAO,KAAK,CAAC;SACd;QACD,OAAO,cAAc,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;IACzD,CAAC,CAAC;AACJ,CAAC;AARD,gEAQC;AAED,SAAgB,UAAU,CAAC,IAAS;IAClC,OAAO,IAAI,CAAC,QAAQ,KAAK,WAAW,CAAC;AACvC,CAAC;AAFD,gCAEC;AAED,SAAgB,kBAAkB,CAAC,IAAS;IAC1C,OAAO,IAAI,CAAC,QAAQ,KAAK,oBAAoB,CAAC;AAChD,CAAC;AAFD,gDAEC;AAED,SAAgB,SAAS,CAAC,IAAS;IACjC,OAAO,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,OAAO,CAAC;AACxC,CAAC;AAFD,8BAEC;AAED,SAAgB,UAAU,CAAC,IAAS;IAClC,OAAO,IAAI,CAAC,QAAQ,KAAK,OAAO,CAAC;AACnC,CAAC;AAFD,gCAEC;AAED,SAAgB,aAAa,CAAC,IAAS;IACrC,OAAO,IAAI,CAAC,QAAQ,KAAK,UAAU,CAAC;AACtC,CAAC;AAFD,sCAEC;AAEY,QAAA,UAAU,GAAG;IACxB,QAAQ,EAAE,QAAQ;IAClB,OAAO,EAAE,OAAO;IAChB,YAAY,EAAE,YAAY;IAC1B,kBAAkB,EAAE,kBAAkB;IACtC,0BAA0B,EAAE,0BAA0B;IACtD,UAAU,EAAE,UAAU;IACtB,YAAY,EAAE,YAAY;IAC1B,GAAG,EAAE,GAAG;IACR,EAAE,EAAE,EAAE;IACN,GAAG,EAAE,GAAG;IACR,aAAa,EAAE,aAAa;CAC7B,CAAC"}