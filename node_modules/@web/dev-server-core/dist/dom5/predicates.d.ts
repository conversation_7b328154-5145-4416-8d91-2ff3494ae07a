/**
 * @license
 * Copyright (c) 2018 The Polymer Project Authors. All rights reserved.
 * This code may only be used under the BSD style license found at
 * http://polymer.github.io/LICENSE.txt
 * The complete set of authors may be found at
 * http://polymer.github.io/AUTHORS.txt
 * The complete set of contributors may be found at
 * http://polymer.github.io/CONTRIBUTORS.txt
 * Code distributed by Google as part of the polymer project is also
 * subject to an additional IP rights grant found at
 * http://polymer.github.io/PATENTS.txt
 */
/**
 * Match the text inside an element, textnode, or comment
 *
 * Note: nodeWalkAll with hasTextValue may return an textnode and its parent if
 * the textnode is the only child in that parent.
 */
declare function hasTextValue(value: string): Predicate;
export type Predicate = (node: any) => boolean;
/**
 * OR an array of predicates
 */
declare function OR(...predicates: Predicate[]): Predicate;
/**
 * AND an array of predicates
 */
declare function AND(...predicates: Predicate[]): Predicate;
/**
 * negate an individual predicate, or a group with AND or OR
 */
declare function NOT(predicateFn: Predicate): Predicate;
/**
 * Returns a predicate that matches any node with a parent matching
 * `predicateFn`.
 */
declare function parentMatches(predicateFn: Predicate): Predicate;
declare function hasAttr(attr: string): Predicate;
declare function hasAttrValue(attr: string, value: string): Predicate;
declare function hasClass(name: string): Predicate;
declare function hasTagName(name: string): Predicate;
/**
 * Returns true if `regex.match(tagName)` finds a match.
 *
 * This will use the lowercased tagName for comparison.
 */
declare function hasMatchingTagName(regex: RegExp): Predicate;
export declare function hasSpaceSeparatedAttrValue(name: string, value: string): Predicate;
export declare function isDocument(node: any): boolean;
export declare function isDocumentFragment(node: any): boolean;
export declare function isElement(node: any): boolean;
export declare function isTextNode(node: any): boolean;
export declare function isCommentNode(node: any): boolean;
export declare const predicates: {
    hasClass: typeof hasClass;
    hasAttr: typeof hasAttr;
    hasAttrValue: typeof hasAttrValue;
    hasMatchingTagName: typeof hasMatchingTagName;
    hasSpaceSeparatedAttrValue: typeof hasSpaceSeparatedAttrValue;
    hasTagName: typeof hasTagName;
    hasTextValue: typeof hasTextValue;
    AND: typeof AND;
    OR: typeof OR;
    NOT: typeof NOT;
    parentMatches: typeof parentMatches;
};
export {};
//# sourceMappingURL=predicates.d.ts.map