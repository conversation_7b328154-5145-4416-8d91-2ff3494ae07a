"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebSocketsManager = exports.NAME_WEB_SOCKET_API = exports.NAME_WEB_SOCKET_IMPORT = void 0;
const ws_1 = __importDefault(require("ws"));
const EventEmitter_js_1 = require("./EventEmitter.js");
exports.NAME_WEB_SOCKET_IMPORT = '/__web-dev-server__web-socket.js';
exports.NAME_WEB_SOCKET_API = 'wds';
/**
 * Manages web sockets. When the browser opens a web socket connection, the socket is stored
 * until it is disconnected. The dev server or plugins can then send messages to the browser.
 */
class WebSocketsManager extends EventEmitter_js_1.EventEmitter {
    constructor(server) {
        super();
        this.webSocketImport = exports.NAME_WEB_SOCKET_IMPORT;
        this.openSockets = new Set();
        this.webSocketServer = new ws_1.default.Server({
            noServer: true,
            path: `/${exports.NAME_WEB_SOCKET_API}`,
        });
        this.webSocketServer.on('connection', webSocket => {
            this.openSockets.add(webSocket);
            webSocket.on('close', () => {
                this.openSockets.delete(webSocket);
            });
            webSocket.on('message', rawData => {
                try {
                    const data = JSON.parse(rawData.toString());
                    if (!data.type) {
                        throw new Error('Missing property "type".');
                    }
                    this.emit('message', { webSocket, data });
                }
                catch (error) {
                    console.error('Failed to parse websocket event received from the browser: ', rawData);
                    console.error(error);
                }
            });
        });
        server.on('upgrade', (request, socket, head) => {
            if (request.url === this.webSocketServer.options.path) {
                this.webSocketServer.handleUpgrade(request, socket, head, ws => {
                    this.webSocketServer.emit('connection', ws, request);
                });
            }
        });
    }
    /**
     * Imports the given path, executing the module as well as a default export if it exports a function.
     *
     * This is a built-in web socket message and will be handled automatically.
     *
     * @param importPath the path to import
     * @param args optional args to pass to the function that is called.
     */
    sendImport(importPath, args = []) {
        this.send(JSON.stringify({ type: 'import', data: { importPath, args } }));
    }
    /**
     * Logs a message to the browser console of all connected web sockets.
     *
     * This is a built-in web socket message and will be handled automatically.
     *
     * @param text message to send
     */
    sendConsoleLog(text) {
        this.sendImport(`data:text/javascript,console.log(${JSON.stringify(text)});`);
    }
    /**
     * Sends messages to all connected web sockets.
     *
     * @param message
     */
    send(message) {
        for (const socket of this.openSockets) {
            if (socket.readyState === socket.OPEN) {
                socket.send(message);
            }
        }
    }
}
exports.WebSocketsManager = WebSocketsManager;
//# sourceMappingURL=WebSocketsManager.js.map