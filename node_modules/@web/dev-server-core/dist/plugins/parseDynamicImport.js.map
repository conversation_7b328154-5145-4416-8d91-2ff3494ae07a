{"version": 3, "file": "parseDynamicImport.js", "sourceRoot": "", "sources": ["../../src/plugins/parseDynamicImport.ts"], "names": [], "mappings": ";;;AAAA,MAAM,iBAAiB,GAAG,IAAI,CAAC;AAE/B,SAAS,2BAA2B,CAAC,GAAW,EAAE,SAAiB;IACjE,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC;IACf,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC;IAEb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;QACtC,MAAM,YAAY,GAAG,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACpD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YAChB,IAAI,CAAC,YAAY,EAAE;gBACjB,KAAK,GAAG,CAAC,CAAC;aACX;SACF;QAED,IAAI,YAAY,EAAE;YAChB,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE;gBACd,+DAA+D;gBAC/D,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;aACb;SACF;aAAM,IAAI,CAAC,KAAK,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE;YAC/B,kCAAkC;YAClC,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE;gBACd,GAAG,GAAG,CAAC,CAAC;aACT;SACF;aAAM;YACL,uCAAuC;YACvC,GAAG,GAAG,CAAC,CAAC,CAAC;SACV;KACF;IAED,OAAO,EAAE,YAAY,EAAE,SAAS,GAAG,KAAK,EAAE,UAAU,EAAE,SAAS,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC;AAC9E,CAAC;AAED,SAAgB,kBAAkB,CAAC,IAAY,EAAE,KAAa,EAAE,GAAW;IACzE,MAAM,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IACpD,MAAM,EAAE,YAAY,EAAE,UAAU,EAAE,GAAG,2BAA2B,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;IAE1F,MAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;IAC9D,MAAM,eAAe,GAAG,YAAY,CAAC,SAAS,CAAC,CAAC,EAAE,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAC3E,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC;IACvC,MAAM,aAAa,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;IAC7D,MAAM,kBAAkB,GACtB,WAAW,KAAK,IAAI,IAAI,eAAe,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,eAAe,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;IAEzF,OAAO;QACL,YAAY;QACZ,eAAe,EAAE,aAAa,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,YAAY;QAC/D,aAAa;QACb,kBAAkB;QAClB,YAAY;QACZ,UAAU;KACX,CAAC;AACJ,CAAC;AAnBD,gDAmBC"}