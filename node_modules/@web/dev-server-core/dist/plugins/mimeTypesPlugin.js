"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.mimeTypesPlugin = void 0;
const picomatch_1 = __importDefault(require("picomatch"));
const path_1 = require("path");
const utils_js_1 = require("../utils.js");
function createMatcher(rootDir, pattern) {
    const resolvedPattern = !(0, path_1.isAbsolute)(pattern) && !pattern.startsWith('*') ? path_1.posix.join(rootDir, pattern) : pattern;
    return (0, picomatch_1.default)(resolvedPattern, { dot: true });
}
function mimeTypesPlugin(mappings) {
    const matchers = [];
    let rootDir;
    return {
        name: 'mime-types',
        serverStart({ config }) {
            ({ rootDir } = config);
            const matcherBaseDir = config.rootDir.split(path_1.sep).join('/');
            for (const [pattern, mimeType] of Object.entries(mappings)) {
                matchers.push({ fn: createMatcher(matcherBaseDir, pattern), mimeType });
            }
        },
        resolveMimeType(context) {
            const filePath = (0, utils_js_1.getRequestFilePath)(context.url, rootDir);
            for (const matcher of matchers) {
                if (matcher.fn(filePath)) {
                    return matcher.mimeType;
                }
            }
        },
    };
}
exports.mimeTypesPlugin = mimeTypesPlugin;
//# sourceMappingURL=mimeTypesPlugin.js.map