{"version": 3, "file": "transformModuleImportsPlugin.js", "sourceRoot": "", "sources": ["../../src/plugins/transformModuleImportsPlugin.ts"], "names": [], "mappings": ";;;;;;AAAA,sDAAsD;AACtD,gDAAwB;AAExB,aAAa;AACb,qDAAsD;AAEtD,+CAAwF;AACxF,mCAAwE;AAExE,yEAAmE;AACnE,0CAAyC;AAEzC,mEAA6D;AAkB7D,MAAM,uBAAuB,GAC3B,wFAAwF,CAAC;AAE3F;;;;;GAKG;AACH,KAAK,UAAU,yBAAyB,CACtC,eAAuB,EACvB,aAA4B,EAC5B,IAAY,EACZ,IAAY,EACZ,MAAc;IAEd,IAAI,aAAa,GAAG,eAAe,CAAC;IACpC,IAAI,YAAY,GAAG,EAAE,CAAC;IAEtB,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE;QAC7D,gCAAgC;QAChC,OAAO,aAAa,CAAC;KACtB;IAED,MAAM,KAAK,GAAG,eAAe,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACzC,IAAI,eAAe,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;QACnC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;YACpB,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;SAC1C;QAED,aAAa,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;QAC1C,YAAY,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;KACvD;SAAM;QACL,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;YACpB,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;SAC1C;QAED,CAAC,aAAa,CAAC,GAAG,KAAK,CAAC;QACxB,YAAY,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;KACvD;IAED,oGAAoG;IACpG,sDAAsD;IACtD,MAAM,WAAW,GAAG,GAAG,aAAa,eAAe,CAAC;IACpD,MAAM,eAAe,GAAG,MAAM,aAAa,CAAC,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;IAC7E,IAAI,CAAC,eAAe,EAAE;QACpB,MAAM,IAAI,KAAK,CAAC,gEAAgE,WAAW,EAAE,CAAC,CAAC;KAChG;IAED,MAAM,UAAU,GAAG,eAAe,CAAC,SAAS,CAAC,CAAC,EAAE,eAAe,CAAC,MAAM,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC;IAChG,OAAO,GAAG,UAAU,GAAG,YAAY,EAAE,CAAC;AACxC,CAAC;AAED,KAAK,UAAU,kBAAkB,CAC/B,eAAuB,EACvB,kBAA2B,EAC3B,aAA4B,EAC5B,IAAY,EACZ,IAAY,EACZ,MAAc;;IAEd,IAAI,sBAAsB,CAAC;IAE3B,IAAI,kBAAkB,EAAE;QACtB,oIAAoI;QACpI,IAAI;YACF,sBAAsB;gBACpB,MAAA,CAAC,MAAM,yBAAyB,CAAC,eAAe,EAAE,aAAa,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC,mCACrF,eAAe,CAAC;SACnB;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,eAAe,CAAC;SACxB;KACF;SAAM;QACL,sBAAsB;YACpB,MAAA,CAAC,MAAM,aAAa,CAAC,eAAe,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC,mCAAI,eAAe,CAAC;KACjF;IACD,OAAO,sBAAsB,CAAC;AAChC,CAAC;AAEM,KAAK,UAAU,gBAAgB,CACpC,IAAY,EACZ,QAAgB,EAChB,aAA4B;IAE5B,IAAI,OAAuB,CAAC;IAC5B,IAAI;QACF,MAAM,WAAW,GAAG,MAAM,IAAA,uBAAK,EAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QAChD,OAAO,GAAG,WAAW,CAAC,CAAC,CAA0B,CAAC;KACnD;IAAC,OAAO,KAAK,EAAE;QACd,IAAI,OAAQ,KAAiC,CAAC,GAAG,KAAK,QAAQ,EAAE;YAC9D,MAAM,UAAU,GAAG,KAAgC,CAAC;YACpD,MAAM,IAAI,wCAAiB,CACzB,cAAc,EACd,QAAQ,EACR,IAAI,EACJ,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,EAChD,UAAU,CAAC,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC,CAC5D,CAAC;SACH;QACD,MAAM,KAAK,CAAC;KACb;IAED,IAAI,cAAc,GAAG,EAAE,CAAC;IACxB,IAAI,SAAS,GAAG,CAAC,CAAC;IAElB,KAAK,MAAM,GAAG,IAAI,OAAO,EAAE;QACzB,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,kBAAkB,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,GAAG,CAAC;QAEtE,IAAI,kBAAkB,KAAK,CAAC,CAAC,EAAE;YAC7B,gBAAgB;YAChB,MAAM,eAAe,GAAG,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;YAChE,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC;YAC1B,MAAM,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;YAEhE,MAAM,cAAc,GAAG,MAAM,kBAAkB,CAC7C,eAAe,EACf,KAAK,EACL,aAAa,EACb,IAAI,EACJ,IAAI,EACJ,MAAM,CACP,CAAC;YAEF,cAAc,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,KAAK,CAAC,GAAG,cAAc,EAAE,CAAC;YACzE,SAAS,GAAG,GAAG,CAAC;SACjB;aAAM,IAAI,kBAAkB,IAAI,CAAC,EAAE;YAClC,iBAAiB;YACjB,MAAM,EACJ,YAAY,EACZ,eAAe,EACf,aAAa,EACb,kBAAkB,EAClB,YAAY,EACZ,UAAU,GACX,GAAG,IAAA,0CAAkB,EAAC,IAAI,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;YAEzC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACtD,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC;YAC1B,MAAM,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YAE/D,IAAI,eAAe,CAAC;YACpB,IAAI,aAAa,EAAE;gBACjB,MAAM,cAAc,GAAG,MAAM,kBAAkB,CAC7C,eAAe,EACf,kBAAkB,EAClB,aAAa,EACb,IAAI,EACJ,IAAI,EACJ,MAAM,CACP,CAAC;gBACF,eAAe,GAAG,GAAG,YAAY,CAAC,CAAC,CAAC,GAAG,cAAc,GACnD,YAAY,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CACtC,EAAE,CAAC;aACJ;iBAAM;gBACL,eAAe,GAAG,YAAY,CAAC;aAChC;YAED,cAAc,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,YAAY,CAAC,GAAG,eAAe,EAAE,CAAC;YACjF,SAAS,GAAG,UAAU,CAAC;SACxB;KACF;IAED,IAAI,SAAS,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;QAC/B,cAAc,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;KAC/D;IAED,OAAO,cAAc,CAAC;AACxB,CAAC;AAzFD,4CAyFC;AAED,KAAK,UAAU,iCAAiC,CAC9C,MAAc,EACd,OAAgB,EAChB,MAAc,EACd,OAAe,EACf,cAAwB;IAExB,MAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAA,qBAAU,EAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;IAE9D,KAAK,UAAU,aAAa,CAAC,MAAc,EAAE,IAAY,EAAE,MAAc,EAAE,IAAY;;QACrF,KAAK,MAAM,MAAM,IAAI,cAAc,EAAE;YACnC,MAAM,QAAQ,GAAG,MAAM,CAAA,MAAA,MAAM,CAAC,aAAa,uDAAG,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAA,CAAC;YACvF,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;gBAChC,MAAM,CAAC,KAAK,CACV,UAAU,MAAM,CAAC,IAAI,oBAAoB,MAAM,OAAO,OAAO,CAAC,IAAI,OAAO,QAAQ,GAAG,CACrF,CAAC;gBACF,OAAO,QAAQ,CAAC;aACjB;YAED,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;gBAChC,MAAM,CAAC,KAAK,CACV,UAAU,MAAM,CAAC,IAAI,oBAAoB,MAAM,OAAO,OAAO,CAAC,IAAI,OAAO,QAAQ,CAAC,EAAE,GAAG,CACxF,CAAC;gBACF,OAAO,QAAQ,CAAC,EAAE,CAAC;aACpB;SACF;IACH,CAAC;IAED,KAAK,UAAU,eAAe,CAAC,MAAc,EAAE,IAAY,EAAE,MAAc,EAAE,IAAY;;QACvF,IAAI,cAAc,GAAG,MAAA,CAAC,MAAM,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,mCAAI,MAAM,CAAC;QACjF,KAAK,MAAM,MAAM,IAAI,cAAc,EAAE;YACnC,MAAM,QAAQ,GAAG,MAAM,CAAA,MAAA,MAAM,CAAC,eAAe,uDAAG;gBAC9C,MAAM,EAAE,cAAc;gBACtB,OAAO;gBACP,MAAM;gBACN,IAAI;aACL,CAAC,CAAA,CAAC;YACH,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;gBAChC,MAAM,CAAC,KAAK,CACV,UAAU,MAAM,CAAC,IAAI,uBAAuB,cAAc,OAAO,OAAO,CAAC,IAAI,OAAO,QAAQ,GAAG,CAChG,CAAC;gBACF,cAAc,GAAG,QAAQ,CAAC;aAC3B;YAED,IAAI,OAAO,QAAQ,KAAK,QAAQ,IAAI,OAAO,QAAQ,CAAC,EAAE,KAAK,QAAQ,EAAE;gBACnE,MAAM,CAAC,KAAK,CACV,UAAU,MAAM,CAAC,IAAI,uBAAuB,cAAc,OAAO,OAAO,CAAC,IAAI,OAAO,QAAQ,CAAC,EAAE,GAAG,CACnG,CAAC;gBACF,cAAc,GAAG,QAAQ,CAAC,EAAE,CAAC;aAC9B;SACF;QACD,OAAO,cAAc,CAAC;IACxB,CAAC;IAED,OAAO,gBAAgB,CAAC,MAAM,EAAE,QAAQ,EAAE,eAAe,CAAC,CAAC;AAC7D,CAAC;AAED,SAAgB,4BAA4B,CAC1C,MAAc,EACd,OAAiB,EACjB,OAAe;IAEf,MAAM,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,aAAa,IAAI,CAAC,CAAC,EAAE,CAAC,eAAe,CAAC,CAAC;IAEvF,OAAO;QACL,IAAI,EAAE,wBAAwB;QAC9B,KAAK,CAAC,SAAS,CAAC,OAAO;YACrB,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC9B,OAAO;aACR;YAED,yBAAyB;YACzB,IAAI,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;gBAC7B,MAAM,uBAAuB,GAAG,MAAM,iCAAiC,CACrE,MAAM,EACN,OAAO,EACP,OAAO,CAAC,IAAc,EACtB,OAAO,EACP,aAAa,CACd,CAAC;gBACF,OAAO,EAAE,IAAI,EAAE,uBAAuB,EAAE,CAAC;aAC1C;YAED,yBAAyB;YACzB,IAAI,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,OAAO,OAAO,CAAC,IAAI,KAAK,QAAQ,EAAE;gBACnE,MAAM,WAAW,GAAG,IAAA,cAAS,EAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBAC5C,MAAM,iBAAiB,GAAG,IAAA,mBAAQ,EAChC,WAAW,EACX,qBAAU,CAAC,GAAG,CACZ,qBAAU,CAAC,UAAU,CAAC,QAAQ,CAAC,EAC/B,qBAAU,CAAC,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC,EACzC,qBAAU,CAAC,GAAG,CAAC,qBAAU,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAC1C,CACF,CAAC;gBACF,IAAI,WAAW,GAAG,KAAK,CAAC;gBAExB,KAAK,MAAM,IAAI,IAAI,iBAAiB,EAAE;oBACpC,MAAM,IAAI,GAAG,IAAA,yBAAc,EAAC,IAAI,CAAC,CAAC;oBAClC,MAAM,YAAY,GAAG,MAAM,iCAAiC,CAC1D,MAAM,EACN,OAAO,EACP,IAAI,EACJ,OAAO,EACP,aAAa,CACd,CAAC;oBACF,IAAI,IAAI,KAAK,YAAY,EAAE;wBACzB,IAAA,yBAAc,EAAC,IAAI,EAAE,YAAY,CAAC,CAAC;wBACnC,WAAW,GAAG,IAAI,CAAC;qBACpB;iBACF;gBAED,IAAI,WAAW,EAAE;oBACf,OAAO,EAAE,IAAI,EAAE,IAAA,kBAAa,EAAC,WAAW,CAAC,EAAE,CAAC;iBAC7C;aACF;QACH,CAAC;KACF,CAAC;AACJ,CAAC;AA5DD,oEA4DC"}