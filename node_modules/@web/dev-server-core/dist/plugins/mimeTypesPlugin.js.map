{"version": 3, "file": "mimeTypesPlugin.js", "sourceRoot": "", "sources": ["../../src/plugins/mimeTypesPlugin.ts"], "names": [], "mappings": ";;;;;;AAAA,0DAAkC;AAClC,+BAA8C;AAI9C,0CAAiD;AAEjD,SAAS,aAAa,CAAC,OAAe,EAAE,OAAe;IACrD,MAAM,eAAe,GACnB,CAAC,IAAA,iBAAU,EAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,YAAK,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;IAC5F,OAAO,IAAA,mBAAS,EAAC,eAAe,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC;AACnD,CAAC;AAOD,SAAgB,eAAe,CAAC,QAA0B;IACxD,MAAM,QAAQ,GAAc,EAAE,CAAC;IAC/B,IAAI,OAAe,CAAC;IAEpB,OAAO;QACL,IAAI,EAAE,YAAY;QAElB,WAAW,CAAC,EAAE,MAAM,EAAE;YACpB,CAAC,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC,CAAC;YACvB,MAAM,cAAc,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,UAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAE3D,KAAK,MAAM,CAAC,OAAO,EAAE,QAAQ,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;gBAC1D,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,aAAa,CAAC,cAAc,EAAE,OAAO,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC;aACzE;QACH,CAAC;QAED,eAAe,CAAC,OAAO;YACrB,MAAM,QAAQ,GAAG,IAAA,6BAAkB,EAAC,OAAO,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;YAC1D,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;gBAC9B,IAAI,OAAO,CAAC,EAAE,CAAC,QAAQ,CAAC,EAAE;oBACxB,OAAO,OAAO,CAAC,QAAQ,CAAC;iBACzB;aACF;QACH,CAAC;KACF,CAAC;AACJ,CAAC;AAzBD,0CAyBC"}