{"version": 3, "file": "watchPlugin.js", "sourceRoot": "", "sources": ["../../src/plugins/watchPlugin.ts"], "names": [], "mappings": ";;;;;;AACA,wDAAgC;AAEhC,SAAgB,WAAW;IACzB,OAAO;QACL,IAAI,EAAE,OAAO;QAEb,eAAe,EAAE,IAAI;QAErB,WAAW,CAAC,EAAE,WAAW,EAAE,UAAU,EAAE;YACrC,IAAI,CAAC,UAAU,EAAE;gBACf,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAC;aACzE;YAED,SAAS,aAAa;gBACpB,UAAW,CAAC,UAAU,CAAC,+CAA+C,CAAC,CAAC;YAC1E,CAAC;YAED,MAAM,QAAQ,GAAG,IAAA,kBAAQ,EAAC,aAAa,EAAE,GAAG,CAAC,CAAC;YAC9C,WAAW,CAAC,WAAW,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YAC5C,WAAW,CAAC,WAAW,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAC9C,CAAC;KACF,CAAC;AACJ,CAAC;AApBD,kCAoBC"}