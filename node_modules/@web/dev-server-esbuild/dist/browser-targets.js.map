{"version": 3, "file": "browser-targets.js", "sourceRoot": "", "sources": ["../src/browser-targets.ts"], "names": [], "mappings": ";;;AAAA,kEAAoD;AAKvC,QAAA,oBAAoB,GAAG,kBAAkB,EAAE,CAAC;AACzD,uFAAuF;AAC1E,QAAA,yBAAyB,GAAG,CAAC,UAAU,EAAE,QAAQ,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC;AAE3F,SAAS,kBAAkB;IACzB,IAAI;QACF,MAAM,YAAY,GAAG,oBAAoB,CAAC,8BAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QACpE,IAAI,CAAC,YAAY;YAAE,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;QAEjF,MAAM,UAAU,GAAG,oBAAoB,CAAC,8BAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAChE,IAAI,CAAC,UAAU;YAAE,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;QAE7E,MAAM,YAAY,GAAG,oBAAoB,CAAC,8BAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QACpE,IAAI,CAAC,YAAY;YAAE,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;QAEjF,MAAM,aAAa,GAAG,oBAAoB,CAAC,8BAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACtE,IAAI,CAAC,aAAa;YAAE,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;QAEnF,OAAO;YACL,SAAS,YAAY,GAAG,CAAC,EAAE;YAC3B,OAAO,UAAU,GAAG,CAAC,EAAE;YACvB,SAAS,YAAY,EAAE;YACvB,UAAU,aAAa,EAAE;SAC1B,CAAC;KACH;IAAC,OAAO,KAAK,EAAE;QACd,MAAM,IAAI,KAAK,CACb,iFACG,KAAe,CAAC,OACnB,EAAE,CACH,CAAC;KACH;AACH,CAAC;AAED,SAAS,eAAe,CAAC,OAAwB;IAC/C,OAAO,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAClD,CAAC;AAED,SAAgB,oBAAoB,CAAC,QAAiC;;IACpE,MAAM,OAAO,GAAG,MAAA,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,EAAE,EAAE,CAAC,OAAO,CAAC,MAAM,KAAK,SAAS,CAAC,0CAAG,CAAC,CAAC,CAAC;IAClG,IAAI,OAAO,EAAE;QACX,OAAO,eAAe,CAAC,OAAO,CAAC,CAAC;KACjC;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAND,oDAMC;AAED,SAAS,aAAa,CAAC,QAAiC,EAAE,OAAwB,EAAE,KAAa;IAC/F,MAAM,mBAAmB,GAAG,eAAe,CAAC,OAAO,CAAC,CAAC;IACrD,MAAM,kBAAkB,GAAG,oBAAoB,CAAC,QAAQ,CAAC,CAAC;IAC1D,IAAI,kBAAkB,IAAI,IAAI,EAAE;QAC9B,OAAO,KAAK,CAAC;KACd;IACD,OAAO,mBAAmB,IAAI,kBAAkB,GAAG,KAAK,CAAC;AAC3D,CAAC;AAED,SAAgB,cAAc,CAAC,EAAE,IAAI,EAAE,OAAO,EAAW;IACvD,MAAM,aAAa,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;IAEzC,iDAAiD;IACjD,IAAI,aAAa,KAAK,QAAQ,EAAE;QAC9B,OAAO,aAAa,CAAC,8BAAQ,CAAC,MAAM,CAAC,QAAQ,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;KAC5D;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AATD,wCASC;AAED,SAAgB,qBAAqB,CAAC,EAAE,IAAI,EAAE,OAAO,EAAW;IAC9D,MAAM,aAAa,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;IAEzC,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE;QACrE,OAAO,aAAa,CAAC,8BAAQ,CAAC,MAAM,CAAC,QAAQ,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;KAC5D;IAED,IAAI,aAAa,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;QAClC,OAAO,aAAa,CAAC,8BAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;KAC1D;IAED,IAAI,aAAa,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE;QACrC,OAAO,aAAa,CAAC,8BAAQ,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;KAC7D;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAhBD,sDAgBC"}