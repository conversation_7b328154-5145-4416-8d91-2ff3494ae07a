{"version": 3, "file": "EsbuildPlugin.js", "sourceRoot": "", "sources": ["../src/EsbuildPlugin.ts"], "names": [], "mappings": ";;;;;;AAAA,0DAO8B;AAE9B,qCAAqD;AACrD,+BAAiC;AACjC,gDAAwB;AACxB,4CAAoB;AACpB,yDAKwC;AACxC,mCAAwE;AAExE,+DAAyD;AAEzD,MAAM,gBAAgB,GAAG,CAAC,gCAAgC,CAAC,CAAC;AAE5D,KAAK,UAAU,UAAU,CAAC,IAAY;IACpC,IAAI;QACF,MAAM,IAAA,gBAAS,EAAC,YAAE,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC;QACjC,OAAO,IAAI,CAAC;KACb;IAAC,WAAM;QACN,OAAO,KAAK,CAAC;KACd;AACH,CAAC;AAeD,MAAa,aAAa;IAQxB,YAAY,aAA4B;QAJhC,yBAAoB,GAAa,EAAE,CAAC;QAE5C,SAAI,GAAG,SAAS,CAAC;QAGf,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;IACrC,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,EAAE,MAAM,EAAE,MAAM,EAAmD;QACnF,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;YAC/B,IAAI,CAAC,WAAW,GAAG,MAAM,IAAA,gBAAS,EAAC,YAAE,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;SACtF;IACH,CAAC;IAED,eAAe,CAAC,OAAgB;QAC9B,MAAM,aAAa,GAAG,cAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACvD,IAAI,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE;YAChE,OAAO,IAAI,CAAC;SACb;IACH,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,EAAE,MAAM,EAAE,OAAO,EAAwC;QAC3E,MAAM,aAAa,GAAG,cAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACvD,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE;YAChE,+BAA+B;YAC/B,OAAO;SACR;QAED,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;YACtD,+BAA+B;YAC/B,OAAO;SACR;QAED,+FAA+F;QAC/F,4DAA4D;QAC5D,MAAM,QAAQ,GAAG,IAAA,oCAAkB,EAAC,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,MAAO,CAAC,OAAO,CAAC,CAAC;QACvE,MAAM,OAAO,GAAG,cAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACvC,MAAM,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;QAClE,MAAM,kBAAkB,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;QAC1D,IAAI,CAAC,CAAC,MAAM,UAAU,CAAC,kBAAkB,CAAC,CAAC,EAAE;YAC3C,OAAO;SACR;QACD,OAAO,UAAU,CAAC;IACpB,CAAC;IAED,iBAAiB,CAAC,OAAgB;QAChC,gEAAgE;QAChE,MAAM,MAAM,GAAG,IAAA,sCAAgB,EAAC,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC;QAC1F,OAAO,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;IAC3D,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,OAAgB;QAC9B,IAAI,MAAc,CAAC;QAEnB,IAAI,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE;YAC/B,qCAAqC;YACrC,MAAM,GAAG,IAAI,CAAC;SACf;aAAM;YACL,MAAM,aAAa,GAAG,cAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACvD,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;SACpD;QAED,IAAI,CAAC,MAAM,EAAE;YACX,gCAAgC;YAChC,OAAO;SACR;QAED,MAAM,MAAM,GAAG,IAAA,sCAAgB,EAAC,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC;QAC1F,IAAI,MAAM,KAAK,QAAQ,IAAI,MAAM,KAAK,IAAI,EAAE;YAC1C,2GAA2G;YAC3G,OAAO;SACR;QAED,MAAM,QAAQ,GAAG,IAAA,oCAAkB,EAAC,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,MAAO,CAAC,OAAO,CAAC,CAAC;QACvE,IAAI,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE;YAC/B,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAC7C,OAAO,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;SAChE;QAED,OAAO,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,IAAc,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;IAChF,CAAC;IAEO,KAAK,CAAC,eAAe,CAC3B,OAAgB,EAChB,QAAgB,EAChB,MAAc,EACd,MAAyB;QAEzB,MAAM,WAAW,GAAG,IAAA,cAAS,EAAC,OAAO,CAAC,IAAc,CAAC,CAAC;QACtD,MAAM,aAAa,GAAG,IAAA,eAAQ,EAC5B,WAAW,EACX,iBAAU,CAAC,GAAG,CACZ,iBAAU,CAAC,UAAU,CAAC,QAAQ,CAAC,EAC/B,iBAAU,CAAC,GAAG,CAAC,iBAAU,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EACzC,iBAAU,CAAC,EAAE,CACX,iBAAU,CAAC,GAAG,CAAC,iBAAU,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,EAC1C,iBAAU,CAAC,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC,CAC1C,CACF,CACF,CAAC;QAEF,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE;YAC9B,OAAO;SACR;QAED,KAAK,MAAM,IAAI,IAAI,aAAa,EAAE;YAChC,MAAM,IAAI,GAAG,IAAA,qBAAc,EAAC,IAAI,CAAC,CAAC;YAClC,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;YACnF,IAAA,qBAAc,EAAC,IAAI,EAAE,eAAe,CAAC,CAAC;SACvC;QAED,OAAO,IAAA,kBAAa,EAAC,WAAW,CAAC,CAAC;IACpC,CAAC;IAEO,KAAK,CAAC,eAAe,CAC3B,IAAY,EACZ,QAAgB,EAChB,MAAc,EACd,MAAyB;QAEzB,IAAI;YACF,MAAM,gBAAgB,GAAqB;gBACzC,UAAU,EAAE,QAAQ;gBACpB,SAAS,EAAE,QAAQ;gBACnB,MAAM;gBACN,MAAM;gBACN,+FAA+F;gBAC/F,MAAM,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK;gBACvE,UAAU,EAAE,IAAI,CAAC,aAAa,CAAC,UAAU;gBACzC,WAAW,EAAE,IAAI,CAAC,aAAa,CAAC,WAAW;gBAC3C,MAAM,EAAE,IAAI,CAAC,aAAa,CAAC,MAAM;gBACjC,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,MAAM,EAAE,IAAI,CAAC,aAAa,CAAC,MAAM;gBACjC,MAAM,EAAE,IAAI,CAAC,aAAa,CAAC,MAAM;aAClC,CAAC;YAEF,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,QAAQ,EAAE,GAAG,MAAM,IAAA,mBAAS,EAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;YAEpF,IAAI,QAAQ,EAAE;gBACZ,MAAM,YAAY,GAAG,cAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,QAAQ,CAAC,CAAC;gBAE5D,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;oBAC9B,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE;wBACzD,IAAI,CAAC,MAAO,CAAC,IAAI,CACf,yDAAyD,YAAY,KAAK,OAAO,CAAC,IAAI,EAAE,CACzF,CAAC;qBACH;iBACF;aACF;YAED,OAAO,eAAe,CAAC;SACxB;QAAC,OAAO,CAAC,EAAE;YACV,IAAI,KAAK,CAAC,OAAO,CAAE,CAAkB,CAAC,MAAM,CAAC,EAAE;gBAC7C,MAAM,GAAG,GAAI,CAAkB,CAAC,MAAM,CAAC,CAAC,CAAY,CAAC;gBAErD,IAAI,GAAG,CAAC,QAAQ,EAAE;oBAChB,MAAM,IAAI,mCAAiB,CACzB,GAAG,CAAC,IAAI,EACR,QAAQ,EACR,IAAI,EACJ,GAAG,CAAC,QAAQ,CAAC,IAAI,EACjB,GAAG,CAAC,QAAQ,CAAC,MAAM,CACpB,CAAC;iBACH;gBAED,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;aAC3B;YAED,MAAM,CAAC,CAAC;SACT;IACH,CAAC;CACF;AAjLD,sCAiLC"}