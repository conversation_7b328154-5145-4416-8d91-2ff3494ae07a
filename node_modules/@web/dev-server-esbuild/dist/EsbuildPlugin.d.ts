import { Context, Plugin, Logger, DevServerCoreConfig } from '@web/dev-server-core';
import { Loader } from 'esbuild';
export interface EsbuildConfig {
    loaders: Record<string, Loader>;
    target: string | string[];
    handledExtensions: string[];
    tsFileExtensions: string[];
    jsxFactory?: string;
    jsxFragment?: string;
    define?: {
        [key: string]: string;
    };
    tsconfig?: string;
    banner?: string;
    footer?: string;
}
export declare class EsbuildPlugin implements Plugin {
    private config?;
    private esbuildConfig;
    private logger?;
    private transformedHtmlFiles;
    private tsconfigRaw?;
    name: string;
    constructor(esbuildConfig: EsbuildConfig);
    serverStart({ config, logger }: {
        config: DevServerCoreConfig;
        logger: Logger;
    }): Promise<void>;
    resolveMimeType(context: Context): "js" | undefined;
    resolveImport({ source, context }: {
        source: string;
        context: Context;
    }): Promise<string | undefined>;
    transformCacheKey(context: Context): string;
    transform(context: Context): Promise<string | undefined>;
    private __transformHtml;
    private __transformCode;
}
//# sourceMappingURL=EsbuildPlugin.d.ts.map