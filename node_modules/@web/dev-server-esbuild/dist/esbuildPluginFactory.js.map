{"version": 3, "file": "esbuildPluginFactory.js", "sourceRoot": "", "sources": ["../src/esbuildPluginFactory.ts"], "names": [], "mappings": ";;;AAEA,yDAAmD;AAkBnD,SAAgB,aAAa,CAAC,OAA0B,EAAE;;IACxD,MAAM,MAAM,GAAG,MAAA,IAAI,CAAC,MAAM,mCAAI,MAAM,CAAC;IACrC,MAAM,OAAO,GAA2B,EAAE,CAAC;IAC3C,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,MAAA,IAAI,CAAC,OAAO,mCAAI,EAAE,CAAC,EAAE;QAC7D,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE,CAAC,GAAG,KAAK,CAAC;KACxD;IACD,IAAI,IAAI,CAAC,EAAE,EAAE;QACX,OAAO,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;KACvB;IACD,IAAI,IAAI,CAAC,GAAG,EAAE;QACZ,OAAO,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC;KACzB;IACD,IAAI,IAAI,CAAC,GAAG,EAAE;QACZ,OAAO,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC;KACzB;IACD,IAAI,IAAI,CAAC,IAAI,EAAE;QACb,OAAO,CAAC,OAAO,CAAC,GAAG,MAAM,CAAC;KAC3B;IACD,IAAI,IAAI,CAAC,EAAE,EAAE;QACX,OAAO,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;KACvB;IACD,IACE,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC;QACrD,CAAC,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAC/D;QACA,OAAO,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;KACvB;IAED,MAAM,iBAAiB,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC/C,MAAM,gBAAgB,GAAa,EAAE,CAAC;IACtC,KAAK,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;QACzD,IAAI,MAAM,KAAK,IAAI,IAAI,MAAM,KAAK,KAAK,EAAE;YACvC,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;SAClC;KACF;IAED,OAAO,IAAI,gCAAa,CAAC;QACvB,OAAO;QACP,MAAM;QACN,iBAAiB;QACjB,gBAAgB;QAChB,UAAU,EAAE,IAAI,CAAC,UAAU;QAC3B,WAAW,EAAE,IAAI,CAAC,WAAW;QAC7B,MAAM,EAAE,IAAI,CAAC,MAAM;QACnB,QAAQ,EAAE,IAAI,CAAC,QAAQ;QACvB,MAAM,EAAE,IAAI,CAAC,MAAM;QACnB,MAAM,EAAE,IAAI,CAAC,MAAM;KACpB,CAAC,CAAC;AACL,CAAC;AAhDD,sCAgDC"}