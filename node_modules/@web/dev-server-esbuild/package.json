{"name": "@web/dev-server-esbuild", "version": "1.0.4", "publishConfig": {"access": "public"}, "description": "Plugin for using esbuild in @web/dev-server", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/modernweb-dev/web.git", "directory": "packages/dev-server-esbuild"}, "author": "modern-web", "homepage": "https://github.com/modernweb-dev/web/tree/master/packages/dev-server-esbuild", "main": "dist/index.js", "exports": {".": {"types": "./index.d.ts", "import": "./index.mjs", "require": "./dist/index.js"}}, "engines": {"node": ">=18.0.0"}, "scripts": {"build": "tsc", "start:demo:jsx": "es-dev-server --config demo/jsx/server.config.js", "start:demo:ts": "es-dev-server --config demo/ts/server.config.js", "test:node": "mocha \"test/**/*.test.ts\" --require ts-node/register --reporter dot", "test:watch": "mocha \"test/**/*.test.ts\" --require ts-node/register --watch --watch-files src,test"}, "files": ["*.d.ts", "*.js", "*.mjs", "dist", "src"], "keywords": ["web", "dev", "server", "test", "runner", "testrunner", "typescript", "jsx", "compile", "transform"], "dependencies": {"@mdn/browser-compat-data": "^4.0.0", "@web/dev-server-core": "^0.7.4", "esbuild": "^0.25.0", "parse5": "^6.0.1", "ua-parser-js": "^1.0.33"}, "devDependencies": {"@types/ua-parser-js": "^0.7.35", "@web/dev-server-rollup": "^0.6.1", "lit-element": "^3.0.0 || ^4.0.1", "preact": "^10.5.9"}}