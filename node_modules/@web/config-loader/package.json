{"name": "@web/config-loader", "version": "0.3.3", "publishConfig": {"access": "public"}, "description": "Load a esm or cjs config from the file system", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/modernweb-dev/web.git", "directory": "packages/config-loader"}, "author": "modern-web", "homepage": "https://github.com/modernweb-dev/web/tree/master/packages/config-loader", "main": "src/index.js", "type": "commonjs", "engines": {"node": ">=18.0.0"}, "scripts": {"build": "tsc", "test:node": "mocha test/**/*.test.js --reporter dot", "test:watch": "mocha test/**/*.test.js --watch --watch-files .,src,test --reporter dot"}, "files": ["*.d.ts", "*.js", "*.mjs", "dist", "src"], "keywords": ["web", "node", "config", "loader", "esm", "es module"], "dependencies": {}, "types": "dist/index.d.ts", "exports": {".": {"types": "./index.d.ts", "import": "./index.mjs", "require": "./src/index.js"}}}