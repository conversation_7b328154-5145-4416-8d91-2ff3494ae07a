{"version": 3, "file": "rollupBundlePlugin.js", "sourceRoot": "", "sources": ["../src/rollupBundlePlugin.ts"], "names": [], "mappings": ";;;;;;AACA,gDAAwB;AACxB,mCAA+C;AAM/C,KAAK,UAAU,iBAAiB,CAAC,YAA2B;IAC1D,MAAM,MAAM,GAAG,MAAM,IAAA,eAAM,EAAC,YAAY,CAAC,CAAC;IAC1C,IAAI,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE;QACtC,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;KACpD;IAED,OAAO,MAAM,CAAC,QAAQ,iCACjB,YAAY,CAAC,MAAM,KACtB,cAAc,EAAE,+BAA+B,EAC/C,cAAc,EAAE,qCAAqC,IACrD,CAAC;AACL,CAAC;AAED,SAAgB,kBAAkB,CAAC,aAAkC;IACnE,MAAM,WAAW,GAAG,IAAI,GAAG,EAAkB,CAAC;IAE9C,OAAO;QACL,IAAI,EAAE,eAAe;QAErB,KAAK,CAAC,WAAW,CAAC,EAAE,MAAM,EAAE;YAC1B,IAAK,MAAc,CAAC,KAAK,EAAE;gBACzB,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;aACvE;YAED,MAAM,MAAM,GAAG,MAAM,iBAAiB,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;YACnE,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,MAAM,EAAE;gBAChC,IAAI,gBAAwB,CAAC;gBAC7B,IAAI,OAAe,CAAC;gBAEpB,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE;oBACzB,IAAI,IAAI,CAAC,OAAO,EAAE;wBAChB,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;4BACxB,MAAM,IAAI,KAAK,CAAC,yDAAyD,CAAC,CAAC;yBAC5E;wBACD,gBAAgB,GAAG,cAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;qBACvE;yBAAM;wBACL,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC;qBAClC;oBACD,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC;iBACrB;qBAAM;oBACL,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACjC,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ,EAAE;wBACnC,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;qBACxE;oBACD,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC;iBACvB;gBAED,MAAM,WAAW,GAAG,IAAI,gBAAgB,CAAC,KAAK,CAAC,cAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;gBACrE,WAAW,CAAC,GAAG,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;aACvC;QACH,CAAC;QAED,KAAK,CAAC,OAAO;YACX,MAAM,OAAO,GAAG,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAC9C,IAAI,OAAO,EAAE;gBACX,OAAO,OAAO,CAAC;aAChB;iBAAM,IAAI,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,sBAAsB,CAAC,EAAE;gBACxD,OAAO,WAAW,CAAC,GAAG,CAAC,IAAI,cAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;aAC3D;QACH,CAAC;KACF,CAAC;AACJ,CAAC;AAhDD,gDAgDC"}