"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createRollupPluginContexts = void 0;
const rollup_1 = require("rollup");
/**
 * Runs rollup with an empty module in order to capture the plugin context and
 * normalized options.
 * @param inputOptions
 */
async function createRollupPluginContexts(inputOptions) {
    let normalizedInputOptions = undefined;
    let pluginContext = undefined;
    let transformPluginContext = undefined;
    await (0, rollup_1.rollup)(Object.assign(Object.assign({}, inputOptions), { input: 'noop', plugins: [
            {
                name: 'noop',
                buildStart(options) {
                    normalizedInputOptions = options;
                },
                resolveId(id) {
                    pluginContext = this; // eslint-disable-line @typescript-eslint/no-this-alias
                    return id;
                },
                load() {
                    return '';
                },
                transform() {
                    transformPluginContext = this; // eslint-disable-line @typescript-eslint/no-this-alias
                    return null;
                },
            },
        ] }));
    if (!normalizedInputOptions || !pluginContext || !transformPluginContext) {
        throw new TypeError();
    }
    return {
        normalizedInputOptions,
        pluginContext,
        transformPluginContext,
        minimalPluginContext: { meta: pluginContext.meta },
    };
}
exports.createRollupPluginContexts = createRollupPluginContexts;
//# sourceMappingURL=createRollupPluginContexts.js.map