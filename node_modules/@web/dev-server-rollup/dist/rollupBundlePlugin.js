"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.rollupBundlePlugin = void 0;
const path_1 = __importDefault(require("path"));
const rollup_1 = require("rollup");
async function bundleEntrypoints(rollupConfig) {
    const bundle = await (0, rollup_1.rollup)(rollupConfig);
    if (Array.isArray(rollupConfig.output)) {
        throw new Error('Multiple outputs not supported.');
    }
    return bundle.generate(Object.assign(Object.assign({}, rollupConfig.output), { chunkFileNames: '__rollup-generated__[name].js', assetFileNames: '__rollup-generated__[name][extname]' }));
}
function rollupBundlePlugin(pluginOptions) {
    const servedFiles = new Map();
    return {
        name: 'rollup-bundle',
        async serverStart({ config }) {
            if (config.watch) {
                throw new Error('rollup-bundle plugin does not work with watch mode');
            }
            const bundle = await bundleEntrypoints(pluginOptions.rollupConfig);
            for (const file of bundle.output) {
                let relativeFilePath;
                let content;
                if (file.type === 'chunk') {
                    if (file.isEntry) {
                        if (!file.facadeModuleId) {
                            throw new Error('Rollup output entry file does not have a facadeModuleId');
                        }
                        relativeFilePath = path_1.default.relative(config.rootDir, file.facadeModuleId);
                    }
                    else {
                        relativeFilePath = file.fileName;
                    }
                    content = file.code;
                }
                else {
                    relativeFilePath = file.fileName;
                    if (typeof file.source !== 'string') {
                        throw new Error('Rollup emitted a file whose content is not a string');
                    }
                    content = file.source;
                }
                const browserPath = `/${relativeFilePath.split(path_1.default.sep).join('/')}`;
                servedFiles.set(browserPath, content);
            }
        },
        serve(context) {
            const content = servedFiles.get(context.path);
            if (content) {
                return content;
            }
            else if (context.path.includes('__rollup-generated__')) {
                return servedFiles.get(`/${path_1.default.basename(context.path)}`);
            }
        },
    };
}
exports.rollupBundlePlugin = rollupBundlePlugin;
//# sourceMappingURL=rollupBundlePlugin.js.map