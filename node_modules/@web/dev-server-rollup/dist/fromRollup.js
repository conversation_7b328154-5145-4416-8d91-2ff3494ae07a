"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.fromRollup = void 0;
const rollupAdapter_js_1 = require("./rollupAdapter.js");
function fromRollup(rollupPluginFn, rollupInputOptions = {}, options = {}) {
    if (typeof rollupPluginFn !== 'function') {
        throw new Error(`fromRollup should be called with a rollup plugin function. Received: ${rollupPluginFn}`);
    }
    // return a function wrapper which intercepts creation of the rollup plugin
    return function wrappedRollupPluginFn(...args) {
        // call the original plugin function
        const rollupPlugin = rollupPluginFn(...args);
        // wrap the rollup plugin in an adapter for web dev server
        return (0, rollupAdapter_js_1.rollupAdapter)(rollupPlugin, rollupInputOptions, options);
    };
}
exports.fromRollup = fromRollup;
//# sourceMappingURL=fromRollup.js.map