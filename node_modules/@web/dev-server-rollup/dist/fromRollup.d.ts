import { InputOptions, Plugin as RollupPlugin } from 'rollup';
type FnArgs = readonly unknown[];
type RollupPluginFn<T extends FnArgs> = (...args: T) => RollupPlugin;
export interface FromRollupOptions {
    throwOnUnresolvedImport?: boolean;
}
export declare function fromRollup<T extends FnArgs>(rollupPluginFn: RollupPluginFn<T>, rollupInputOptions?: Partial<InputOptions>, options?: FromRollupOptions): (...args: T) => import("@web/dev-server-core").Plugin;
export {};
//# sourceMappingURL=fromRollup.d.ts.map