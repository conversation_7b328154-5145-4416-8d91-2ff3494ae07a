{"name": "@web/dev-server-rollup", "version": "0.6.4", "publishConfig": {"access": "public"}, "description": "Use rollup plugins in @web/dev-server", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/modernweb-dev/web.git", "directory": "packages/dev-server-rollup"}, "author": "modern-web", "homepage": "https://github.com/modernweb-dev/web/tree/master/packages/dev-server-rollup", "main": "dist/index.js", "exports": {".": {"types": "./index.d.ts", "import": "./index.mjs", "require": "./dist/index.js"}}, "engines": {"node": ">=18.0.0"}, "scripts": {"test:node": "mocha \"test/node/**/*.test.ts\" --require ts-node/register --exit --reporter dot", "test:watch": "mocha \"test/node/**/*.test.ts\" --require ts-node/register --watch --watch-files src,test"}, "files": ["*.d.ts", "*.js", "*.mjs", "dist", "src"], "keywords": ["web", "dev", "server", "rollup", "plugin", "test", "runner", "testrunner", "compile", "transform"], "dependencies": {"@rollup/plugin-node-resolve": "^15.0.1", "@web/dev-server-core": "^0.7.2", "nanocolors": "^0.2.1", "parse5": "^6.0.1", "rollup": "^4.4.0", "whatwg-url": "^14.0.0"}, "devDependencies": {"@babel/plugin-transform-template-literals": "^7.12.1", "@rollup/plugin-alias": "^5.0.1", "@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-commonjs": "25.0.7", "@rollup/plugin-replace": "^5.0.5", "@types/parse5": "^6.0.1", "@types/whatwg-url": "^11.0.0", "@web/test-runner-chrome": "^0.16.0", "@web/test-runner-core": "^0.13.0", "chai": "^4.2.0", "mocha": "^10.2.0", "postcss": "^8.4.31", "rollup-plugin-postcss": "^4.0.2"}}