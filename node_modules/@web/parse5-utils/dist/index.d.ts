export type TreeAdapter = import('parse5').TreeAdapter;
export type Element = import('parse5').Element;
export type Attribute = import('parse5').Attribute;
export type Node = import('parse5').Node;
export type ParentNode = import('parse5').ParentNode;
export type ChildNode = import('parse5').ChildNode;
export type CommentNode = import('parse5').CommentNode;
export type TextNode = import('parse5').TextNode;
/**
 * Creates an element node.
 *
 * @param {string} tagName Tag name of the element.
 * @param {Record<string, string>} attrs Attribute name-value pair array. Foreign attributes may contain `namespace` and `prefix` fields as well.
 * @param {string} namespaceURI  Namespace of the element.
 * @returns {Element}
 */
export function createElement(tagName: string, attrs?: Record<string, string>, namespaceURI?: string): Element;
/**
 * Creates a script element.
 * @param {Record<string,string>} [attrs]
 * @param {string} [code]
 * @returns {Element}
 */
export function createScript(attrs?: Record<string, string> | undefined, code?: string | undefined): Element;
/**
 * @param {string} html
 */
export function isHtmlFragment(html: string): boolean;
/**
 * @param {Element} element
 * @param {string} name
 */
export function hasAttribute(element: Element, name: string): boolean;
/**
 * @param {Element} element
 * @param {string} name
 */
export function getAttribute(element: Element, name: string): string | null | undefined;
/**
 * @param {Element} element
 */
export function getAttributes(element: Element): Record<string, string>;
/**
 * @param {Element} element
 * @param {string} name
 * @param {string} value
 */
export function setAttribute(element: Element, name: string, value: string): void;
/**
 * @param {Element} element
 * @param {Record<string,string|undefined>} attributes
 */
export function setAttributes(element: Element, attributes: Record<string, string | undefined>): void;
/**
 * @param {Element} element
 * @param {string} name
 */
export function removeAttribute(element: Element, name: string): void;
/**
 * @param {Node} node
 * @param {string} value
 */
export function setTextContent(node: Node, value: string): void;
/**
 * @param {Node} node
 * @returns {string}
 */
export function getTextContent(node: Node): string;
/**
 * Removes element from the AST.
 * @param {ChildNode} node
 */
export function remove(node: ChildNode): void;
/**
 * Looks for a child node which passes the given test
 * @param {Node[] | Node} nodes
 * @param {(node: Node) => boolean} test
 * @returns {Node | null}
 */
export function findNode(nodes: Node[] | Node, test: (node: Node) => boolean): Node | null;
/**
 * Looks for all child nodes which passes the given test
 * @param {Node | Node[]} nodes
 * @param {(node: Node) => boolean} test
 * @returns {Node[]}
 */
export function findNodes(nodes: Node | Node[], test: (node: Node) => boolean): Node[];
/**
 * Looks for a child element which passes the given test
 * @param {Node[] | Node} nodes
 * @param {(node: Element) => boolean} test
 * @returns {Element | null}
 */
export function findElement(nodes: Node[] | Node, test: (node: Element) => boolean): Element | null;
/**
 * Looks for all child elements which passes the given test
 * @param {Node | Node[]} nodes
 * @param {(node: Element) => boolean} test
 * @returns {Element[]}
 */
export function findElements(nodes: Node | Node[], test: (node: Element) => boolean): Element[];
/**
 * @param {ParentNode} parent
 * @param {ChildNode} node
 */
export function prepend(parent: ParentNode, node: ChildNode): void;
/**
 * Prepends HTML snippet to the given html document. The document must have either
 * a <body> or <head> element.
 * @param {string} document
 * @param {string} appendedHtml
 * @returns {string | null}
 */
export function prependToDocument(document: string, appendedHtml: string): string | null;
/**
 * Append HTML snippet to the given html document. The document must have either
 * a <body> or <head> element.
 * @param {string} document
 * @param {string} appendedHtml
 */
export function appendToDocument(document: string, appendedHtml: string): string;
export { createDocument, createDocumentFragment, createCommentNode, appendChild, insertBefore, setTemplateContent, getTemplateContent, setDocumentType, setDocumentMode, getDocumentMode, detachNode, insertText, insertTextBefore, adoptAttributes, getFirstChild, getChildNodes, getParentNode, getAttrList, getTagName, getNamespaceURI, getTextNodeContent, getCommentNodeContent, getDocumentTypeNodeName, getDocumentTypeNodePublicId, getDocumentTypeNodeSystemId, isTextNode, isCommentNode, isDocumentTypeNode, isElementNode, setNodeSourceCodeLocation, getNodeSourceCodeLocation };
//# sourceMappingURL=index.d.ts.map