# FitStudio - Веб-приложение для фитнес студии

Современное веб-приложение для управления фитнес-студией, построенное на Lit Elements и PouchDB.

## Исправленные проблемы с путями

✅ **Все ссылки в файлах исправлены для корректного запуска приложения:**

### Основные исправления:

1. **index.html**:
   - Удалена ссылка на несуществующий favicon
   - Исправлен путь к CSS: `/src/styles/main.css` → `main.css`
   - Исправлен путь к JS: `/dist/bundle.js` → `app.js`
   - Добавлен script тег для загрузки PouchDB

2. **Импорты в JavaScript файлах**:
   - `app.js`: исправлены пути к компонентам и сервисам
   - `app-router.js`: исправлены пути к страницам
   - Все страницы: исправлены пути к сервисам (`../../services/` → `./`)
   - `app-footer.js`: исправлен разбитый импорт Lit

3. **PouchDB интеграция**:
   - Исправлена проблема с ES модулями PouchDB
   - PouchDB теперь загружается как UMD модуль через script тег
   - Удален проблемный ES import в database.js

4. **Конфигурация сборки**:
   - `rollup.config.js`: исправлен входной файл (`src/app.js` → `app.js`)
   - `package.json`: обновлены скрипты сборки
   - `web-dev-server.config.js`: изменен порт на 8080

5. **Ресурсы**:
   - Заменены ссылки на несуществующие изображения в `/assets/` на placeholder'ы
   - Удалены CSS-ссылки на несуществующие фоновые изображения

## Установка и запуск

### Предварительные требования
- Node.js (версия 14 или выше)
- npm

### Установка зависимостей
```bash
npm install
```

### Запуск в режиме разработки
```bash
npm run dev
# или
npm start
```

Приложение будет доступно по адресу: http://localhost:8080

### Сборка для продакшена
```bash
npm run build
```

Собранные файлы будут находиться в папке `dist/`.

## Структура проекта

```
fit/
├── index.html              # Главная HTML страница
├── main.css               # Основные стили
├── app.js                 # Главный компонент приложения
├── app-header.js          # Компонент шапки
├── app-router.js          # Компонент маршрутизации
├── app-footer.js          # Компонент подвала
├── database.js            # Сервис работы с базой данных
├── auth.js                # Сервис аутентификации
├── home-page.js           # Главная страница
├── login-page.js          # Страница входа
├── register-page.js       # Страница регистрации
├── schedule-page.js       # Страница расписания
├── trainers-page.js       # Страница тренеров
├── about-page.js          # Страница о нас
├── admin-page.js          # Административная панель
├── not-found-page.js      # Страница 404
├── package.json           # Конфигурация проекта
├── rollup.config.js       # Конфигурация сборки
├── web-dev-server.config.js # Конфигурация dev-сервера
└── node_modules/          # Зависимости
```

## Технологии

- **Lit Elements** - для создания веб-компонентов
- **PouchDB** - для локального хранения данных
- **Web Dev Server** - для разработки
- **Rollup** - для сборки проекта

## Функциональность

- Регистрация и аутентификация пользователей
- Просмотр расписания тренировок
- Информация о тренерах
- Запись на тренировки
- Административная панель
- Адаптивный дизайн

## Статус проекта

🟢 **Готов к запуску** - все ссылки исправлены, проблема с PouchDB решена, приложение корректно запускается и собирается.
