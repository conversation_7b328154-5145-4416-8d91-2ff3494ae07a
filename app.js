import { LitElement, html, css } from '/node_modules/lit/index.js';
import { databaseService } from './database.js';
import './app-header.js';
import './app-router.js';
import './app-footer.js';

class FitnessApp extends LitElement {
  static styles = css`
    :host {
      display: block;
      min-height: 100vh;
      background-color: var(--gray-50);
    }

    .app-container {
      display: flex;
      flex-direction: column;
      min-height: 100vh;
    }

    main {
      flex: 1;
      padding: var(--spacing-4) 0;
    }

    .loading-overlay {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.9);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 9999;
      transition: opacity 0.3s ease;
    }

    .loading-overlay.hidden {
      opacity: 0;
      pointer-events: none;
    }

    .loading-spinner {
      text-align: center;
    }

    .spinner {
      width: 40px;
      height: 40px;
      border: 4px solid var(--gray-200);
      border-top: 4px solid var(--primary-color);
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 1rem;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  `;

  static properties = {
    loading: { type: Boolean },
    currentUser: { type: Object },
    currentRoute: { type: String }
  };

  constructor() {
    super();
    this.loading = true;
    this.currentUser = null;
    this.currentRoute = 'home';
    
    console.log('App: Constructor called');
    this.initializeApp();
  }

  async initializeApp() {
    try {
      // Инициализация базы данных
      await this.initializeDatabase();
      
      // Проверка сохраненной сессии пользователя
      this.checkUserSession();
      
      // Настройка маршрутизации
      this.setupRouting();
      
      // Скрытие индикатора загрузки
      setTimeout(() => {
        this.loading = false;
        const loadingElement = document.getElementById('loading');
        if (loadingElement) {
          loadingElement.classList.add('hidden');
        }
      }, 1000);
      
    } catch (error) {
      console.error('Ошибка инициализации приложения:', error);
      this.loading = false;
    }
  }

  async initializeDatabase() {
    // База данных инициализируется автоматически при создании экземпляра
    console.log('База данных инициализирована');
  }

  checkUserSession() {
    // Проверяем сохраненную сессию в localStorage
    const savedUser = localStorage.getItem('currentUser');
    if (savedUser) {
      try {
        this.currentUser = JSON.parse(savedUser);
      } catch (error) {
        console.error('Ошибка восстановления сессии:', error);
        localStorage.removeItem('currentUser');
      }
    }
  }

  setupRouting() {
    // Обработка изменения URL
    window.addEventListener('popstate', () => {
      this.updateRoute();
    });
    
    // Обработка кликов по ссылкам
    document.addEventListener('click', (e) => {
      if (e.target.matches('a[href^="/"]') || e.target.closest('a[href^="/"]')) {
        e.preventDefault();
        const link = e.target.matches('a') ? e.target : e.target.closest('a');
        this.navigateTo(link.getAttribute('href'));
      }
    });
    
    // Установка начального маршрута
    this.updateRoute();
  }

  updateRoute() {
    const path = window.location.pathname;
    this.currentRoute = path === '/' ? 'home' : path.slice(1);
    this.requestUpdate();
  }

  navigateTo(path) {
    window.history.pushState({}, '', path);
    this.updateRoute();
  }

  handleUserLogin(event) {
    this.currentUser = event.detail.user;
    localStorage.setItem('currentUser', JSON.stringify(this.currentUser));
    this.requestUpdate();
  }

  handleUserLogout() {
    this.currentUser = null;
    localStorage.removeItem('currentUser');
    this.navigateTo('/');
    this.requestUpdate();
  }

  render() {
    return html`
      <div class="app-container">
        <app-header 
          .currentUser=${this.currentUser}
          @user-logout=${this.handleUserLogout}
        ></app-header>
        
        <main>
          <app-router 
            .currentRoute=${this.currentRoute}
            .currentUser=${this.currentUser}
            @user-login=${this.handleUserLogin}
            @navigate=${(e) => this.navigateTo(e.detail.path)}
          ></app-router>
        </main>
        
        <app-footer></app-footer>
        
        ${this.loading ? html`
          <div class="loading-overlay">
            <div class="loading-spinner">
              <div class="spinner"></div>
              <p>Загрузка приложения...</p>
            </div>
          </div>
        ` : ''}
      </div>
    `;
  }
}

customElements.define('fitness-app', FitnessApp);
