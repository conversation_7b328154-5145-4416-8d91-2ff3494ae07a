{"name": "fitness-studio", "version": "1.0.0", "type": "module", "main": "index.js", "scripts": {"start": "web-dev-server", "dev": "web-dev-server", "build": "rm -rf dist && mkdir -p dist/node_modules/pouchdb/dist && cp index.html dist/ && cp main.css dist/ && cp node_modules/pouchdb/dist/pouchdb.js dist/node_modules/pouchdb/dist/ && rollup -c && sed -i '' 's/src=\"app.js\"/src=\"bundle.js\"/' dist/index.html", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["fitness", "lit", "pouchdb", "couchdb"], "author": "", "license": "ISC", "description": "Веб-приложение для фитнес студии", "dependencies": {"bcryptjs": "^3.0.2", "lit": "^3.3.1", "pouchdb": "^9.0.0", "uuid": "^13.0.0"}, "devDependencies": {"@rollup/plugin-node-resolve": "^16.0.1", "@rollup/plugin-terser": "^0.4.4", "@web/dev-server": "^0.4.6", "@web/dev-server-esbuild": "^1.0.4", "rollup": "^4.52.0"}}