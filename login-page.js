import { LitElement, html, css } from '/node_modules/lit/index.js';
import { authService } from './auth.js';

class LoginPage extends LitElement {
  static styles = css`
    :host {
      display: block;
    }

    .login-container {
      max-width: 400px;
      margin: var(--spacing-12) auto;
      padding: 0 var(--spacing-4);
    }

    .login-card {
      background: white;
      border-radius: var(--radius-xl);
      box-shadow: var(--shadow-xl);
      padding: var(--spacing-8);
    }

    .login-header {
      text-align: center;
      margin-bottom: var(--spacing-8);
    }

    .login-title {
      font-size: var(--font-size-3xl);
      font-weight: 700;
      color: var(--gray-800);
      margin-bottom: var(--spacing-2);
    }

    .login-subtitle {
      color: var(--gray-600);
      font-size: var(--font-size-base);
    }

    .form-group {
      margin-bottom: var(--spacing-6);
    }

    .form-label {
      display: block;
      font-weight: 500;
      color: var(--gray-700);
      margin-bottom: var(--spacing-2);
    }

    .form-input {
      width: 100%;
      padding: var(--spacing-3);
      border: 2px solid var(--gray-200);
      border-radius: var(--radius-lg);
      font-size: var(--font-size-base);
      transition: border-color var(--transition-fast);
    }

    .form-input:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
    }

    .form-input.error {
      border-color: var(--error-color);
    }

    .form-error {
      color: var(--error-color);
      font-size: var(--font-size-sm);
      margin-top: var(--spacing-1);
    }

    .login-button {
      width: 100%;
      padding: var(--spacing-4);
      background-color: var(--primary-color);
      color: white;
      border: none;
      border-radius: var(--radius-lg);
      font-size: var(--font-size-base);
      font-weight: 600;
      cursor: pointer;
      transition: all var(--transition-fast);
      margin-bottom: var(--spacing-6);
    }

    .login-button:hover:not(:disabled) {
      background-color: var(--primary-dark);
      transform: translateY(-1px);
      box-shadow: var(--shadow-md);
    }

    .login-button:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      transform: none;
    }

    .login-footer {
      text-align: center;
      padding-top: var(--spacing-6);
      border-top: 1px solid var(--gray-200);
    }

    .login-footer p {
      color: var(--gray-600);
      margin-bottom: var(--spacing-2);
    }

    .login-footer a {
      color: var(--primary-color);
      text-decoration: none;
      font-weight: 500;
    }

    .login-footer a:hover {
      text-decoration: underline;
    }

    .alert {
      padding: var(--spacing-3);
      border-radius: var(--radius-md);
      margin-bottom: var(--spacing-6);
      font-size: var(--font-size-sm);
    }

    .alert-error {
      background-color: rgb(254 242 242);
      border: 1px solid rgb(252 165 165);
      color: var(--error-color);
    }

    .alert-success {
      background-color: rgb(240 253 244);
      border: 1px solid rgb(167 243 208);
      color: var(--success-color);
    }

    .loading-spinner {
      display: inline-block;
      width: 16px;
      height: 16px;
      border: 2px solid transparent;
      border-top: 2px solid currentColor;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-right: var(--spacing-2);
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .demo-credentials {
      background-color: var(--gray-50);
      border: 1px solid var(--gray-200);
      border-radius: var(--radius-md);
      padding: var(--spacing-4);
      margin-bottom: var(--spacing-6);
    }

    .demo-credentials h4 {
      font-size: var(--font-size-sm);
      font-weight: 600;
      color: var(--gray-700);
      margin-bottom: var(--spacing-2);
    }

    .demo-credentials p {
      font-size: var(--font-size-xs);
      color: var(--gray-600);
      margin: var(--spacing-1) 0;
    }

    @media (max-width: 480px) {
      .login-container {
        margin: var(--spacing-6) auto;
      }

      .login-card {
        padding: var(--spacing-6);
      }
    }
  `;

  static properties = {
    loading: { type: Boolean },
    error: { type: String },
    success: { type: String },
    formData: { type: Object }
  };

  constructor() {
    super();
    this.loading = false;
    this.error = '';
    this.success = '';
    this.formData = {
      email: '',
      password: ''
    };
  }

  handleInputChange(event) {
    const { name, value } = event.target;
    this.formData = {
      ...this.formData,
      [name]: value
    };
    
    // Очищаем ошибки при изменении полей
    if (this.error) {
      this.error = '';
    }
  }

  async handleSubmit(event) {
    event.preventDefault();
    
    if (this.loading) return;

    this.loading = true;
    this.error = '';
    this.success = '';

    try {
      const result = await authService.login(this.formData.email, this.formData.password);
      
      if (result.success) {
        this.success = result.message;
        
        // Отправляем событие успешного входа
        this.dispatchEvent(new CustomEvent('user-login', {
          detail: { user: result.user }
        }));

        // Перенаправляем на главную страницу через небольшую задержку
        setTimeout(() => {
          window.history.pushState({}, '', '/');
          window.dispatchEvent(new PopStateEvent('popstate'));
        }, 1000);
        
      } else {
        this.error = result.error;
      }
    } catch (error) {
      this.error = 'Произошла ошибка при входе в систему';
      console.error('Login error:', error);
    } finally {
      this.loading = false;
    }
  }

  fillDemoCredentials(type) {
    if (type === 'admin') {
      this.formData = {
        email: '<EMAIL>',
        password: 'password'
      };
    } else {
      this.formData = {
        email: '<EMAIL>',
        password: 'password'
      };
    }
    this.requestUpdate();
  }

  render() {
    return html`
      <div class="login-container">
        <div class="login-card">
          <div class="login-header">
            <h1 class="login-title">Вход в систему</h1>
            <p class="login-subtitle">Войдите в свой аккаунт, чтобы записаться на тренировки</p>
          </div>

          <div class="demo-credentials">
            <h4>Демо-аккаунты для тестирования:</h4>
            <p><strong>Администратор:</strong> <EMAIL> / password 
              <button class="btn btn-ghost btn-sm" @click=${() => this.fillDemoCredentials('admin')}>Заполнить</button>
            </p>
          </div>

          ${this.error ? html`
            <div class="alert alert-error">
              ${this.error}
            </div>
          ` : ''}

          ${this.success ? html`
            <div class="alert alert-success">
              ${this.success}
            </div>
          ` : ''}

          <form @submit=${this.handleSubmit}>
            <div class="form-group">
              <label class="form-label" for="email">Email</label>
              <input
                type="email"
                id="email"
                name="email"
                class="form-input"
                .value=${this.formData.email}
                @input=${this.handleInputChange}
                required
                autocomplete="email"
                placeholder="<EMAIL>"
              />
            </div>

            <div class="form-group">
              <label class="form-label" for="password">Пароль</label>
              <input
                type="password"
                id="password"
                name="password"
                class="form-input"
                .value=${this.formData.password}
                @input=${this.handleInputChange}
                required
                autocomplete="current-password"
                placeholder="Введите пароль"
              />
            </div>

            <button type="submit" class="login-button" ?disabled=${this.loading}>
              ${this.loading ? html`
                <span class="loading-spinner"></span>
                Вход...
              ` : 'Войти'}
            </button>
          </form>

          <div class="login-footer">
            <p>Нет аккаунта? <a href="/register">Зарегистрироваться</a></p>
          </div>
        </div>
      </div>
    `;
  }
}

customElements.define('login-page', LoginPage);
