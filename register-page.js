import { LitElement, html, css } from '/node_modules/lit/index.js';
import { authService } from './auth.js';

class RegisterPage extends LitElement {
  static styles = css`
    :host {
      display: block;
    }

    .register-container {
      max-width: 500px;
      margin: var(--spacing-12) auto;
      padding: 0 var(--spacing-4);
    }

    .register-card {
      background: white;
      border-radius: var(--radius-xl);
      box-shadow: var(--shadow-xl);
      padding: var(--spacing-8);
    }

    .register-header {
      text-align: center;
      margin-bottom: var(--spacing-8);
    }

    .register-title {
      font-size: var(--font-size-3xl);
      font-weight: 700;
      color: var(--gray-800);
      margin-bottom: var(--spacing-2);
    }

    .register-subtitle {
      color: var(--gray-600);
      font-size: var(--font-size-base);
    }

    .form-row {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: var(--spacing-4);
    }

    .form-group {
      margin-bottom: var(--spacing-6);
    }

    .form-label {
      display: block;
      font-weight: 500;
      color: var(--gray-700);
      margin-bottom: var(--spacing-2);
    }

    .form-input {
      width: 100%;
      padding: var(--spacing-3);
      border: 2px solid var(--gray-200);
      border-radius: var(--radius-lg);
      font-size: var(--font-size-base);
      transition: border-color var(--transition-fast);
    }

    .form-input:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
    }

    .form-input.error {
      border-color: var(--error-color);
    }

    .form-error {
      color: var(--error-color);
      font-size: var(--font-size-sm);
      margin-top: var(--spacing-1);
    }

    .password-strength {
      margin-top: var(--spacing-2);
    }

    .strength-bar {
      height: 4px;
      background-color: var(--gray-200);
      border-radius: 2px;
      overflow: hidden;
      margin-bottom: var(--spacing-1);
    }

    .strength-fill {
      height: 100%;
      transition: width var(--transition-fast), background-color var(--transition-fast);
    }

    .strength-weak { background-color: var(--error-color); }
    .strength-medium { background-color: var(--warning-color); }
    .strength-strong { background-color: var(--success-color); }

    .strength-text {
      font-size: var(--font-size-xs);
      color: var(--gray-600);
    }

    .register-button {
      width: 100%;
      padding: var(--spacing-4);
      background-color: var(--primary-color);
      color: white;
      border: none;
      border-radius: var(--radius-lg);
      font-size: var(--font-size-base);
      font-weight: 600;
      cursor: pointer;
      transition: all var(--transition-fast);
      margin-bottom: var(--spacing-6);
    }

    .register-button:hover:not(:disabled) {
      background-color: var(--primary-dark);
      transform: translateY(-1px);
      box-shadow: var(--shadow-md);
    }

    .register-button:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      transform: none;
    }

    .register-footer {
      text-align: center;
      padding-top: var(--spacing-6);
      border-top: 1px solid var(--gray-200);
    }

    .register-footer p {
      color: var(--gray-600);
      margin-bottom: var(--spacing-2);
    }

    .register-footer a {
      color: var(--primary-color);
      text-decoration: none;
      font-weight: 500;
    }

    .register-footer a:hover {
      text-decoration: underline;
    }

    .alert {
      padding: var(--spacing-3);
      border-radius: var(--radius-md);
      margin-bottom: var(--spacing-6);
      font-size: var(--font-size-sm);
    }

    .alert-error {
      background-color: rgb(254 242 242);
      border: 1px solid rgb(252 165 165);
      color: var(--error-color);
    }

    .alert-success {
      background-color: rgb(240 253 244);
      border: 1px solid rgb(167 243 208);
      color: var(--success-color);
    }

    .loading-spinner {
      display: inline-block;
      width: 16px;
      height: 16px;
      border: 2px solid transparent;
      border-top: 2px solid currentColor;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-right: var(--spacing-2);
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .terms {
      font-size: var(--font-size-sm);
      color: var(--gray-600);
      margin-bottom: var(--spacing-6);
      line-height: 1.5;
    }

    .terms a {
      color: var(--primary-color);
      text-decoration: none;
    }

    .terms a:hover {
      text-decoration: underline;
    }

    @media (max-width: 640px) {
      .register-container {
        margin: var(--spacing-6) auto;
      }

      .register-card {
        padding: var(--spacing-6);
      }

      .form-row {
        grid-template-columns: 1fr;
      }
    }
  `;

  static properties = {
    loading: { type: Boolean },
    error: { type: String },
    success: { type: String },
    formData: { type: Object },
    fieldErrors: { type: Object }
  };

  constructor() {
    super();
    this.loading = false;
    this.error = '';
    this.success = '';
    this.formData = {
      name: '',
      email: '',
      phone: '',
      password: '',
      confirmPassword: ''
    };
    this.fieldErrors = {};
  }

  handleInputChange(event) {
    const { name, value } = event.target;
    this.formData = {
      ...this.formData,
      [name]: value
    };
    
    // Очищаем ошибки при изменении полей
    if (this.error) {
      this.error = '';
    }
    
    if (this.fieldErrors[name]) {
      this.fieldErrors = {
        ...this.fieldErrors,
        [name]: ''
      };
    }

    this.requestUpdate();
  }

  validateField(name, value) {
    switch (name) {
      case 'name':
        return value.trim().length >= 2 ? '' : 'Имя должно содержать минимум 2 символа';
      
      case 'email':
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(value) ? '' : 'Некорректный формат email';
      
      case 'phone':
        const phoneRegex = /^(\+7|8)?[\s\-]?\(?[489][0-9]{2}\)?[\s\-]?[0-9]{3}[\s\-]?[0-9]{2}[\s\-]?[0-9]{2}$/;
        return phoneRegex.test(value.replace(/\s/g, '')) ? '' : 'Некорректный формат телефона';
      
      case 'password':
        return value.length >= 6 ? '' : 'Пароль должен содержать минимум 6 символов';
      
      case 'confirmPassword':
        return value === this.formData.password ? '' : 'Пароли не совпадают';
      
      default:
        return '';
    }
  }

  getPasswordStrength(password) {
    if (!password) return { strength: 0, text: '' };
    
    let score = 0;
    
    // Длина
    if (password.length >= 8) score += 1;
    if (password.length >= 12) score += 1;
    
    // Содержит цифры
    if (/\d/.test(password)) score += 1;
    
    // Содержит строчные буквы
    if (/[a-z]/.test(password)) score += 1;
    
    // Содержит заглавные буквы
    if (/[A-Z]/.test(password)) score += 1;
    
    // Содержит специальные символы
    if (/[^A-Za-z0-9]/.test(password)) score += 1;

    if (score <= 2) return { strength: 1, text: 'Слабый пароль' };
    if (score <= 4) return { strength: 2, text: 'Средний пароль' };
    return { strength: 3, text: 'Сильный пароль' };
  }

  async handleSubmit(event) {
    event.preventDefault();
    
    if (this.loading) return;

    // Валидация всех полей
    const errors = {};
    Object.keys(this.formData).forEach(field => {
      const error = this.validateField(field, this.formData[field]);
      if (error) errors[field] = error;
    });

    if (Object.keys(errors).length > 0) {
      this.fieldErrors = errors;
      this.error = 'Пожалуйста, исправьте ошибки в форме';
      return;
    }

    this.loading = true;
    this.error = '';
    this.success = '';
    this.fieldErrors = {};

    try {
      const result = await authService.register(this.formData);
      
      if (result.success) {
        this.success = result.message;
        
        // Отправляем событие успешной регистрации
        this.dispatchEvent(new CustomEvent('user-login', {
          detail: { user: result.user }
        }));

        // Перенаправляем на главную страницу через небольшую задержку
        setTimeout(() => {
          window.history.pushState({}, '', '/');
          window.dispatchEvent(new PopStateEvent('popstate'));
        }, 1500);
        
      } else {
        this.error = result.error;
      }
    } catch (error) {
      this.error = 'Произошла ошибка при регистрации';
      console.error('Registration error:', error);
    } finally {
      this.loading = false;
    }
  }

  render() {
    const passwordStrength = this.getPasswordStrength(this.formData.password);
    
    return html`
      <div class="register-container">
        <div class="register-card">
          <div class="register-header">
            <h1 class="register-title">Регистрация</h1>
            <p class="register-subtitle">Создайте аккаунт, чтобы записываться на тренировки</p>
          </div>

          ${this.error ? html`
            <div class="alert alert-error">
              ${this.error}
            </div>
          ` : ''}

          ${this.success ? html`
            <div class="alert alert-success">
              ${this.success}
            </div>
          ` : ''}

          <form @submit=${this.handleSubmit}>
            <div class="form-group">
              <label class="form-label" for="name">Полное имя</label>
              <input
                type="text"
                id="name"
                name="name"
                class="form-input ${this.fieldErrors.name ? 'error' : ''}"
                .value=${this.formData.name}
                @input=${this.handleInputChange}
                required
                autocomplete="name"
                placeholder="Иван Иванов"
              />
              ${this.fieldErrors.name ? html`
                <div class="form-error">${this.fieldErrors.name}</div>
              ` : ''}
            </div>

            <div class="form-row">
              <div class="form-group">
                <label class="form-label" for="email">Email</label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  class="form-input ${this.fieldErrors.email ? 'error' : ''}"
                  .value=${this.formData.email}
                  @input=${this.handleInputChange}
                  required
                  autocomplete="email"
                  placeholder="<EMAIL>"
                />
                ${this.fieldErrors.email ? html`
                  <div class="form-error">${this.fieldErrors.email}</div>
                ` : ''}
              </div>

              <div class="form-group">
                <label class="form-label" for="phone">Телефон</label>
                <input
                  type="tel"
                  id="phone"
                  name="phone"
                  class="form-input ${this.fieldErrors.phone ? 'error' : ''}"
                  .value=${this.formData.phone}
                  @input=${this.handleInputChange}
                  required
                  autocomplete="tel"
                  placeholder="+7 (999) 123-45-67"
                />
                ${this.fieldErrors.phone ? html`
                  <div class="form-error">${this.fieldErrors.phone}</div>
                ` : ''}
              </div>
            </div>

            <div class="form-group">
              <label class="form-label" for="password">Пароль</label>
              <input
                type="password"
                id="password"
                name="password"
                class="form-input ${this.fieldErrors.password ? 'error' : ''}"
                .value=${this.formData.password}
                @input=${this.handleInputChange}
                required
                autocomplete="new-password"
                placeholder="Минимум 6 символов"
              />
              ${this.fieldErrors.password ? html`
                <div class="form-error">${this.fieldErrors.password}</div>
              ` : ''}
              
              ${this.formData.password ? html`
                <div class="password-strength">
                  <div class="strength-bar">
                    <div class="strength-fill strength-${passwordStrength.strength === 1 ? 'weak' : passwordStrength.strength === 2 ? 'medium' : 'strong'}" 
                         style="width: ${(passwordStrength.strength / 3) * 100}%"></div>
                  </div>
                  <div class="strength-text">${passwordStrength.text}</div>
                </div>
              ` : ''}
            </div>

            <div class="form-group">
              <label class="form-label" for="confirmPassword">Подтверждение пароля</label>
              <input
                type="password"
                id="confirmPassword"
                name="confirmPassword"
                class="form-input ${this.fieldErrors.confirmPassword ? 'error' : ''}"
                .value=${this.formData.confirmPassword}
                @input=${this.handleInputChange}
                required
                autocomplete="new-password"
                placeholder="Повторите пароль"
              />
              ${this.fieldErrors.confirmPassword ? html`
                <div class="form-error">${this.fieldErrors.confirmPassword}</div>
              ` : ''}
            </div>

            <div class="terms">
              Регистрируясь, вы соглашаетесь с <a href="#">Условиями использования</a> 
              и <a href="#">Политикой конфиденциальности</a>
            </div>

            <button type="submit" class="register-button" ?disabled=${this.loading}>
              ${this.loading ? html`
                <span class="loading-spinner"></span>
                Регистрация...
              ` : 'Зарегистрироваться'}
            </button>
          </form>

          <div class="register-footer">
            <p>Уже есть аккаунт? <a href="/login">Войти</a></p>
          </div>
        </div>
      </div>
    `;
  }
}

customElements.define('register-page', RegisterPage);
