import { LitElement, html, css } from '/node_modules/lit/index.js';

class AppFooter extends LitElement {
  static styles = css`
    :host {
      display: block;
      background-color: var(--gray-800);
      color: var(--gray-300);
      margin-top: auto;
    }

    .footer {
      max-width: var(--container-xl);
      margin: 0 auto;
      padding: var(--spacing-12) var(--spacing-4) var(--spacing-6);
    }

    .footer-content {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: var(--spacing-8);
      margin-bottom: var(--spacing-8);
    }

    .footer-section h3 {
      color: white;
      font-size: var(--font-size-lg);
      font-weight: 600;
      margin-bottom: var(--spacing-4);
    }

    .footer-section p,
    .footer-section li {
      color: var(--gray-400);
      line-height: 1.6;
      margin-bottom: var(--spacing-2);
    }

    .footer-section ul {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    .footer-section a {
      color: var(--gray-400);
      text-decoration: none;
      transition: color var(--transition-fast);
    }

    .footer-section a:hover {
      color: var(--primary-light);
    }

    .contact-info {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-3);
    }

    .contact-item {
      display: flex;
      align-items: center;
      gap: var(--spacing-3);
    }

    .contact-icon {
      width: 20px;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: var(--primary-color);
      border-radius: var(--radius-md);
      color: white;
      font-size: var(--font-size-sm);
    }

    .social-links {
      display: flex;
      gap: var(--spacing-4);
      margin-top: var(--spacing-4);
    }

    .social-link {
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: var(--gray-700);
      border-radius: var(--radius-lg);
      color: var(--gray-400);
      text-decoration: none;
      transition: all var(--transition-fast);
    }

    .social-link:hover {
      background-color: var(--primary-color);
      color: white;
      transform: translateY(-2px);
    }

    .footer-bottom {
      border-top: 1px solid var(--gray-700);
      padding-top: var(--spacing-6);
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      gap: var(--spacing-4);
    }

    .footer-bottom p {
      margin: 0;
      color: var(--gray-500);
      font-size: var(--font-size-sm);
    }

    .footer-links {
      display: flex;
      gap: var(--spacing-6);
      list-style: none;
      margin: 0;
      padding: 0;
    }

    .footer-links a {
      color: var(--gray-500);
      text-decoration: none;
      font-size: var(--font-size-sm);
      transition: color var(--transition-fast);
    }

    .footer-links a:hover {
      color: var(--primary-light);
    }

    @media (max-width: 768px) {
      .footer-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-6);
      }

      .footer-bottom {
        flex-direction: column;
        text-align: center;
      }

      .footer-links {
        justify-content: center;
      }
    }
  `;

  render() {
    return html`
      <footer class="footer">
        <div class="footer-content">
          <div class="footer-section">
            <h3>FitStudio</h3>
            <p>Современная фитнес-студия с профессиональными тренерами и разнообразными программами тренировок. Мы поможем вам достичь ваших целей в фитнесе.</p>
            <div class="social-links">
              <a href="#" class="social-link" title="Instagram">📷</a>
              <a href="#" class="social-link" title="Facebook">📘</a>
              <a href="#" class="social-link" title="VKontakte">🔵</a>
              <a href="#" class="social-link" title="Telegram">✈️</a>
            </div>
          </div>

          <div class="footer-section">
            <h3>Быстрые ссылки</h3>
            <ul>
              <li><a href="/">Главная</a></li>
              <li><a href="/schedule">Расписание</a></li>
              <li><a href="/trainers">Тренеры</a></li>
              <li><a href="/about">О студии</a></li>
            </ul>
          </div>

          <div class="footer-section">
            <h3>Программы</h3>
            <ul>
              <li><a href="/schedule">Йога</a></li>
              <li><a href="/schedule">Пилатес</a></li>
              <li><a href="/schedule">Функциональный тренинг</a></li>
              <li><a href="/schedule">Танцевальная аэробика</a></li>
            </ul>
          </div>

          <div class="footer-section">
            <h3>Контакты</h3>
            <div class="contact-info">
              <div class="contact-item">
                <div class="contact-icon">📍</div>
                <span>г. Москва, ул. Спортивная, д. 15</span>
              </div>
              <div class="contact-item">
                <div class="contact-icon">📞</div>
                <span>+7 (495) 123-45-67</span>
              </div>
              <div class="contact-item">
                <div class="contact-icon">✉️</div>
                <span><EMAIL></span>
              </div>
              <div class="contact-item">
                <div class="contact-icon">🕒</div>
                <span>Пн-Пт: 07:00-23:00<br>Сб-Вс: 09:00-21:00</span>
              </div>
            </div>
          </div>
        </div>

        <div class="footer-bottom">
          <p>&copy; 2025 FitStudio. Все права защищены.</p>
          <ul class="footer-links">
            <li><a href="#">Политика конфиденциальности</a></li>
            <li><a href="#">Условия использования</a></li>
            <li><a href="#">Публичная оферта</a></li>
          </ul>
        </div>
      </footer>
    `;
  }
}

customElements.define('app-footer', AppFooter);
