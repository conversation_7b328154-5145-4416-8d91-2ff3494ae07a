import { LitElement, html, css } from '/node_modules/lit/index.js';
import { databaseService } from './database.js';

class AdminPage extends LitElement {
  static styles = css`
    :host {
      display: block;
    }

    .admin-container {
      max-width: var(--container-xl);
      margin: 0 auto;
      padding: var(--spacing-6) var(--spacing-4);
    }

    .admin-header {
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      color: white;
      padding: var(--spacing-8);
      border-radius: var(--radius-xl);
      margin-bottom: var(--spacing-8);
      text-align: center;
    }

    .admin-title {
      font-size: var(--font-size-3xl);
      font-weight: 700;
      margin-bottom: var(--spacing-2);
    }

    .admin-subtitle {
      font-size: var(--font-size-lg);
      opacity: 0.9;
    }

    .admin-tabs {
      display: flex;
      background: white;
      border-radius: var(--radius-xl);
      box-shadow: var(--shadow-sm);
      margin-bottom: var(--spacing-8);
      overflow: hidden;
    }

    .tab-button {
      flex: 1;
      padding: var(--spacing-4) var(--spacing-6);
      background: none;
      border: none;
      font-size: var(--font-size-base);
      font-weight: 500;
      color: var(--gray-600);
      cursor: pointer;
      transition: all var(--transition-fast);
      position: relative;
    }

    .tab-button.active {
      color: var(--primary-color);
      background-color: rgb(37 99 235 / 0.05);
    }

    .tab-button.active::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 3px;
      background: var(--primary-color);
    }

    .tab-button:hover:not(.active) {
      background-color: var(--gray-50);
    }

    .tab-content {
      background: white;
      border-radius: var(--radius-xl);
      box-shadow: var(--shadow-sm);
      padding: var(--spacing-8);
    }

    .section-title {
      font-size: var(--font-size-xl);
      font-weight: 700;
      color: var(--gray-800);
      margin-bottom: var(--spacing-6);
      display: flex;
      align-items: center;
      gap: var(--spacing-3);
    }

    .form-group {
      margin-bottom: var(--spacing-6);
    }

    .form-label {
      display: block;
      font-weight: 500;
      color: var(--gray-700);
      margin-bottom: var(--spacing-2);
    }

    .form-input,
    .form-textarea {
      width: 100%;
      padding: var(--spacing-3);
      border: 2px solid var(--gray-200);
      border-radius: var(--radius-lg);
      font-size: var(--font-size-base);
      transition: border-color var(--transition-fast);
      font-family: inherit;
    }

    .form-textarea {
      min-height: 120px;
      resize: vertical;
    }

    .form-input:focus,
    .form-textarea:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
    }

    .save-button {
      background-color: var(--primary-color);
      color: white;
      border: none;
      border-radius: var(--radius-lg);
      padding: var(--spacing-3) var(--spacing-6);
      font-size: var(--font-size-base);
      font-weight: 600;
      cursor: pointer;
      transition: all var(--transition-fast);
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
    }

    .save-button:hover:not(:disabled) {
      background-color: var(--primary-dark);
      transform: translateY(-1px);
      box-shadow: var(--shadow-md);
    }

    .save-button:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      transform: none;
    }

    .slides-grid {
      display: grid;
      gap: var(--spacing-6);
    }

    .slide-card {
      border: 2px solid var(--gray-200);
      border-radius: var(--radius-lg);
      padding: var(--spacing-6);
      transition: border-color var(--transition-fast);
    }

    .slide-card:hover {
      border-color: var(--primary-color);
    }

    .slide-header {
      display: flex;
      justify-content: between;
      align-items: center;
      margin-bottom: var(--spacing-4);
    }

    .slide-number {
      background: var(--primary-color);
      color: white;
      width: 32px;
      height: 32px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      font-size: var(--font-size-sm);
    }

    .slide-actions {
      display: flex;
      gap: var(--spacing-2);
    }

    .action-button {
      padding: var(--spacing-2) var(--spacing-3);
      border: none;
      border-radius: var(--radius-md);
      font-size: var(--font-size-sm);
      font-weight: 500;
      cursor: pointer;
      transition: all var(--transition-fast);
    }

    .btn-edit {
      background-color: var(--warning-color);
      color: white;
    }

    .btn-edit:hover {
      background-color: #d97706;
    }

    .btn-delete {
      background-color: var(--error-color);
      color: white;
    }

    .btn-delete:hover {
      background-color: #dc2626;
    }

    .slide-form {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: var(--spacing-4);
    }

    .slide-preview {
      grid-column: 1 / -1;
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      color: white;
      padding: var(--spacing-6);
      border-radius: var(--radius-lg);
      text-align: center;
      margin-bottom: var(--spacing-4);
    }

    .preview-title {
      font-size: var(--font-size-xl);
      font-weight: 700;
      margin-bottom: var(--spacing-2);
    }

    .preview-subtitle {
      font-size: var(--font-size-base);
      opacity: 0.9;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: var(--spacing-6);
      margin-bottom: var(--spacing-8);
    }

    .stat-card {
      background: white;
      border-radius: var(--radius-lg);
      padding: var(--spacing-6);
      text-align: center;
      box-shadow: var(--shadow-sm);
      border: 2px solid var(--gray-100);
      transition: all var(--transition-fast);
    }

    .stat-card:hover {
      border-color: var(--primary-color);
      transform: translateY(-2px);
    }

    .stat-icon {
      width: 60px;
      height: 60px;
      margin: 0 auto var(--spacing-3);
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: var(--font-size-xl);
    }

    .stat-number {
      font-size: var(--font-size-2xl);
      font-weight: 700;
      color: var(--gray-800);
      margin-bottom: var(--spacing-1);
    }

    .stat-label {
      color: var(--gray-600);
      font-size: var(--font-size-sm);
    }

    .notification {
      position: fixed;
      top: var(--spacing-4);
      right: var(--spacing-4);
      padding: var(--spacing-4) var(--spacing-6);
      border-radius: var(--radius-lg);
      color: white;
      font-weight: 500;
      z-index: 1000;
      animation: slideIn 0.3s ease-out;
    }

    .notification.success {
      background-color: var(--success-color);
    }

    .notification.error {
      background-color: var(--error-color);
    }

    @keyframes slideIn {
      from {
        transform: translateX(100%);
        opacity: 0;
      }
      to {
        transform: translateX(0);
        opacity: 1;
      }
    }

    .loading-spinner {
      display: inline-block;
      width: 16px;
      height: 16px;
      border: 2px solid transparent;
      border-top: 2px solid currentColor;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    @media (max-width: 768px) {
      .admin-tabs {
        flex-direction: column;
      }

      .slide-form {
        grid-template-columns: 1fr;
      }

      .stats-grid {
        grid-template-columns: repeat(2, 1fr);
      }
    }

    @media (max-width: 480px) {
      .stats-grid {
        grid-template-columns: 1fr;
      }
    }
  `;

  static properties = {
    currentUser: { type: Object },
    activeTab: { type: String },
    homeContent: { type: Object },
    slides: { type: Array },
    loading: { type: Boolean },
    stats: { type: Object }
  };

  constructor() {
    super();
    this.currentUser = null;
    this.activeTab = 'dashboard';
    this.homeContent = {};
    this.slides = [];
    this.loading = false;
    this.stats = {
      totalUsers: 0,
      totalBookings: 0,
      totalTrainers: 0,
      totalWorkouts: 0
    };
    
    this.loadData();
  }

  async loadData() {
    try {
      this.loading = true;
      
      // Загружаем контент главной страницы
      const content = await databaseService.getContent('home');
      if (content) {
        this.homeContent = {
          title: content.title || '',
          subtitle: content.subtitle || '',
          description: content.description || ''
        };
      }
      
      // Загружаем слайды
      this.slides = await databaseService.getSlides();
      
      // Загружаем статистику (эмуляция)
      this.stats = {
        totalUsers: 156,
        totalBookings: 342,
        totalTrainers: 8,
        totalWorkouts: 15
      };
      
    } catch (error) {
      console.error('Ошибка загрузки данных:', error);
      this.showNotification('Ошибка загрузки данных', 'error');
    } finally {
      this.loading = false;
    }
  }

  switchTab(tab) {
    this.activeTab = tab;
  }

  async saveHomeContent() {
    try {
      this.loading = true;
      
      await databaseService.updateContent('home', this.homeContent);
      this.showNotification('Контент главной страницы сохранен', 'success');
      
    } catch (error) {
      console.error('Ошибка сохранения контента:', error);
      this.showNotification('Ошибка сохранения контента', 'error');
    } finally {
      this.loading = false;
    }
  }

  async saveSlide(slideId, slideData) {
    try {
      this.loading = true;
      
      await databaseService.updateSlide(slideId, slideData);
      await this.loadData(); // Перезагружаем данные
      this.showNotification('Слайд сохранен', 'success');
      
    } catch (error) {
      console.error('Ошибка сохранения слайда:', error);
      this.showNotification('Ошибка сохранения слайда', 'error');
    } finally {
      this.loading = false;
    }
  }

  handleContentInput(field, value) {
    this.homeContent = {
      ...this.homeContent,
      [field]: value
    };
  }

  handleSlideInput(slideId, field, value) {
    this.slides = this.slides.map(slide => 
      slide._id === slideId 
        ? { ...slide, [field]: value }
        : slide
    );
  }

  showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    document.body.appendChild(notification);
    
    setTimeout(() => {
      notification.remove();
    }, 5000);
  }

  renderDashboard() {
    return html`
      <div class="section-title">
        📊 Панель управления
      </div>
      
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon">👥</div>
          <div class="stat-number">${this.stats.totalUsers}</div>
          <div class="stat-label">Пользователей</div>
        </div>
        <div class="stat-card">
          <div class="stat-icon">📅</div>
          <div class="stat-number">${this.stats.totalBookings}</div>
          <div class="stat-label">Записей на тренировки</div>
        </div>
        <div class="stat-card">
          <div class="stat-icon">👨‍🏫</div>
          <div class="stat-number">${this.stats.totalTrainers}</div>
          <div class="stat-label">Тренеров</div>
        </div>
        <div class="stat-card">
          <div class="stat-icon">🏋️‍♀️</div>
          <div class="stat-number">${this.stats.totalWorkouts}</div>
          <div class="stat-label">Видов тренировок</div>
        </div>
      </div>
      
      <div class="section-title">
        📈 Быстрые действия
      </div>
      
      <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: var(--spacing-4);">
        <button class="save-button" @click=${() => this.switchTab('content')}>
          ✏️ Редактировать главную страницу
        </button>
        <button class="save-button" @click=${() => this.switchTab('slider')}>
          🖼️ Управление слайдером
        </button>
        <button class="save-button" @click=${() => window.open('/schedule', '_blank')}>
          📅 Посмотреть расписание
        </button>
        <button class="save-button" @click=${() => window.open('/trainers', '_blank')}>
          👨‍🏫 Управление тренерами
        </button>
      </div>
    `;
  }

  renderContentEditor() {
    return html`
      <div class="section-title">
        ✏️ Редактирование главной страницы
      </div>
      
      <form @submit=${(e) => { e.preventDefault(); this.saveHomeContent(); }}>
        <div class="form-group">
          <label class="form-label">Заголовок</label>
          <input
            type="text"
            class="form-input"
            .value=${this.homeContent.title || ''}
            @input=${(e) => this.handleContentInput('title', e.target.value)}
            placeholder="Добро пожаловать в FitStudio"
          />
        </div>
        
        <div class="form-group">
          <label class="form-label">Подзаголовок</label>
          <input
            type="text"
            class="form-input"
            .value=${this.homeContent.subtitle || ''}
            @input=${(e) => this.handleContentInput('subtitle', e.target.value)}
            placeholder="Ваш путь к здоровью и красоте"
          />
        </div>
        
        <div class="form-group">
          <label class="form-label">Описание</label>
          <textarea
            class="form-textarea"
            .value=${this.homeContent.description || ''}
            @input=${(e) => this.handleContentInput('description', e.target.value)}
            placeholder="Современная фитнес-студия с профессиональными тренерами..."
          ></textarea>
        </div>
        
        <button type="submit" class="save-button" ?disabled=${this.loading}>
          ${this.loading ? html`<span class="loading-spinner"></span>` : '💾'}
          Сохранить изменения
        </button>
      </form>
    `;
  }

  renderSliderEditor() {
    return html`
      <div class="section-title">
        🖼️ Управление слайдером
      </div>
      
      <div class="slides-grid">
        ${this.slides.map((slide, index) => html`
          <div class="slide-card">
            <div class="slide-header">
              <div class="slide-number">${index + 1}</div>
              <div class="slide-actions">
                <button class="action-button btn-edit" 
                        @click=${() => this.saveSlide(slide._id, slide)}>
                  💾 Сохранить
                </button>
              </div>
            </div>
            
            <div class="slide-preview">
              <div class="preview-title">${slide.title || 'Заголовок слайда'}</div>
              <div class="preview-subtitle">${slide.subtitle || 'Подзаголовок слайда'}</div>
            </div>
            
            <div class="slide-form">
              <div class="form-group">
                <label class="form-label">Заголовок</label>
                <input
                  type="text"
                  class="form-input"
                  .value=${slide.title || ''}
                  @input=${(e) => this.handleSlideInput(slide._id, 'title', e.target.value)}
                  placeholder="Заголовок слайда"
                />
              </div>
              
              <div class="form-group">
                <label class="form-label">Подзаголовок</label>
                <input
                  type="text"
                  class="form-input"
                  .value=${slide.subtitle || ''}
                  @input=${(e) => this.handleSlideInput(slide._id, 'subtitle', e.target.value)}
                  placeholder="Подзаголовок слайда"
                />
              </div>
              
              <div class="form-group">
                <label class="form-label">Порядок отображения</label>
                <input
                  type="number"
                  class="form-input"
                  .value=${slide.order || 1}
                  @input=${(e) => this.handleSlideInput(slide._id, 'order', parseInt(e.target.value))}
                  min="1"
                />
              </div>
              
              <div class="form-group">
                <label class="form-label">Статус</label>
                <select
                  class="form-input"
                  .value=${slide.active ? 'true' : 'false'}
                  @change=${(e) => this.handleSlideInput(slide._id, 'active', e.target.value === 'true')}
                >
                  <option value="true">Активен</option>
                  <option value="false">Неактивен</option>
                </select>
              </div>
            </div>
          </div>
        `)}
      </div>
    `;
  }

  render() {
    // Проверка прав доступа
    if (!this.currentUser || this.currentUser.role !== 'admin') {
      return html`
        <div class="admin-container">
          <div style="text-align: center; padding: var(--spacing-12);">
            <h2>Доступ запрещен</h2>
            <p>У вас нет прав для доступа к админ-панели</p>
            <a href="/" class="save-button" style="margin-top: var(--spacing-4); display: inline-flex;">
              Вернуться на главную
            </a>
          </div>
        </div>
      `;
    }

    return html`
      <div class="admin-container">
        <div class="admin-header">
          <h1 class="admin-title">Панель администратора</h1>
          <p class="admin-subtitle">Управление контентом и настройками FitStudio</p>
        </div>

        <div class="admin-tabs">
          <button 
            class="tab-button ${this.activeTab === 'dashboard' ? 'active' : ''}"
            @click=${() => this.switchTab('dashboard')}
          >
            📊 Панель управления
          </button>
          <button 
            class="tab-button ${this.activeTab === 'content' ? 'active' : ''}"
            @click=${() => this.switchTab('content')}
          >
            ✏️ Главная страница
          </button>
          <button 
            class="tab-button ${this.activeTab === 'slider' ? 'active' : ''}"
            @click=${() => this.switchTab('slider')}
          >
            🖼️ Слайдер
          </button>
        </div>

        <div class="tab-content">
          ${this.activeTab === 'dashboard' ? this.renderDashboard() : ''}
          ${this.activeTab === 'content' ? this.renderContentEditor() : ''}
          ${this.activeTab === 'slider' ? this.renderSliderEditor() : ''}
        </div>
      </div>
    `;
  }
}

customElements.define('admin-page', AdminPage);
