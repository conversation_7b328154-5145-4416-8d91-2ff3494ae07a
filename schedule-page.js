import { LitElement, html, css } from '/node_modules/lit/index.js';
import { databaseService } from './database.js';

class SchedulePage extends LitElement {
  static styles = css`
    :host {
      display: block;
    }

    .schedule-container {
      max-width: var(--container-xl);
      margin: 0 auto;
      padding: var(--spacing-6) var(--spacing-4);
    }

    .schedule-header {
      text-align: center;
      margin-bottom: var(--spacing-8);
    }

    .schedule-title {
      font-size: var(--font-size-3xl);
      font-weight: 700;
      color: var(--gray-800);
      margin-bottom: var(--spacing-4);
    }

    .schedule-subtitle {
      font-size: var(--font-size-lg);
      color: var(--gray-600);
      max-width: 600px;
      margin: 0 auto;
    }

    .schedule-filters {
      background: white;
      border-radius: var(--radius-xl);
      box-shadow: var(--shadow-sm);
      padding: var(--spacing-6);
      margin-bottom: var(--spacing-8);
      display: flex;
      gap: var(--spacing-4);
      align-items: center;
      flex-wrap: wrap;
    }

    .filter-group {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-2);
    }

    .filter-label {
      font-weight: 500;
      color: var(--gray-700);
      font-size: var(--font-size-sm);
    }

    .filter-select {
      padding: var(--spacing-2) var(--spacing-3);
      border: 2px solid var(--gray-200);
      border-radius: var(--radius-md);
      font-size: var(--font-size-sm);
      background: white;
      cursor: pointer;
    }

    .filter-select:focus {
      outline: none;
      border-color: var(--primary-color);
    }

    .date-navigation {
      display: flex;
      align-items: center;
      gap: var(--spacing-4);
      margin-left: auto;
    }

    .date-nav-btn {
      background: none;
      border: 2px solid var(--gray-200);
      border-radius: var(--radius-md);
      padding: var(--spacing-2);
      cursor: pointer;
      color: var(--gray-600);
      transition: all var(--transition-fast);
    }

    .date-nav-btn:hover {
      border-color: var(--primary-color);
      color: var(--primary-color);
    }

    .current-date {
      font-weight: 600;
      color: var(--gray-800);
      min-width: 120px;
      text-align: center;
    }

    .schedule-grid {
      display: grid;
      gap: var(--spacing-6);
    }

    .day-section {
      background: white;
      border-radius: var(--radius-xl);
      box-shadow: var(--shadow-sm);
      overflow: hidden;
    }

    .day-header {
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      color: white;
      padding: var(--spacing-4) var(--spacing-6);
      text-align: center;
    }

    .day-title {
      font-size: var(--font-size-lg);
      font-weight: 600;
      margin-bottom: var(--spacing-1);
    }

    .day-date {
      font-size: var(--font-size-sm);
      opacity: 0.9;
    }

    .workouts-list {
      padding: var(--spacing-6);
    }

    .workout-card {
      border: 2px solid var(--gray-100);
      border-radius: var(--radius-lg);
      padding: var(--spacing-4);
      margin-bottom: var(--spacing-4);
      transition: all var(--transition-fast);
      cursor: pointer;
    }

    .workout-card:hover {
      border-color: var(--primary-color);
      box-shadow: var(--shadow-md);
      transform: translateY(-2px);
    }

    .workout-card:last-child {
      margin-bottom: 0;
    }

    .workout-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: var(--spacing-3);
    }

    .workout-info h3 {
      font-size: var(--font-size-lg);
      font-weight: 600;
      color: var(--gray-800);
      margin-bottom: var(--spacing-1);
    }

    .workout-time {
      font-size: var(--font-size-sm);
      color: var(--gray-600);
      display: flex;
      align-items: center;
      gap: var(--spacing-1);
    }

    .workout-status {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      gap: var(--spacing-2);
    }

    .participants-info {
      font-size: var(--font-size-sm);
      color: var(--gray-600);
      text-align: right;
    }

    .participants-count {
      font-weight: 600;
      color: var(--primary-color);
    }

    .workout-details {
      display: grid;
      grid-template-columns: 1fr auto;
      gap: var(--spacing-4);
      align-items: center;
    }

    .workout-meta {
      display: flex;
      gap: var(--spacing-4);
      font-size: var(--font-size-sm);
      color: var(--gray-600);
    }

    .meta-item {
      display: flex;
      align-items: center;
      gap: var(--spacing-1);
    }

    .difficulty-badge {
      padding: var(--spacing-1) var(--spacing-2);
      border-radius: var(--radius-sm);
      font-size: var(--font-size-xs);
      font-weight: 500;
      text-transform: uppercase;
    }

    .difficulty-beginner {
      background-color: rgb(34 197 94 / 0.1);
      color: var(--success-color);
    }

    .difficulty-intermediate {
      background-color: rgb(251 191 36 / 0.1);
      color: var(--warning-color);
    }

    .difficulty-advanced {
      background-color: rgb(239 68 68 / 0.1);
      color: var(--error-color);
    }

    .book-btn {
      padding: var(--spacing-2) var(--spacing-4);
      background-color: var(--primary-color);
      color: white;
      border: none;
      border-radius: var(--radius-md);
      font-size: var(--font-size-sm);
      font-weight: 500;
      cursor: pointer;
      transition: all var(--transition-fast);
    }

    .book-btn:hover:not(:disabled) {
      background-color: var(--primary-dark);
      transform: translateY(-1px);
    }

    .book-btn:disabled {
      background-color: var(--gray-400);
      cursor: not-allowed;
      transform: none;
    }

    .book-btn.booked {
      background-color: var(--success-color);
    }

    .book-btn.full {
      background-color: var(--error-color);
    }

    .empty-state {
      text-align: center;
      padding: var(--spacing-12);
      color: var(--gray-500);
    }

    .empty-state-icon {
      font-size: 4rem;
      margin-bottom: var(--spacing-4);
    }

    .loading-state {
      text-align: center;
      padding: var(--spacing-12);
    }

    .spinner {
      width: 40px;
      height: 40px;
      border: 4px solid var(--gray-200);
      border-top: 4px solid var(--primary-color);
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto var(--spacing-4);
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .notification {
      position: fixed;
      top: var(--spacing-4);
      right: var(--spacing-4);
      padding: var(--spacing-4) var(--spacing-6);
      border-radius: var(--radius-lg);
      color: white;
      font-weight: 500;
      z-index: 1000;
      animation: slideIn 0.3s ease-out;
    }

    .notification.success {
      background-color: var(--success-color);
    }

    .notification.error {
      background-color: var(--error-color);
    }

    @keyframes slideIn {
      from {
        transform: translateX(100%);
        opacity: 0;
      }
      to {
        transform: translateX(0);
        opacity: 1;
      }
    }

    @media (max-width: 768px) {
      .schedule-filters {
        flex-direction: column;
        align-items: stretch;
      }

      .date-navigation {
        margin-left: 0;
        justify-content: center;
      }

      .workout-header {
        flex-direction: column;
        gap: var(--spacing-2);
      }

      .workout-details {
        grid-template-columns: 1fr;
        gap: var(--spacing-3);
      }

      .workout-meta {
        flex-wrap: wrap;
      }
    }
  `;

  static properties = {
    currentUser: { type: Object },
    schedule: { type: Array },
    workouts: { type: Array },
    trainers: { type: Array },
    userBookings: { type: Array },
    loading: { type: Boolean },
    selectedDate: { type: String },
    filterWorkout: { type: String },
    filterTrainer: { type: String }
  };

  constructor() {
    super();
    this.currentUser = null;
    this.schedule = [];
    this.workouts = [];
    this.trainers = [];
    this.userBookings = [];
    this.loading = true;
    this.selectedDate = new Date().toISOString().split('T')[0];
    this.filterWorkout = '';
    this.filterTrainer = '';
    
    this.loadData();
  }

  async loadData() {
    try {
      this.loading = true;
      
      // Загружаем все необходимые данные
      const [schedule, workouts, trainers] = await Promise.all([
        databaseService.getSchedule(),
        databaseService.getWorkouts(),
        databaseService.getTrainers()
      ]);
      
      this.schedule = schedule;
      this.workouts = workouts;
      this.trainers = trainers;
      
      // Загружаем записи пользователя, если он авторизован
      if (this.currentUser) {
        this.userBookings = await databaseService.getUserBookings(this.currentUser._id);
      }
      
    } catch (error) {
      console.error('Ошибка загрузки данных:', error);
      this.showNotification('Ошибка загрузки данных', 'error');
    } finally {
      this.loading = false;
    }
  }

  getWorkoutById(workoutId) {
    return this.workouts.find(w => w._id === workoutId);
  }

  getTrainerById(trainerId) {
    return this.trainers.find(t => t._id === trainerId);
  }

  isUserBooked(scheduleId) {
    return this.userBookings.some(booking => 
      booking.schedule_id === scheduleId && booking.status === 'active'
    );
  }

  getUserBooking(scheduleId) {
    return this.userBookings.find(booking => 
      booking.schedule_id === scheduleId && booking.status === 'active'
    );
  }

  canCancelBooking(scheduleItem) {
    const workoutDateTime = new Date(`${scheduleItem.date}T${scheduleItem.time}`);
    const now = new Date();
    const hourBeforeWorkout = new Date(workoutDateTime.getTime() - 60 * 60 * 1000);
    return now <= hourBeforeWorkout;
  }

  async handleBooking(scheduleItem) {
    if (!this.currentUser) {
      this.showNotification('Необходимо войти в систему для записи на тренировки', 'error');
      return;
    }

    try {
      const isBooked = this.isUserBooked(scheduleItem._id);
      
      if (isBooked) {
        // Отмена записи
        if (!this.canCancelBooking(scheduleItem)) {
          this.showNotification('Отмена возможна не позднее чем за час до начала тренировки', 'error');
          return;
        }
        
        const booking = this.getUserBooking(scheduleItem._id);
        await databaseService.cancelBooking(booking._id, this.currentUser._id);
        this.showNotification('Запись отменена', 'success');
      } else {
        // Новая запись
        if (scheduleItem.current_participants >= scheduleItem.max_participants) {
          this.showNotification('Нет свободных мест', 'error');
          return;
        }
        
        await databaseService.createBooking(this.currentUser._id, scheduleItem._id);
        this.showNotification('Вы успешно записались на тренировку', 'success');
      }
      
      // Обновляем данные
      await this.loadData();
      
    } catch (error) {
      console.error('Ошибка при записи/отмене:', error);
      this.showNotification(error.message || 'Произошла ошибка', 'error');
    }
  }

  showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    document.body.appendChild(notification);
    
    setTimeout(() => {
      notification.remove();
    }, 5000);
  }

  changeDate(direction) {
    const currentDate = new Date(this.selectedDate);
    currentDate.setDate(currentDate.getDate() + direction);
    this.selectedDate = currentDate.toISOString().split('T')[0];
  }

  formatDate(dateString) {
    const date = new Date(dateString);
    const options = { 
      weekday: 'long', 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    };
    return date.toLocaleDateString('ru-RU', options);
  }

  formatTime(timeString) {
    return timeString.slice(0, 5);
  }

  getDifficultyText(difficulty) {
    const map = {
      'beginner': 'Начинающий',
      'intermediate': 'Средний',
      'advanced': 'Продвинутый'
    };
    return map[difficulty] || difficulty;
  }

  getFilteredSchedule() {
    let filtered = this.schedule;
    
    // Фильтр по дате
    filtered = filtered.filter(item => item.date === this.selectedDate);
    
    // Фильтр по типу тренировки
    if (this.filterWorkout) {
      filtered = filtered.filter(item => item.workout_id === this.filterWorkout);
    }
    
    // Фильтр по тренеру
    if (this.filterTrainer) {
      filtered = filtered.filter(item => item.trainer_id === this.filterTrainer);
    }
    
    return filtered;
  }

  render() {
    if (this.loading) {
      return html`
        <div class="schedule-container">
          <div class="loading-state">
            <div class="spinner"></div>
            <p>Загрузка расписания...</p>
          </div>
        </div>
      `;
    }

    const filteredSchedule = this.getFilteredSchedule();

    return html`
      <div class="schedule-container">
        <div class="schedule-header">
          <h1 class="schedule-title">Расписание тренировок</h1>
          <p class="schedule-subtitle">
            Выберите подходящую тренировку и запишитесь онлайн. 
            Отмена возможна не позднее чем за час до начала занятия.
          </p>
        </div>

        <div class="schedule-filters">
          <div class="filter-group">
            <label class="filter-label">Тип тренировки</label>
            <select class="filter-select" .value=${this.filterWorkout} 
                    @change=${(e) => this.filterWorkout = e.target.value}>
              <option value="">Все тренировки</option>
              ${this.workouts.map(workout => html`
                <option value=${workout._id}>${workout.name}</option>
              `)}
            </select>
          </div>

          <div class="filter-group">
            <label class="filter-label">Тренер</label>
            <select class="filter-select" .value=${this.filterTrainer} 
                    @change=${(e) => this.filterTrainer = e.target.value}>
              <option value="">Все тренеры</option>
              ${this.trainers.map(trainer => html`
                <option value=${trainer._id}>${trainer.name}</option>
              `)}
            </select>
          </div>

          <div class="date-navigation">
            <button class="date-nav-btn" @click=${() => this.changeDate(-1)}>
              ←
            </button>
            <div class="current-date">
              ${this.formatDate(this.selectedDate)}
            </div>
            <button class="date-nav-btn" @click=${() => this.changeDate(1)}>
              →
            </button>
          </div>
        </div>

        <div class="schedule-grid">
          ${filteredSchedule.length > 0 ? html`
            <div class="day-section">
              <div class="day-header">
                <div class="day-title">${this.formatDate(this.selectedDate)}</div>
                <div class="day-date">${filteredSchedule.length} тренировок</div>
              </div>
              <div class="workouts-list">
                ${filteredSchedule.map(scheduleItem => {
                  const workout = this.getWorkoutById(scheduleItem.workout_id);
                  const trainer = this.getTrainerById(scheduleItem.trainer_id);
                  const isBooked = this.isUserBooked(scheduleItem._id);
                  const isFull = scheduleItem.current_participants >= scheduleItem.max_participants;
                  const canCancel = isBooked && this.canCancelBooking(scheduleItem);
                  
                  return html`
                    <div class="workout-card">
                      <div class="workout-header">
                        <div class="workout-info">
                          <h3>${workout?.name || 'Неизвестная тренировка'}</h3>
                          <div class="workout-time">
                            🕐 ${this.formatTime(scheduleItem.time)} - ${this.formatTime(
                              new Date(new Date(`2000-01-01T${scheduleItem.time}`).getTime() + scheduleItem.duration * 60000)
                                .toTimeString().slice(0, 5)
                            )}
                          </div>
                        </div>
                        <div class="workout-status">
                          <div class="participants-info">
                            <span class="participants-count">${scheduleItem.current_participants}</span>
                            /${scheduleItem.max_participants} мест
                          </div>
                        </div>
                      </div>
                      
                      <div class="workout-details">
                        <div class="workout-meta">
                          <div class="meta-item">
                            👨‍🏫 ${trainer?.name || 'Неизвестный тренер'}
                          </div>
                          <div class="meta-item">
                            ⏱️ ${scheduleItem.duration} мин
                          </div>
                          <div class="meta-item">
                            <span class="difficulty-badge difficulty-${workout?.difficulty}">
                              ${this.getDifficultyText(workout?.difficulty)}
                            </span>
                          </div>
                        </div>
                        
                        ${this.currentUser ? html`
                          <button 
                            class="book-btn ${isBooked ? 'booked' : ''} ${isFull && !isBooked ? 'full' : ''}"
                            ?disabled=${isFull && !isBooked}
                            @click=${() => this.handleBooking(scheduleItem)}
                          >
                            ${isBooked 
                              ? (canCancel ? 'Отменить запись' : 'Записан') 
                              : (isFull ? 'Нет мест' : 'Записаться')
                            }
                          </button>
                        ` : html`
                          <a href="/login" class="book-btn">
                            Войти для записи
                          </a>
                        `}
                      </div>
                    </div>
                  `;
                })}
              </div>
            </div>
          ` : html`
            <div class="empty-state">
              <div class="empty-state-icon">📅</div>
              <h3>Нет тренировок на выбранную дату</h3>
              <p>Попробуйте выбрать другую дату или изменить фильтры</p>
            </div>
          `}
        </div>
      </div>
    `;
  }
}

customElements.define('schedule-page', SchedulePage);
