// Сервис для работы с базой данных (эмуляция CouchDB API)
import PouchDB from '/node_modules/pouchdb/dist/pouchdb.js';

class DatabaseService {
  constructor() {
    // Локальные базы данных PouchDB
    this.users = new PouchDB('users');
    this.trainers = new PouchDB('trainers');
    this.workouts = new PouchDB('workouts');
    this.schedule = new PouchDB('schedule');
    this.bookings = new PouchDB('bookings');
    this.content = new PouchDB('content');
    this.slider = new PouchDB('slider');
    
    // Эмуляция удаленного CouchDB сервера
    this.remoteUrl = 'http://localhost:5984';
    this.isOnline = false;
    
    this.initializeData();
  }

  async initializeData() {
    try {
      // Проверяем, есть ли уже данные
      const usersInfo = await this.users.info();
      if (usersInfo.doc_count === 0) {
        await this.seedInitialData();
      }
    } catch (error) {
      console.log('Инициализация данных:', error);
      await this.seedInitialData();
    }
  }

  async seedInitialData() {
    // Создание начальных данных
    
    // Тренеры
    const trainers = [
      {
        _id: 'trainer_1',
        type: 'trainer',
        name: 'Анна Петрова',
        specialization: ['йога', 'пилатес'],
        bio: 'Сертифицированный инструктор йоги с 8-летним опытом. Специализируется на хатха-йоге и пилатесе.',
        photo: 'https://via.placeholder.com/300x300/2563eb/ffffff?text=Анна+Петрова',
        experience: '8 лет'
      },
      {
        _id: 'trainer_2',
        type: 'trainer',
        name: 'Михаил Сидоров',
        specialization: ['функциональный тренинг', 'кроссфит'],
        bio: 'Мастер спорта по тяжелой атлетике. Специалист по функциональному тренингу и кроссфиту.',
        photo: 'https://via.placeholder.com/300x300/10b981/ffffff?text=Михаил+Сидоров',
        experience: '12 лет'
      },
      {
        _id: 'trainer_3',
        type: 'trainer',
        name: 'Елена Козлова',
        specialization: ['танцы', 'аэробика'],
        bio: 'Хореограф и фитнес-инструктор. Ведет занятия по современным танцам и аэробике.',
        photo: 'https://via.placeholder.com/300x300/f59e0b/ffffff?text=Елена+Козлова',
        experience: '6 лет'
      }
    ];

    // Типы тренировок
    const workouts = [
      {
        _id: 'workout_1',
        type: 'workout',
        name: 'Хатха-йога',
        description: 'Классическая йога для начинающих и продолжающих. Развитие гибкости, силы и баланса.',
        duration: 90,
        max_participants: 12,
        difficulty: 'beginner'
      },
      {
        _id: 'workout_2',
        type: 'workout',
        name: 'Функциональный тренинг',
        description: 'Интенсивная тренировка с использованием собственного веса и функциональных движений.',
        duration: 60,
        max_participants: 8,
        difficulty: 'intermediate'
      },
      {
        _id: 'workout_3',
        type: 'workout',
        name: 'Танцевальная аэробика',
        description: 'Энергичная тренировка под музыку с элементами современных танцев.',
        duration: 60,
        max_participants: 15,
        difficulty: 'beginner'
      },
      {
        _id: 'workout_4',
        type: 'workout',
        name: 'Пилатес',
        description: 'Система упражнений для укрепления мышц кора, улучшения осанки и гибкости.',
        duration: 60,
        max_participants: 10,
        difficulty: 'beginner'
      }
    ];

    // Расписание на неделю
    const schedule = [];
    const today = new Date();
    for (let i = 0; i < 7; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() + i);
      const dateStr = date.toISOString().split('T')[0];
      
      // Утренние занятия
      schedule.push({
        _id: `schedule_${i}_morning`,
        type: 'schedule',
        workout_id: workouts[i % workouts.length]._id,
        trainer_id: trainers[i % trainers.length]._id,
        date: dateStr,
        time: '09:00',
        duration: workouts[i % workouts.length].duration,
        max_participants: workouts[i % workouts.length].max_participants,
        current_participants: Math.floor(Math.random() * 5)
      });
      
      // Вечерние занятия
      schedule.push({
        _id: `schedule_${i}_evening`,
        type: 'schedule',
        workout_id: workouts[(i + 1) % workouts.length]._id,
        trainer_id: trainers[(i + 1) % trainers.length]._id,
        date: dateStr,
        time: '19:00',
        duration: workouts[(i + 1) % workouts.length].duration,
        max_participants: workouts[(i + 1) % workouts.length].max_participants,
        current_participants: Math.floor(Math.random() * 8)
      });
    }

    // Контент страниц
    const content = [
      {
        _id: 'content_home',
        type: 'content',
        page: 'home',
        title: 'Добро пожаловать в FitStudio',
        subtitle: 'Ваш путь к здоровью и красоте',
        description: 'Современная фитнес-студия с профессиональными тренерами и разнообразными программами тренировок.',
        updated_at: new Date().toISOString()
      }
    ];

    // Слайды для главной страницы
    const slider = [
      {
        _id: 'slide_1',
        type: 'slide',
        title: 'Йога и медитация',
        subtitle: 'Найдите гармонию тела и души',
        image: 'https://via.placeholder.com/800x400/2563eb/ffffff?text=Йога+и+медитация',
        order: 1,
        active: true
      },
      {
        _id: 'slide_2',
        type: 'slide',
        title: 'Функциональный тренинг',
        subtitle: 'Развивайте силу и выносливость',
        image: 'https://via.placeholder.com/800x400/10b981/ffffff?text=Функциональный+тренинг',
        order: 2,
        active: true
      }
    ];

    // Админ пользователь
    const adminUser = {
      _id: 'user_admin',
      type: 'user',
      email: '<EMAIL>',
      name: 'Администратор',
      phone: '+7 (495) 123-45-67',
      password_hash: '$2a$10$N9qo8uLOickgx2ZMRZoMye.IjPeOXANBjH6dUeAMgMxudsKBC1o16', // password
      role: 'admin',
      created_at: new Date().toISOString()
    };

    // Сохранение данных
    try {
      await this.trainers.bulkDocs(trainers);
      await this.workouts.bulkDocs(workouts);
      await this.schedule.bulkDocs(schedule);
      await this.content.bulkDocs(content);
      await this.slider.bulkDocs(slider);
      await this.users.put(adminUser);
      
      console.log('Начальные данные успешно созданы');
    } catch (error) {
      console.error('Ошибка при создании начальных данных:', error);
    }
  }

  // Методы для работы с пользователями
  async createUser(userData) {
    const user = {
      _id: `user_${Date.now()}`,
      type: 'user',
      ...userData,
      role: 'user',
      created_at: new Date().toISOString()
    };
    return await this.users.put(user);
  }

  async getUserByEmail(email) {
    try {
      const result = await this.users.find({
        selector: { email: email, type: 'user' }
      });
      return result.docs[0] || null;
    } catch (error) {
      return null;
    }
  }

  // Методы для работы с расписанием
  async getSchedule(date = null) {
    try {
      let selector = { type: 'schedule' };
      if (date) {
        selector.date = date;
      }
      
      const result = await this.schedule.find({
        selector: selector,
        sort: [{ date: 'asc' }, { time: 'asc' }]
      });
      
      return result.docs;
    } catch (error) {
      console.error('Ошибка получения расписания:', error);
      return [];
    }
  }

  // Методы для работы с записями
  async createBooking(userId, scheduleId) {
    // Проверяем доступность места (эмуляция прямого запроса к CouchDB)
    const scheduleItem = await this.schedule.get(scheduleId);
    if (scheduleItem.current_participants >= scheduleItem.max_participants) {
      throw new Error('Нет свободных мест');
    }

    // Проверяем, не записан ли уже пользователь
    const existingBooking = await this.bookings.find({
      selector: {
        user_id: userId,
        schedule_id: scheduleId,
        status: 'active'
      }
    });

    if (existingBooking.docs.length > 0) {
      throw new Error('Вы уже записаны на эту тренировку');
    }

    // Создаем запись
    const booking = {
      _id: `booking_${Date.now()}`,
      type: 'booking',
      user_id: userId,
      schedule_id: scheduleId,
      status: 'active',
      booked_at: new Date().toISOString()
    };

    // Увеличиваем количество участников
    scheduleItem.current_participants += 1;
    await this.schedule.put(scheduleItem);

    return await this.bookings.put(booking);
  }

  async cancelBooking(bookingId, userId) {
    const booking = await this.bookings.get(bookingId);
    
    if (booking.user_id !== userId) {
      throw new Error('Нет прав для отмены этой записи');
    }

    // Проверяем время отмены (не позднее чем за час)
    const scheduleItem = await this.schedule.get(booking.schedule_id);
    const workoutDateTime = new Date(`${scheduleItem.date}T${scheduleItem.time}`);
    const now = new Date();
    const hourBeforeWorkout = new Date(workoutDateTime.getTime() - 60 * 60 * 1000);

    if (now > hourBeforeWorkout) {
      throw new Error('Отмена возможна не позднее чем за час до начала тренировки');
    }

    // Отменяем запись
    booking.status = 'cancelled';
    booking.cancelled_at = new Date().toISOString();

    // Уменьшаем количество участников
    scheduleItem.current_participants -= 1;
    await this.schedule.put(scheduleItem);

    return await this.bookings.put(booking);
  }

  async getUserBookings(userId) {
    try {
      const result = await this.bookings.find({
        selector: { user_id: userId, type: 'booking' },
        sort: [{ booked_at: 'desc' }]
      });
      return result.docs;
    } catch (error) {
      console.error('Ошибка получения записей пользователя:', error);
      return [];
    }
  }

  // Методы для работы с контентом (админ)
  async updateContent(pageId, contentData) {
    try {
      const doc = await this.content.get(`content_${pageId}`);
      const updatedDoc = {
        ...doc,
        ...contentData,
        updated_at: new Date().toISOString()
      };
      return await this.content.put(updatedDoc);
    } catch (error) {
      // Если документ не существует, создаем новый
      const newDoc = {
        _id: `content_${pageId}`,
        type: 'content',
        page: pageId,
        ...contentData,
        updated_at: new Date().toISOString()
      };
      return await this.content.put(newDoc);
    }
  }

  async getContent(pageId) {
    try {
      return await this.content.get(`content_${pageId}`);
    } catch (error) {
      return null;
    }
  }

  // Методы для работы со слайдером
  async getSlides() {
    try {
      const result = await this.slider.find({
        selector: { type: 'slide', active: true },
        sort: [{ order: 'asc' }]
      });
      return result.docs;
    } catch (error) {
      console.error('Ошибка получения слайдов:', error);
      return [];
    }
  }

  async updateSlide(slideId, slideData) {
    try {
      const doc = await this.slider.get(slideId);
      const updatedDoc = {
        ...doc,
        ...slideData,
        updated_at: new Date().toISOString()
      };
      return await this.slider.put(updatedDoc);
    } catch (error) {
      console.error('Ошибка обновления слайда:', error);
      throw error;
    }
  }

  // Получение данных для отображения
  async getTrainers() {
    try {
      const result = await this.trainers.find({
        selector: { type: 'trainer' }
      });
      return result.docs;
    } catch (error) {
      console.error('Ошибка получения тренеров:', error);
      return [];
    }
  }

  async getWorkouts() {
    try {
      const result = await this.workouts.find({
        selector: { type: 'workout' }
      });
      return result.docs;
    } catch (error) {
      console.error('Ошибка получения тренировок:', error);
      return [];
    }
  }

  async getTrainer(trainerId) {
    try {
      return await this.trainers.get(trainerId);
    } catch (error) {
      return null;
    }
  }

  async getWorkout(workoutId) {
    try {
      return await this.workouts.get(workoutId);
    } catch (error) {
      return null;
    }
  }
}

// Создаем единственный экземпляр сервиса
export const databaseService = new DatabaseService();
