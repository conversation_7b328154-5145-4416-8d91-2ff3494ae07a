import { LitElement, html, css } from '/node_modules/lit/index.js';
import { databaseService } from './database.js';

class HomePage extends LitElement {
  static styles = css`
    :host {
      display: block;
    }

    .hero {
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      color: white;
      padding: var(--spacing-20) 0;
      text-align: center;
      position: relative;
      overflow: hidden;
    }

    .hero::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(45deg, rgba(37, 99, 235, 0.1), rgba(16, 185, 129, 0.1));
      opacity: 0.2;
      z-index: 0;
    }

    .hero-content {
      position: relative;
      z-index: 1;
      max-width: var(--container-lg);
      margin: 0 auto;
      padding: 0 var(--spacing-4);
    }

    .hero h1 {
      font-size: var(--font-size-4xl);
      font-weight: 700;
      margin-bottom: var(--spacing-6);
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .hero p {
      font-size: var(--font-size-xl);
      margin-bottom: var(--spacing-8);
      opacity: 0.9;
    }

    .hero-buttons {
      display: flex;
      gap: var(--spacing-4);
      justify-content: center;
      flex-wrap: wrap;
    }

    .hero-buttons .btn {
      padding: var(--spacing-4) var(--spacing-8);
      font-size: var(--font-size-lg);
      font-weight: 600;
    }

    .btn-white {
      background-color: white;
      color: var(--primary-color);
      border: 2px solid white;
    }

    .btn-white:hover {
      background-color: transparent;
      color: white;
    }

    .slider {
      margin: var(--spacing-16) 0;
    }

    .slider-container {
      max-width: var(--container-xl);
      margin: 0 auto;
      padding: 0 var(--spacing-4);
    }

    .slider-title {
      text-align: center;
      margin-bottom: var(--spacing-8);
    }

    .slides {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: var(--spacing-6);
    }

    .slide {
      background: white;
      border-radius: var(--radius-xl);
      overflow: hidden;
      box-shadow: var(--shadow-lg);
      transition: transform var(--transition-normal);
    }

    .slide:hover {
      transform: translateY(-5px);
    }

    .slide-image {
      width: 100%;
      height: 200px;
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: var(--font-size-4xl);
    }

    .slide-content {
      padding: var(--spacing-6);
    }

    .slide h3 {
      font-size: var(--font-size-xl);
      margin-bottom: var(--spacing-3);
      color: var(--gray-800);
    }

    .slide p {
      color: var(--gray-600);
      margin-bottom: 0;
    }

    .features {
      background-color: white;
      padding: var(--spacing-16) 0;
    }

    .features-container {
      max-width: var(--container-xl);
      margin: 0 auto;
      padding: 0 var(--spacing-4);
    }

    .features-title {
      text-align: center;
      margin-bottom: var(--spacing-12);
    }

    .features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: var(--spacing-8);
    }

    .feature {
      text-align: center;
      padding: var(--spacing-6);
    }

    .feature-icon {
      width: 80px;
      height: 80px;
      margin: 0 auto var(--spacing-4);
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: var(--font-size-2xl);
    }

    .feature h3 {
      font-size: var(--font-size-xl);
      margin-bottom: var(--spacing-3);
      color: var(--gray-800);
    }

    .feature p {
      color: var(--gray-600);
      margin-bottom: 0;
    }

    .cta {
      background: linear-gradient(135deg, var(--gray-800), var(--gray-700));
      color: white;
      padding: var(--spacing-16) 0;
      text-align: center;
    }

    .cta-container {
      max-width: var(--container-lg);
      margin: 0 auto;
      padding: 0 var(--spacing-4);
    }

    .cta h2 {
      font-size: var(--font-size-3xl);
      margin-bottom: var(--spacing-4);
    }

    .cta p {
      font-size: var(--font-size-lg);
      margin-bottom: var(--spacing-8);
      opacity: 0.9;
    }

    @media (max-width: 768px) {
      .hero h1 {
        font-size: var(--font-size-3xl);
      }

      .hero p {
        font-size: var(--font-size-lg);
      }

      .hero-buttons {
        flex-direction: column;
        align-items: center;
      }

      .hero-buttons .btn {
        width: 100%;
        max-width: 300px;
      }

      .slides {
        grid-template-columns: 1fr;
      }

      .features-grid {
        grid-template-columns: 1fr;
      }
    }
  `;

  static properties = {
    currentUser: { type: Object },
    slides: { type: Array },
    content: { type: Object }
  };

  constructor() {
    super();
    this.currentUser = null;
    this.slides = [];
    this.content = null;
    this.loadData();
  }

  async loadData() {
    try {
      // Загружаем слайды
      this.slides = await databaseService.getSlides();
      
      // Загружаем контент главной страницы
      this.content = await databaseService.getContent('home');
      
      this.requestUpdate();
    } catch (error) {
      console.error('Ошибка загрузки данных:', error);
    }
  }

  render() {
    return html`
      <div class="hero">
        <div class="hero-content">
          <h1>${this.content?.title || 'Добро пожаловать в FitStudio'}</h1>
          <p>${this.content?.subtitle || 'Ваш путь к здоровью и красоте'}</p>
          <div class="hero-buttons">
            ${this.currentUser ? html`
              <a href="/schedule" class="btn btn-white">Записаться на тренировку</a>
            ` : html`
              <a href="/register" class="btn btn-white">Начать заниматься</a>
              <a href="/schedule" class="btn btn-outline" style="border-color: white; color: white;">Посмотреть расписание</a>
            `}
          </div>
        </div>
      </div>

      ${this.slides.length > 0 ? html`
        <section class="slider">
          <div class="slider-container">
            <h2 class="slider-title">Наши программы</h2>
            <div class="slides">
              ${this.slides.map(slide => html`
                <div class="slide">
                  <div class="slide-image">
                    🏃‍♀️
                  </div>
                  <div class="slide-content">
                    <h3>${slide.title}</h3>
                    <p>${slide.subtitle}</p>
                  </div>
                </div>
              `)}
            </div>
          </div>
        </section>
      ` : ''}

      <section class="features">
        <div class="features-container">
          <h2 class="features-title">Почему выбирают нас</h2>
          <div class="features-grid">
            <div class="feature">
              <div class="feature-icon">👨‍🏫</div>
              <h3>Профессиональные тренеры</h3>
              <p>Сертифицированные инструкторы с многолетним опытом помогут достичь ваших целей</p>
            </div>
            <div class="feature">
              <div class="feature-icon">🏋️‍♀️</div>
              <h3>Современное оборудование</h3>
              <p>Новейшее фитнес-оборудование от ведущих мировых производителей</p>
            </div>
            <div class="feature">
              <div class="feature-icon">📅</div>
              <h3>Удобное расписание</h3>
              <p>Гибкое расписание занятий, которое подойдет для любого образа жизни</p>
            </div>
            <div class="feature">
              <div class="feature-icon">🎯</div>
              <h3>Индивидуальный подход</h3>
              <p>Персональные программы тренировок с учетом ваших особенностей и целей</p>
            </div>
          </div>
        </div>
      </section>

      <section class="cta">
        <div class="cta-container">
          <h2>Готовы начать свой путь к здоровью?</h2>
          <p>Присоединяйтесь к нашему сообществу и откройте для себя мир фитнеса</p>
          ${this.currentUser ? html`
            <a href="/schedule" class="btn btn-primary btn-lg">Записаться на тренировку</a>
          ` : html`
            <a href="/register" class="btn btn-primary btn-lg">Зарегистрироваться сейчас</a>
          `}
        </div>
      </section>
    `;
  }
}

customElements.define('home-page', HomePage);
